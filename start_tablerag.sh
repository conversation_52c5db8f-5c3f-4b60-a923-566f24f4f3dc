#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查并杀死端口占用进程
kill_port() {
    local port=$1
    local pid=$(lsof -t -i:$port)
    if [ ! -z "$pid" ]; then
        echo -e "${YELLOW}端口 $port 被占用，正在强制结束进程 (PID: $pid)...${NC}"
        kill -9 $pid
        sleep 1
    fi
}

echo -e "${GREEN}启动 TableRAG Enhanced 服务...${NC}"

# 强制结束可能占用端口的进程
echo -e "${YELLOW}检查并清理端口占用...${NC}"
kill_port 3000  # 前端React端口
kill_port 8000  # 后端FastAPI端口

# 启动后端服务
echo -e "${GREEN}启动后端服务...${NC}"
cd backend
python main.py &
BACKEND_PID=$!
cd ..

# 等待后端服务启动
echo -e "${YELLOW}等待后端服务启动...${NC}"
sleep 3

# 检查后端服务是否成功启动
if ! curl -s http://localhost:8000/health > /dev/null; then
    echo -e "${RED}后端服务启动失败！${NC}"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务
echo -e "${GREEN}启动前端服务...${NC}"
cd analyzer
npm start &
FRONTEND_PID=$!
cd ..

# 等待前端服务启动
echo -e "${YELLOW}等待前端服务启动...${NC}"
sleep 5

# 检查前端服务是否成功启动
if ! curl -s http://localhost:3000 > /dev/null; then
    echo -e "${RED}前端服务启动失败！${NC}"
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 1
fi

echo -e "${GREEN}所有服务启动成功！${NC}"
echo -e "${YELLOW}提示：${NC}"
echo -e "1. 后端服务运行在 http://localhost:8000"
echo -e "2. 前端服务运行在 http://localhost:3000"
echo -e "3. 使用 ${RED}Ctrl+C${NC} 停止所有服务"

# 等待用户中断
wait
