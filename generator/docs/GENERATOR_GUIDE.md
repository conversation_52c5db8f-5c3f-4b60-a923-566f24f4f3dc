# Generator 数据生成器开发指南

TableRAG Enhanced 数据生成器模块的详细开发和使用指南。

## 功能概览

数据生成器负责创建多样化的表格数据，用于训练和测试表格解析模型。支持：

- **LLM驱动生成**: 使用大语言模型生成真实的表格内容
- **图片生成**: 将表格数据转换为图片格式
- **复杂表格**: 支持合并单元格、多层表头等复杂结构
- **批量处理**: 支持大规模数据集的自动化生成
- **质量控制**: 内置数据质量检查和过滤机制

## 项目结构

```
generator/
├── src/                    # 源码目录
│   ├── image_gen.py       # 图片生成模块
│   ├── complex_loop.py    # 复杂表格生成循环
│   ├── loop.py            # 基础生成循环
│   ├── llm_train_data_gen.py  # LLM训练数据生成
│   ├── table_generator.py # 表格生成核心
│   ├── content_generator.py  # 内容生成器
│   └── utils/             # 工具函数
├── data/                  # 生成数据存储
├── templates/             # 表格模板
├── config/                # 配置文件
├── tests/                 # 测试文件
└── docs/                  # 文档
```

## 核心模块

### 1. 表格生成器 (table_generator.py)
```python
import json
import random
from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class TableStructure:
    """表格结构定义"""
    rows: int
    cols: int
    merged_cells: List[Dict] = None
    headers: List[str] = None
    
class TableGenerator:
    """表格生成器核心类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.llm_client = self._init_llm_client()
    
    def generate_table(self, topic: str = None, complexity: str = "simple") -> Dict:
        """
        生成表格数据
        
        Args:
            topic: 表格主题
            complexity: 复杂度 (simple, medium, complex)
            
        Returns:
            Dict: 表格数据
        """
        # 生成表格结构
        structure = self._generate_structure(complexity)
        
        # 生成表格内容
        content = self._generate_content(structure, topic)
        
        # 生成元数据
        metadata = self._generate_metadata(structure, topic, complexity)
        
        return {
            "structure": structure,
            "content": content,
            "metadata": metadata
        }
    
    def _generate_structure(self, complexity: str) -> TableStructure:
        """生成表格结构"""
        if complexity == "simple":
            rows = random.randint(3, 6)
            cols = random.randint(3, 5)
            merged_cells = []
        elif complexity == "medium":
            rows = random.randint(4, 8)
            cols = random.randint(4, 6)
            merged_cells = self._generate_merged_cells(rows, cols, 0.1)
        else:  # complex
            rows = random.randint(6, 12)
            cols = random.randint(5, 8)
            merged_cells = self._generate_merged_cells(rows, cols, 0.2)
        
        return TableStructure(
            rows=rows,
            cols=cols,
            merged_cells=merged_cells,
            headers=self._generate_headers(cols)
        )
    
    def _generate_content(self, structure: TableStructure, topic: str) -> List[List[str]]:
        """生成表格内容"""
        if self.config.get("use_llm", True):
            return self._generate_content_with_llm(structure, topic)
        else:
            return self._generate_content_random(structure)
    
    def _generate_content_with_llm(self, structure: TableStructure, topic: str) -> List[List[str]]:
        """使用LLM生成内容"""
        prompt = self._build_generation_prompt(structure, topic)
        
        try:
            response = self.llm_client.generate(prompt)
            content = self._parse_llm_response(response, structure)
            return content
        except Exception as e:
            print(f"LLM生成失败，使用随机生成: {e}")
            return self._generate_content_random(structure)
    
    def _build_generation_prompt(self, structure: TableStructure, topic: str) -> str:
        """构建LLM生成提示"""
        prompt = f"""
        请生成一个关于"{topic}"的表格，要求：
        - 行数: {structure.rows}
        - 列数: {structure.cols}
        - 表头: {', '.join(structure.headers)}
        - 内容要真实、有意义
        - 返回JSON格式的二维数组
        
        示例格式:
        [
            ["表头1", "表头2", "表头3"],
            ["数据1", "数据2", "数据3"],
            ...
        ]
        """
        return prompt
```

### 2. 图片生成器 (image_gen.py)
```python
import os
from PIL import Image, ImageDraw, ImageFont
from typing import List, Dict, Tuple

class ImageGenerator:
    """表格图片生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.font_path = config.get("font_path", "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf")
        self.output_dir = config.get("output_dir", "generated_images")
        
    def generate_table_image(self, table_data: Dict, output_path: str) -> str:
        """
        生成表格图片
        
        Args:
            table_data: 表格数据
            output_path: 输出路径
            
        Returns:
            str: 生成的图片路径
        """
        content = table_data["content"]
        structure = table_data["structure"]
        
        # 计算图片尺寸
        img_width, img_height = self._calculate_image_size(content, structure)
        
        # 创建图片
        img = Image.new('RGB', (img_width, img_height), 'white')
        draw = ImageDraw.Draw(img)
        
        # 加载字体
        font = self._load_font()
        
        # 绘制表格
        self._draw_table(draw, content, structure, font, img_width, img_height)
        
        # 保存图片
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        img.save(output_path, 'JPEG', quality=95)
        
        return output_path
    
    def _calculate_image_size(self, content: List[List[str]], structure) -> Tuple[int, int]:
        """计算图片尺寸"""
        # 基础尺寸
        base_width = 800
        base_height = 600
        
        # 根据内容调整
        max_text_length = max(len(str(cell)) for row in content for cell in row)
        width = max(base_width, max_text_length * 10 + structure.cols * 20)
        height = max(base_height, structure.rows * 40 + 100)
        
        return width, height
    
    def _draw_table(self, draw, content, structure, font, img_width, img_height):
        """绘制表格"""
        # 计算单元格尺寸
        cell_width = (img_width - 40) // structure.cols
        cell_height = (img_height - 40) // structure.rows
        
        # 绘制网格线
        for i in range(structure.rows + 1):
            y = 20 + i * cell_height
            draw.line([(20, y), (img_width - 20, y)], fill='black', width=1)
        
        for j in range(structure.cols + 1):
            x = 20 + j * cell_width
            draw.line([(x, 20), (x, img_height - 20)], fill='black', width=1)
        
        # 绘制文本
        for i, row in enumerate(content):
            for j, cell in enumerate(row):
                x = 25 + j * cell_width
                y = 25 + i * cell_height
                
                # 处理合并单元格
                if self._is_merged_cell(i, j, structure.merged_cells):
                    continue
                
                # 绘制文本
                text = str(cell)[:20]  # 限制文本长度
                draw.text((x, y), text, fill='black', font=font)
    
    def _load_font(self):
        """加载字体"""
        try:
            return ImageFont.truetype(self.font_path, 14)
        except:
            return ImageFont.load_default()
    
    def _is_merged_cell(self, row: int, col: int, merged_cells: List[Dict]) -> bool:
        """检查是否为合并单元格"""
        for merge in merged_cells or []:
            if (merge["start_row"] <= row <= merge["end_row"] and 
                merge["start_col"] <= col <= merge["end_col"] and
                not (row == merge["start_row"] and col == merge["start_col"])):
                return True
        return False
```

### 3. 批量生成循环 (loop.py)
```python
import time
import json
import os
from datetime import datetime
from typing import Dict, List

class BatchGenerator:
    """批量生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.table_generator = TableGenerator(config)
        self.image_generator = ImageGenerator(config)
        self.output_dir = config.get("output_dir", "generated_data")
        
    def run_generation_loop(self, num_tables: int = 100, topics: List[str] = None):
        """
        运行生成循环
        
        Args:
            num_tables: 生成表格数量
            topics: 主题列表
        """
        print(f"开始批量生成 {num_tables} 个表格...")
        
        # 默认主题
        if not topics:
            topics = [
                "财务报表", "销售数据", "员工信息", "产品清单", "库存管理",
                "客户信息", "订单记录", "成绩单", "课程表", "项目进度"
            ]
        
        generated_count = 0
        failed_count = 0
        
        for i in range(num_tables):
            try:
                # 选择主题
                topic = topics[i % len(topics)]
                
                # 生成表格数据
                table_data = self.table_generator.generate_table(
                    topic=topic,
                    complexity=self._get_random_complexity()
                )
                
                # 生成图片
                image_path = os.path.join(
                    self.output_dir, 
                    f"table_{i:04d}_{topic}.jpg"
                )
                self.image_generator.generate_table_image(table_data, image_path)
                
                # 保存元数据
                metadata_path = os.path.join(
                    self.output_dir,
                    f"table_{i:04d}_{topic}.json"
                )
                self._save_metadata(table_data, metadata_path)
                
                generated_count += 1
                
                if (i + 1) % 10 == 0:
                    print(f"已生成 {i + 1}/{num_tables} 个表格")
                
                # 避免过快请求
                time.sleep(self.config.get("generation_delay", 1))
                
            except Exception as e:
                print(f"生成第 {i} 个表格失败: {e}")
                failed_count += 1
                continue
        
        print(f"批量生成完成: 成功 {generated_count}, 失败 {failed_count}")
        
        # 生成汇总报告
        self._generate_summary_report(generated_count, failed_count, topics)
    
    def _get_random_complexity(self) -> str:
        """随机选择复杂度"""
        import random
        complexities = ["simple", "medium", "complex"]
        weights = [0.5, 0.3, 0.2]  # 简单表格占比更高
        return random.choices(complexities, weights=weights)[0]
    
    def _save_metadata(self, table_data: Dict, metadata_path: str):
        """保存元数据"""
        metadata = {
            "generated_at": datetime.now().isoformat(),
            "structure": {
                "rows": table_data["structure"].rows,
                "cols": table_data["structure"].cols,
                "merged_cells": table_data["structure"].merged_cells,
                "headers": table_data["structure"].headers
            },
            "content": table_data["content"],
            "metadata": table_data["metadata"]
        }
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    def _generate_summary_report(self, success_count: int, failed_count: int, topics: List[str]):
        """生成汇总报告"""
        report = {
            "generation_summary": {
                "total_requested": success_count + failed_count,
                "successful": success_count,
                "failed": failed_count,
                "success_rate": success_count / (success_count + failed_count) if (success_count + failed_count) > 0 else 0
            },
            "topics_used": topics,
            "generated_at": datetime.now().isoformat(),
            "output_directory": self.output_dir
        }
        
        report_path = os.path.join(self.output_dir, "generation_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"汇总报告已保存: {report_path}")
```

## 配置管理

### 生成器配置 (config/generator_config.json)
```json
{
  "llm_config": {
    "use_llm": true,
    "endpoint": "http://localhost:8000/v1/chat/completions",
    "model": "gpt-3.5-turbo",
    "max_tokens": 2000,
    "temperature": 0.7
  },
  
  "image_config": {
    "output_format": "JPEG",
    "quality": 95,
    "font_path": "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
    "base_width": 800,
    "base_height": 600
  },
  
  "generation_config": {
    "batch_size": 10,
    "generation_delay": 1,
    "max_retries": 3,
    "output_dir": "generated_data"
  },
  
  "table_config": {
    "min_rows": 3,
    "max_rows": 12,
    "min_cols": 3,
    "max_cols": 8,
    "merge_probability": 0.15
  },
  
  "topics": [
    "财务报表", "销售数据", "员工信息", "产品清单",
    "库存管理", "客户信息", "订单记录", "成绩单",
    "课程表", "项目进度", "市场分析", "预算计划"
  ]
}
```

## 使用示例

### 1. 基础使用
```python
from generator.src.table_generator import TableGenerator
from generator.src.image_gen import ImageGenerator

# 初始化配置
config = {
    "use_llm": True,
    "llm_endpoint": "http://localhost:8000/v1/chat/completions",
    "output_dir": "my_generated_data"
}

# 创建生成器
table_gen = TableGenerator(config)
image_gen = ImageGenerator(config)

# 生成单个表格
table_data = table_gen.generate_table(
    topic="销售数据",
    complexity="medium"
)

# 生成图片
image_path = image_gen.generate_table_image(
    table_data,
    "output/sales_table.jpg"
)

print(f"表格图片已生成: {image_path}")
```

### 2. 批量生成
```python
from generator.src.loop import BatchGenerator

# 配置
config = {
    "use_llm": True,
    "generation_delay": 2,
    "output_dir": "batch_generated"
}

# 创建批量生成器
batch_gen = BatchGenerator(config)

# 运行批量生成
batch_gen.run_generation_loop(
    num_tables=50,
    topics=["财务", "销售", "人事", "库存"]
)
```

### 3. 启动生成服务
```bash
# 启动生成器服务
cd generator
./start_gen.sh

# 检查生成日志
tail -f loop.log
```

## 质量控制

### 1. 数据验证
```python
def validate_table_data(table_data: Dict) -> bool:
    """验证表格数据质量"""
    content = table_data.get("content", [])
    structure = table_data.get("structure")
    
    # 检查基本结构
    if not content or not structure:
        return False
    
    # 检查行列数一致性
    if len(content) != structure.rows:
        return False
    
    for row in content:
        if len(row) != structure.cols:
            return False
    
    # 检查内容质量
    empty_cells = sum(1 for row in content for cell in row if not str(cell).strip())
    total_cells = structure.rows * structure.cols
    
    if empty_cells / total_cells > 0.3:  # 空单元格超过30%
        return False
    
    return True
```

### 2. 图片质量检查
```python
def validate_image_quality(image_path: str) -> bool:
    """验证图片质量"""
    try:
        from PIL import Image
        
        img = Image.open(image_path)
        
        # 检查尺寸
        if img.width < 400 or img.height < 300:
            return False
        
        # 检查文件大小
        file_size = os.path.getsize(image_path)
        if file_size < 10000:  # 小于10KB可能有问题
            return False
        
        return True
    except Exception:
        return False
```

## 性能优化

### 1. 并行生成
```python
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing

class ParallelGenerator:
    """并行生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.max_workers = config.get("max_workers", multiprocessing.cpu_count())
    
    def generate_parallel(self, num_tables: int, topics: List[str]):
        """并行生成表格"""
        tasks = [
            (i, topics[i % len(topics)]) 
            for i in range(num_tables)
        ]
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = [
                executor.submit(self._generate_single_table, task_id, topic)
                for task_id, topic in tasks
            ]
            
            results = []
            for future in futures:
                try:
                    result = future.result(timeout=60)
                    results.append(result)
                except Exception as e:
                    print(f"生成任务失败: {e}")
        
        return results
```

### 2. 缓存机制
```python
import hashlib
import pickle

class CachedGenerator:
    """带缓存的生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.cache_dir = config.get("cache_dir", "cache")
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def generate_with_cache(self, topic: str, complexity: str) -> Dict:
        """带缓存的生成"""
        # 生成缓存键
        cache_key = self._get_cache_key(topic, complexity)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.pkl")
        
        # 检查缓存
        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                return pickle.load(f)
        
        # 生成新数据
        table_data = self.table_generator.generate_table(topic, complexity)
        
        # 保存缓存
        with open(cache_file, 'wb') as f:
            pickle.dump(table_data, f)
        
        return table_data
    
    def _get_cache_key(self, topic: str, complexity: str) -> str:
        """生成缓存键"""
        key_string = f"{topic}_{complexity}_{self.config.get('version', '1.0')}"
        return hashlib.md5(key_string.encode()).hexdigest()
```

## 最佳实践

1. **主题多样化**: 使用丰富的主题确保数据多样性
2. **质量控制**: 实施严格的数据验证和质量检查
3. **增量生成**: 支持增量生成避免重复工作
4. **错误恢复**: 实现健壮的错误处理和恢复机制
5. **资源管理**: 合理控制并发数量和资源使用
6. **版本管理**: 对生成的数据进行版本管理
7. **监控日志**: 详细记录生成过程和性能指标
