# Generator Tests

数据生成器测试套件，包含表格生成、图片生成和LLM训练数据生成测试。

## 测试文件说明

目前generator模块的测试文件较少，主要测试逻辑集成在生成脚本中。

## 运行测试

### 生成器功能测试
```bash
cd generator
# 启动生成器
./start_gen.sh

# 检查生成日志
tail -f loop.log
tail -f complex_agent.log
```

### 手动测试生成功能
```bash
# 测试简单表格生成
python src/image_gen.py

# 测试复杂表格生成
python src/complex_loop.py

# 测试LLM训练数据生成
python src/llm_train_data_gen.py
```

## 测试环境要求

### 依赖服务
- LLM服务 (用于生成表格内容)
- 图片生成服务
- 文件存储服务

### 环境变量
确保config.env中配置了：
- `LLM_SERVICE_TYPE` - LLM服务类型
- `CUSTOM_LLM_ENDPOINT` - LLM API端点
- `TABLE_DEFAULT_ROWS` - 默认表格行数
- `TABLE_DEFAULT_COLS` - 默认表格列数

### 存储空间
- 生成的数据需要足够的磁盘空间
- 图片文件可能较大
- 训练数据文件较多

## 生成器功能

### 表格数据生成
- 简单表格结构
- 复杂表格 (合并单元格)
- 多样化内容主题
- 不同尺寸规格

### 图片生成
- 表格转图片
- 多种样式风格
- 不同分辨率
- 质量控制

### 训练数据生成
- 短文本数据
- 长文本数据
- 结构化数据
- 标注数据

## 测试数据

### 输入数据
- JSON格式的表格定义
- 主题和内容模板
- 样式配置参数

### 输出数据
- 生成的图片文件
- JSON格式的元数据
- 训练用的文本数据

## 性能监控

### 生成速度
- 单个表格生成时间
- 批量生成吞吐量
- 资源使用情况

### 质量控制
- 生成内容的多样性
- 图片质量检查
- 数据格式验证

## 调试指南

### 生成失败问题
1. 检查LLM服务连接
2. 验证输入数据格式
3. 检查存储空间
4. 查看错误日志

### 质量问题
1. 调整生成参数
2. 检查模板配置
3. 验证样式设置
4. 测试不同主题

### 性能问题
1. 监控资源使用
2. 优化批处理大小
3. 调整并发数量
4. 检查网络延迟

## 扩展测试

### 添加新的测试用例
1. 在tests/目录创建测试文件
2. 使用pytest框架
3. 模拟各种输入场景
4. 验证输出质量

### 集成测试
1. 端到端生成流程
2. 与解析器的集成
3. 与标注系统的集成
4. 完整工作流测试

## 注意事项

1. 生成过程可能耗时较长
2. 需要稳定的网络连接
3. LLM服务可能有调用限制
4. 生成的数据需要定期清理
5. 某些功能需要GPU加速
