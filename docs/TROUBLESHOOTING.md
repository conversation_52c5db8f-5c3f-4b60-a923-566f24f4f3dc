# TableRAG Enhanced 故障排除指南

本文档提供常见问题的诊断和解决方案。

## 🚨 紧急问题快速诊断

### 系统完全无法访问
```bash
# 1. 检查所有服务状态
./scripts/check_services.sh

# 2. 快速重启所有服务
./start_tablerag.sh

# 3. 检查端口占用
lsof -i :3000 -i :8000
```

### 数据丢失或损坏
```bash
# 1. 立即停止所有写操作
sudo systemctl stop tablerag-backend

# 2. 检查数据库状态
mysql -h localhost -P 13306 -u aidocsuser -p -e "SHOW TABLES;"

# 3. 恢复最近备份
./scripts/restore_backup.sh latest
```

## 🔧 服务启动问题

### 后端服务无法启动

**症状**: `python main.py` 失败或服务立即退出

**诊断步骤**:
```bash
# 1. 检查Python环境
python3 --version
which python3

# 2. 检查依赖
pip list | grep -E "(fastapi|uvicorn|sqlalchemy)"

# 3. 检查配置文件
cat config.env | grep -E "(DATABASE_URL|BACKEND_PORT)"

# 4. 测试数据库连接
python -c "
import os
from sqlalchemy import create_engine
engine = create_engine(os.getenv('DATABASE_URL'))
print('Database connection:', engine.connect())
"
```

**常见解决方案**:
```bash
# 重新安装依赖
cd backend
pip install -r requirements.txt --force-reinstall

# 检查端口占用
sudo lsof -i :8000
sudo kill -9 $(lsof -t -i:8000)

# 重置数据库连接
sudo systemctl restart mysql
```

### 前端服务无法启动

**症状**: `npm start` 失败或页面无法加载

**诊断步骤**:
```bash
# 1. 检查Node.js环境
node --version
npm --version

# 2. 清理缓存
cd analyzer
rm -rf node_modules package-lock.json
npm cache clean --force

# 3. 重新安装依赖
npm install

# 4. 检查端口
lsof -i :3000
```

**常见解决方案**:
```bash
# 强制使用指定端口
export PORT=3000
npm start

# 修复权限问题
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) node_modules

# 降级Node.js版本 (如果需要)
nvm install 16
nvm use 16
```

## 🌐 网络连接问题

### SSH隧道连接失败

**症状**: 本地无法访问远程服务

**诊断步骤**:
```bash
# 1. 检查SSH连接
ssh -v user@remote-server

# 2. 测试端口转发
ssh -L 3000:localhost:3000 user@remote-server -N &
curl http://localhost:3000

# 3. 检查防火墙
sudo ufw status
```

**解决方案**:
```bash
# 重新建立隧道
pkill -f "ssh.*-L"
ssh -L 3000:localhost:3000 -L 8000:localhost:8000 user@remote-server

# 使用autossh保持连接
autossh -M 20000 -L 3000:localhost:3000 -L 8000:localhost:8000 user@remote-server
```

### API请求失败

**症状**: 前端无法获取数据，控制台显示网络错误

**诊断步骤**:
```bash
# 1. 测试后端API
curl http://localhost:8000/health
curl http://localhost:8000/api/datasets

# 2. 检查CORS配置
curl -H "Origin: http://localhost:3000" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS http://localhost:8000/api/datasets

# 3. 检查网络路由
traceroute localhost
```

**解决方案**:
```python
# 修复CORS配置 (backend/main.py)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 💾 数据库问题

### 连接失败

**症状**: `Can't connect to MySQL server`

**诊断步骤**:
```bash
# 1. 检查MySQL服务
sudo systemctl status mysql
sudo systemctl start mysql

# 2. 检查端口监听
sudo netstat -tlnp | grep 13306

# 3. 测试连接
mysql -h localhost -P 13306 -u aidocsuser -p

# 4. 检查用户权限
mysql -u root -p -e "SELECT User, Host FROM mysql.user WHERE User='aidocsuser';"
```

**解决方案**:
```sql
-- 重新创建用户
DROP USER IF EXISTS 'aidocsuser'@'localhost';
CREATE USER 'aidocsuser'@'localhost' IDENTIFIED BY 'aidocspass';
GRANT ALL PRIVILEGES ON aidocsdb.* TO 'aidocsuser'@'localhost';
FLUSH PRIVILEGES;
```

### 表不存在

**症状**: `Table 'aidocsdb.tablerag_annotations' doesn't exist`

**解决方案**:
```bash
# 重新初始化数据库
cd backend
python -c "
from database.database import init_database
init_database()
print('Database initialized successfully')
"
```

### 数据库性能问题

**症状**: 查询响应缓慢

**诊断步骤**:
```sql
-- 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
SHOW VARIABLES LIKE 'long_query_time';

-- 查看进程列表
SHOW PROCESSLIST;

-- 检查表状态
SHOW TABLE STATUS FROM aidocsdb;
```

**优化方案**:
```sql
-- 添加索引
CREATE INDEX idx_dataset_image ON tablerag_annotations(dataset_name, image_name);
CREATE INDEX idx_created_at ON tablerag_annotations(created_at);

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = 1073741824;  -- 1GB
```

## 📁 文件系统问题

### 磁盘空间不足

**症状**: `No space left on device`

**诊断步骤**:
```bash
# 1. 检查磁盘使用
df -h
du -sh /data/projects/kingsoft/personal/workspace/tablerag/enhance/*

# 2. 查找大文件
find . -type f -size +100M -exec ls -lh {} \;

# 3. 检查日志文件
ls -lah *.log
```

**解决方案**:
```bash
# 清理日志文件
find . -name "*.log" -mtime +7 -delete

# 清理临时文件
rm -rf /tmp/tablerag_*
rm -rf analyzer/node_modules/.cache

# 压缩旧数据
tar -czf old_data_$(date +%Y%m%d).tar.gz parse_results/old_*
rm -rf parse_results/old_*
```

### 权限问题

**症状**: `Permission denied`

**诊断步骤**:
```bash
# 检查文件权限
ls -la dataset/
ls -la parse_results/

# 检查进程用户
ps aux | grep -E "(python|node)"
```

**解决方案**:
```bash
# 修复权限
sudo chown -R $(whoami):$(whoami) .
chmod -R 755 dataset/ parse_results/ reports/

# 修复特定目录权限
sudo chown -R www-data:www-data analyzer/build/
```

## 🔍 解析器问题

### 解析器无响应

**症状**: 解析任务卡住或超时

**诊断步骤**:
```bash
# 1. 检查解析器进程
ps aux | grep -E "(kdc|monkey|llm)"

# 2. 检查网络连接
curl -I http://monkey-ocr-api.example.com
ping llm-api.example.com

# 3. 检查解析日志
tail -f parser/parse.log
```

**解决方案**:
```bash
# 重启解析器服务
sudo systemctl restart kdc-service
sudo systemctl restart monkey-ocr-local

# 调整超时设置
export MONKEY_OCR_TIMEOUT=600
export LLM_TIMEOUT=120

# 切换到串行模式调试
export PARSER_EXECUTION_MODE=sequential
```

### 解析结果异常

**症状**: 解析结果为空或格式错误

**诊断步骤**:
```bash
# 1. 检查输入图片
file dataset/kingsoft/images/test.jpg
identify dataset/kingsoft/images/test.jpg

# 2. 手动测试解析器
python parser/tests/test_single_parser.py kdc_markdown test.jpg

# 3. 检查解析结果格式
cat parse_results/kingsoft/latest_results.json | jq '.cases[0]'
```

**解决方案**:
```python
# 添加结果验证
def validate_parse_result(result):
    if not result.get("success"):
        return False
    if not result.get("content"):
        return False
    return True
```

## 📊 性能问题

### 内存使用过高

**症状**: 系统响应缓慢，内存占用高

**诊断步骤**:
```bash
# 1. 检查内存使用
free -h
ps aux --sort=-%mem | head -10

# 2. 检查进程内存
pmap -x $(pgrep python)

# 3. 监控内存变化
watch -n 1 'free -h'
```

**解决方案**:
```python
# 优化数据加载
import gc

def process_large_dataset(dataset):
    for batch in batch_iterator(dataset, batch_size=100):
        process_batch(batch)
        gc.collect()  # 强制垃圾回收

# 使用生成器
def load_cases_generator(dataset_name):
    for case_file in os.listdir(f"parse_results/{dataset_name}"):
        yield load_case(case_file)
```

### CPU使用率过高

**症状**: CPU持续高负载

**诊断步骤**:
```bash
# 1. 检查CPU使用
top -p $(pgrep python)
htop

# 2. 分析进程
strace -p $(pgrep python) -c

# 3. 检查并发设置
echo $PARSER_EXECUTION_MODE
echo $PARSER_ENABLED_LIST
```

**解决方案**:
```bash
# 降低并发数
export PARSER_EXECUTION_MODE=sequential

# 限制解析器数量
export PARSER_ENABLED_LIST="kdc_markdown,monkey_ocr"

# 调整进程优先级
nice -n 10 python main.py
```

## 🔐 安全问题

### 未授权访问

**症状**: 安全扫描发现漏洞

**解决方案**:
```python
# 添加API认证
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

@app.get("/api/protected")
async def protected_route(token: str = Depends(security)):
    if not validate_token(token):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )
```

### 数据泄露风险

**解决方案**:
```bash
# 加密敏感配置
echo "sensitive_data" | openssl enc -aes-256-cbc -base64

# 设置文件权限
chmod 600 config.env
chmod 700 dataset/

# 配置防火墙
sudo ufw deny from any to any port 13306
sudo ufw allow from 127.0.0.1 to any port 13306
```

## 🛠️ 调试工具

### 日志分析脚本
```bash
#!/bin/bash
# debug_logs.sh

echo "=== Backend Logs ==="
tail -50 backend/backend.log | grep -E "(ERROR|CRITICAL)"

echo "=== Frontend Logs ==="
tail -50 analyzer/frontend.log | grep -E "(error|Error)"

echo "=== Parser Logs ==="
tail -50 parser/parse.log | grep -E "(ERROR|Failed)"

echo "=== System Logs ==="
journalctl -u tablerag-backend --since "1 hour ago" | grep -E "(error|failed)"
```

### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

echo "=== Service Health Check ==="

# 检查后端
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ Backend: OK"
else
    echo "❌ Backend: FAILED"
fi

# 检查前端
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Frontend: OK"
else
    echo "❌ Frontend: FAILED"
fi

# 检查数据库
if mysql -h localhost -P 13306 -u aidocsuser -p'aidocspass' -e "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database: OK"
else
    echo "❌ Database: FAILED"
fi
```

### 性能监控脚本
```bash
#!/bin/bash
# monitor_performance.sh

while true; do
    echo "$(date): CPU=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}'), MEM=$(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}'), DISK=$(df / | tail -1 | awk '{print $5}')"
    sleep 60
done
```

## 📞 获取帮助

### 收集诊断信息
```bash
#!/bin/bash
# collect_debug_info.sh

echo "=== System Information ===" > debug_info.txt
uname -a >> debug_info.txt
cat /etc/os-release >> debug_info.txt

echo "=== Service Status ===" >> debug_info.txt
systemctl status tablerag-backend >> debug_info.txt
systemctl status mysql >> debug_info.txt

echo "=== Configuration ===" >> debug_info.txt
cat config.env | grep -v -E "(PASSWORD|SECRET|KEY)" >> debug_info.txt

echo "=== Recent Logs ===" >> debug_info.txt
tail -100 backend/backend.log >> debug_info.txt

echo "Debug information collected in debug_info.txt"
```

### 联系支持
1. **GitHub Issues**: 提交详细的问题报告
2. **邮件支持**: 发送debug_info.txt文件
3. **社区论坛**: 搜索类似问题或发布新问题

### 问题报告模板
```markdown
## 问题描述
简要描述遇到的问题

## 环境信息
- 操作系统: 
- Python版本: 
- Node.js版本: 
- MySQL版本: 

## 重现步骤
1. 
2. 
3. 

## 期望结果
描述期望的正常行为

## 实际结果
描述实际发生的情况

## 错误日志
```
粘贴相关的错误日志
```

## 已尝试的解决方案
列出已经尝试过的解决方法
```

记住：大多数问题都有解决方案，保持耐心并系统性地排查问题！
