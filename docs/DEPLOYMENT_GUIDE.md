# TableRAG Enhanced 部署指南

本文档详细说明如何在不同环境中部署TableRAG Enhanced系统。

## 部署架构

### 系统组件
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │    Database     │
│   (React)       │◄──►│   (FastAPI)     │◄──►│    (MySQL)      │
│   Port: 3000    │    │ Port: 8000      │    │   Port: 13306   │
└─────────────────┘    │ + Static Files  │    └─────────────────┘
                       └─────────────────┘
```

### 网络配置
- **前端服务**: React开发服务器或Nginx静态服务
- **后端服务**: FastAPI + Uvicorn (包含静态文件服务)
- **数据库**: MySQL 8.0+

## 环境要求

### 系统要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **内存**: 8GB+ (推荐16GB+)
- **存储**: 50GB+ 可用空间
- **网络**: 稳定的互联网连接

### 软件依赖
- **Python**: 3.8+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Git**: 2.0+

## 开发环境部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd tablerag-enhance
```

### 2. 环境配置
```bash
# 复制配置文件
cp config.env.example config.env

# 编辑配置文件
vim config.env
```

### 3. 数据库设置
```bash
# 安装MySQL
sudo apt update
sudo apt install mysql-server

# 创建数据库和用户
sudo mysql -u root -p
```

```sql
CREATE DATABASE aidocsdb CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'aidocsuser'@'localhost' IDENTIFIED BY 'aidocspass';
GRANT ALL PRIVILEGES ON aidocsdb.* TO 'aidocsuser'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 4. 后端部署
```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 初始化数据库
python -c "from database.database import init_database; init_database()"

# 启动服务
python main.py
```

### 5. 前端部署
```bash
cd analyzer

# 安装依赖
npm install

# 启动开发服务器
npm start
```

### 6. 验证部署
```bash
# 检查后端服务
curl http://localhost:8000/health

# 检查前端服务
curl http://localhost:3000

# 检查数据库连接
mysql -h localhost -P 13306 -u aidocsuser -p aidocsdb -e "SHOW TABLES;"
```

## 生产环境部署

### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y git python3 python3-pip nodejs npm mysql-server nginx

# 配置防火墙
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. 数据库配置
```bash
# 安全配置MySQL
sudo mysql_secure_installation

# 配置MySQL
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
bind-address = 127.0.0.1
port = 13306
max_connections = 200
innodb_buffer_pool_size = 2G
```

```bash
# 重启MySQL
sudo systemctl restart mysql
```

### 3. 后端生产部署
```bash
# 创建应用用户
sudo useradd -m -s /bin/bash tablerag
sudo su - tablerag

# 克隆代码
git clone <repository-url> /home/<USER>/app
cd /home/<USER>/app

# 设置Python环境
python3 -m venv venv
source venv/bin/activate
pip install -r backend/requirements.txt

# 配置环境变量
cp config.env.example config.env
# 编辑生产配置...

# 创建systemd服务
sudo vim /etc/systemd/system/tablerag-backend.service
```

```ini
[Unit]
Description=TableRAG Backend Service
After=network.target mysql.service

[Service]
Type=simple
User=tablerag
WorkingDirectory=/home/<USER>/app/backend
Environment=PATH=/home/<USER>/app/venv/bin
ExecStart=/home/<USER>/app/venv/bin/python main.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

```bash
# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable tablerag-backend
sudo systemctl start tablerag-backend
```

### 4. 前端生产部署
```bash
cd /home/<USER>/app/analyzer

# 构建生产版本
npm install
npm run build

# 配置Nginx
sudo vim /etc/nginx/sites-available/tablerag
```

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /home/<USER>/app/analyzer/build;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 静态文件服务
    location /static/ {
        alias /home/<USER>/app/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/tablerag /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 5. SSL证书配置
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Docker部署

### 1. Dockerfile配置

**后端Dockerfile (backend/Dockerfile)**
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    default-libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "main.py"]
```

**前端Dockerfile (analyzer/Dockerfile)**
```dockerfile
FROM node:16-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 2. Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpass
      MYSQL_DATABASE: aidocsdb
      MYSQL_USER: aidocsuser
      MYSQL_PASSWORD: aidocspass
    ports:
      - "13306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/init_mysql.sql:/docker-entrypoint-initdb.d/init.sql

  backend:
    build: ./backend
    depends_on:
      - mysql
    environment:
      - DATABASE_URL=mysql+pymysql://aidocsuser:aidocspass@mysql:3306/aidocsdb
    ports:
      - "8000:8000"
    volumes:
      - ./dataset:/app/dataset
      - ./parse_results:/app/parse_results

  frontend:
    build: ./analyzer
    ports:
      - "3000:80"
    depends_on:
      - backend

volumes:
  mysql_data:
```

### 3. 启动Docker环境
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 监控和维护

### 1. 日志管理
```bash
# 配置日志轮转
sudo vim /etc/logrotate.d/tablerag
```

```
/home/<USER>/app/backend/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 tablerag tablerag
}
```

### 2. 性能监控
```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控脚本
cat > /home/<USER>/monitor.sh << 'EOF'
#!/bin/bash
echo "=== System Status ==="
date
echo "CPU Usage:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
echo "Memory Usage:"
free -h
echo "Disk Usage:"
df -h /
echo "Service Status:"
systemctl is-active tablerag-backend
systemctl is-active mysql
systemctl is-active nginx
EOF

chmod +x /home/<USER>/monitor.sh
```

### 3. 备份策略
```bash
# 数据库备份脚本
cat > /home/<USER>/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -h localhost -P 13306 -u aidocsuser -p'aidocspass' aidocsdb > $BACKUP_DIR/db_$DATE.sql

# 备份数据文件
tar -czf $BACKUP_DIR/data_$DATE.tar.gz /home/<USER>/app/dataset /home/<USER>/app/parse_results

# 清理旧备份 (保留7天)
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

chmod +x /home/<USER>/backup.sh

# 添加到crontab
crontab -e
# 添加: 0 2 * * * /home/<USER>/backup.sh
```

## 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查找占用进程
sudo lsof -i :8000
sudo lsof -i :3000

# 杀死进程
sudo kill -9 <PID>
```

2. **数据库连接失败**
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 检查端口监听
sudo netstat -tlnp | grep 13306

# 测试连接
mysql -h localhost -P 13306 -u aidocsuser -p
```

3. **权限问题**
```bash
# 修复文件权限
sudo chown -R tablerag:tablerag /home/<USER>/app
sudo chmod -R 755 /home/<USER>/app
```

4. **内存不足**
```bash
# 检查内存使用
free -h
ps aux --sort=-%mem | head

# 重启服务释放内存
sudo systemctl restart tablerag-backend
```

### 性能优化

1. **数据库优化**
```sql
-- 添加索引
CREATE INDEX idx_dataset_name ON tablerag_annotations(dataset_name);
CREATE INDEX idx_image_name ON tablerag_annotations(image_name);

-- 优化配置
SET GLOBAL innodb_buffer_pool_size = 2147483648;  -- 2GB
```

2. **应用优化**
```python
# 连接池配置
DATABASE_URL = "mysql+pymysql://user:pass@host:port/db?pool_size=20&max_overflow=30"

# 缓存配置
CACHE_CONFIG = {
    "CACHE_TYPE": "redis",
    "CACHE_REDIS_URL": "redis://localhost:6379/0"
}
```

3. **Nginx优化**
```nginx
# 启用gzip压缩
gzip on;
gzip_types text/plain text/css application/json application/javascript;

# 设置缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 安全配置

### 1. 防火墙配置
```bash
# 只开放必要端口
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. 应用安全
```python
# 环境变量配置
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key")
DATABASE_URL = os.getenv("DATABASE_URL")

# CORS配置
CORS_ORIGINS = [
    "https://your-domain.com",
    "https://www.your-domain.com"
]
```

### 3. 数据库安全
```sql
-- 删除默认用户
DROP USER IF EXISTS ''@'localhost';
DROP USER IF EXISTS ''@'%';

-- 限制权限
REVOKE ALL PRIVILEGES ON *.* FROM 'aidocsuser'@'localhost';
GRANT SELECT, INSERT, UPDATE, DELETE ON aidocsdb.* TO 'aidocsuser'@'localhost';
```

## 扩展部署

### 负载均衡配置
```nginx
upstream backend {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    location /api/ {
        proxy_pass http://backend;
    }
}
```

### 集群部署
```yaml
# kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tablerag-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: tablerag-backend
  template:
    metadata:
      labels:
        app: tablerag-backend
    spec:
      containers:
      - name: backend
        image: tablerag/backend:latest
        ports:
        - containerPort: 8000
```

通过以上配置，您可以在不同环境中成功部署TableRAG Enhanced系统。记住根据实际需求调整配置参数和资源分配。
