"""
解析管理器

统一管理所有解析器，协调解析流程
"""
import os
import time
from typing import Dict, Any, List
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

from .parsers.base import BaseParser
from .parsers.kdc_parser import <PERSON>d<PERSON><PERSON><PERSON><PERSON>, <PERSON>dc<PERSON><PERSON><PERSON><PERSON>er, KdcKdcParser
from .parsers.monkey_ocr_parser import MonkeyOcrParser, MonkeyOcrLatexParser, LocalMonkeyOcrParser
from .parsers.vl_llm_parser import VlLlmParser
from .parsers.monkey_ocr_kas_parser import MonkeyOcrKasParser
from .parsers.ocrflux_parser import OcrfluxParser
from .parsers.mineru_parser import MineruParser
from .parsers.mineru_vlm_parser import MineruVlmParser

logger = logging.getLogger(__name__)


class ParseManager:
    """解析管理器"""
    
    def __init__(self):
        self.parsers: Dict[str, BaseParser] = {}
        self.logger = logging.getLogger(__name__)
    
    def register_parser(self, parser: BaseParser):
        """
        注册解析器
        
        Args:
            parser: 解析器实例
        """
        self.parsers[parser.name] = parser
        self.logger.info(f"注册解析器: {parser.name}")
    
    def get_parser(self, name: str) -> BaseParser:
        """
        获取解析器
        
        Args:
            name: 解析器名称
            
        Returns:
            解析器实例
        """
        return self.parsers.get(name)
    
    def list_parsers(self) -> List[str]:
        """
        列出所有已注册的解析器
        
        Returns:
            解析器名称列表
        """
        return list(self.parsers.keys())
    
    def get_enabled_parsers(self) -> List[str]:
        """
        从环境变量获取启用的解析器列表
        
        Returns:
            启用的解析器名称列表
        """
        enabled_list = os.getenv("PARSER_ENABLED_LIST", "kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,monkey_ocr_local,vl_llm,monkey_ocr_kas,ocrflux,mineru,mineru_vlm")
        parser_names = [name.strip() for name in enabled_list.split(",") if name.strip()]
        
        self.logger.info(f"启用的解析器: {parser_names}")
        return parser_names
    
    def get_available_enabled_parsers(self) -> List[str]:
        """
        获取已注册且启用的解析器列表
        
        Returns:
            已注册且启用的解析器名称列表
        """
        enabled_parsers = self.get_enabled_parsers()
        
        # 过滤掉未注册的解析器
        available_parsers = []
        for name in enabled_parsers:
            if name in self.parsers:
                available_parsers.append(name)
            else:
                self.logger.warning(f"解析器 {name} 未注册，将被跳过")
        
        self.logger.info(f"可用的启用解析器: {available_parsers}")
        return available_parsers
    
    def get_execution_mode(self) -> str:
        """
        从环境变量获取执行模式
        
        Returns:
            执行模式: 'parallel' 或 'sequential'
        """
        mode = os.getenv("PARSER_EXECUTION_MODE", "parallel").lower()
        if mode not in ["parallel", "sequential"]:
            self.logger.warning(f"无效的执行模式 '{mode}'，使用默认的 'parallel'")
            mode = "parallel"
        
        self.logger.info(f"解析器执行模式: {mode}")
        return mode
    
    def get_sequential_order(self) -> List[str]:
        """
        从环境变量获取串行执行顺序
        
        Returns:
            解析器执行顺序列表
        """
        order_str = os.getenv("PARSER_SEQUENTIAL_ORDER", "monkey_ocr_local,kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,vl_llm,monkey_ocr_kas,ocrflux,mineru")
        order_list = [name.strip() for name in order_str.split(",") if name.strip()]
        
        # 只返回已启用且已注册的解析器
        enabled_parsers = set(self.get_available_enabled_parsers())
        ordered_parsers = [name for name in order_list if name in enabled_parsers]
        
        # 添加启用但不在顺序列表中的解析器
        remaining_parsers = [name for name in enabled_parsers if name not in ordered_parsers]
        ordered_parsers.extend(remaining_parsers)
        
        self.logger.info(f"串行执行顺序: {ordered_parsers}")
        return ordered_parsers

    def get_retry_count(self) -> int:
        """
        从环境变量获取重试次数

        Returns:
            重试次数
        """
        retry_count = int(os.getenv("PARSER_RETRY_COUNT", "3"))
        if retry_count < 0:
            self.logger.warning(f"无效的重试次数 '{retry_count}'，使用默认值 3")
            retry_count = 3

        self.logger.debug(f"解析器重试次数: {retry_count}")
        return retry_count

    def get_retry_delay(self) -> float:
        """
        从环境变量获取重试间隔时间

        Returns:
            重试间隔时间（秒）
        """
        retry_delay = float(os.getenv("PARSER_RETRY_DELAY", "2"))
        if retry_delay < 0:
            self.logger.warning(f"无效的重试间隔 '{retry_delay}'，使用默认值 2")
            retry_delay = 2.0

        self.logger.debug(f"解析器重试间隔: {retry_delay}秒")
        return retry_delay

    def parse_with_parser(self, parser_name: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用指定解析器解析文件

        Args:
            parser_name: 解析器名称
            file_info: 文件信息

        Returns:
            解析结果
        """
        parser = self.get_parser(parser_name)
        if not parser:
            raise ValueError(f"未找到解析器: {parser_name}")

        return parser.parse_with_error_handling(file_info)

    def parse_with_parser_retry(self, parser_name: str, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用指定解析器解析文件，支持重试

        Args:
            parser_name: 解析器名称
            file_info: 文件信息

        Returns:
            解析结果
        """
        retry_count = self.get_retry_count()
        retry_delay = self.get_retry_delay()

        last_error = None

        for attempt in range(retry_count + 1):  # +1 因为第一次不算重试
            try:
                if attempt > 0:
                    self.logger.info(f"解析器 {parser_name} 第 {attempt} 次重试 (文件: {file_info.get('filename', 'Unknown')})")
                    time.sleep(retry_delay)

                result = self.parse_with_parser(parser_name, file_info)

                # 检查解析是否成功
                if result.get('success', False):
                    if attempt > 0:
                        self.logger.info(f"解析器 {parser_name} 重试成功 (第 {attempt} 次重试)")
                    return result
                else:
                    # 解析失败，记录错误但继续重试
                    error_msg = result.get('error', '未知错误')
                    self.logger.warning(f"解析器 {parser_name} 解析失败: {error_msg}")
                    last_error = error_msg

                    # 如果是最后一次尝试，返回失败结果
                    if attempt == retry_count:
                        self.logger.error(f"解析器 {parser_name} 重试 {retry_count} 次后仍然失败")
                        # 更新错误信息以包含重试信息
                        result["error"] = f"重试 {retry_count} 次后失败: {last_error}"
                        return result

            except Exception as e:
                error_msg = str(e)
                self.logger.warning(f"解析器 {parser_name} 执行异常: {error_msg}")
                last_error = error_msg

                # 如果是最后一次尝试，返回错误结果
                if attempt == retry_count:
                    self.logger.error(f"解析器 {parser_name} 重试 {retry_count} 次后仍然异常")
                    return {
                        "parser_name": parser_name,
                        "success": False,
                        "error": f"重试 {retry_count} 次后失败: {last_error}",
                        "filename": file_info.get("filename"),
                        "file_id": file_info.get("file_id"),
                        "original_image": file_info.get("original_image"),
                        "result": None
                    }

        # 理论上不会到达这里，但为了安全起见
        return {
            "parser_name": parser_name,
            "success": False,
            "error": f"重试 {retry_count} 次后失败: {last_error}",
            "filename": file_info.get("filename"),
            "file_id": file_info.get("file_id"),
            "original_image": file_info.get("original_image"),
            "result": None
        }
    
    def parse_with_multiple_parsers(self, file_info: Dict[str, Any], 
                                   parser_names: List[str] = None,
                                   execution_mode: str = None) -> Dict[str, Dict[str, Any]]:
        """
        使用多个解析器解析文件
        
        Args:
            file_info: 文件信息
            parser_names: 解析器名称列表，None表示使用环境变量配置
            execution_mode: 执行模式，None表示使用环境变量配置
            
        Returns:
            解析结果字典，键为解析器名称
        """
        # 使用配置的解析器列表和执行模式
        if parser_names is None:
            parser_names = self.get_available_enabled_parsers()
        
        if execution_mode is None:
            execution_mode = self.get_execution_mode()
        
        if not parser_names:
            self.logger.warning("没有启用的解析器")
            return {}
        
        results = {}
        
        if execution_mode == "parallel":
            # 并行执行
            self.logger.info(f"并行执行 {len(parser_names)} 个解析器 (重试次数: {self.get_retry_count()})")
            with ThreadPoolExecutor(max_workers=len(parser_names)) as executor:
                future_to_parser = {
                    executor.submit(self.parse_with_parser_retry, parser_name, file_info): parser_name
                    for parser_name in parser_names
                    if self.get_parser(parser_name)
                }

                for future in as_completed(future_to_parser):
                    parser_name = future_to_parser[future]
                    try:
                        result = future.result()
                        results[parser_name] = result
                        if result.get('success', False):
                            self.logger.info(f"解析器 {parser_name} 执行完成")
                        else:
                            self.logger.warning(f"解析器 {parser_name} 执行失败: {result.get('error', '未知错误')}")
                    except Exception as e:
                        self.logger.error(f"解析器 {parser_name} 执行异常: {e}")
                        results[parser_name] = {
                            "parser_name": parser_name,
                            "success": False,
                            "error": str(e),
                            "filename": file_info.get("filename"),
                            "file_id": file_info.get("file_id"),
                            "original_image": file_info.get("original_image"),
                            "result": None
                        }
        else:
            # 串行执行
            if parser_names == self.get_available_enabled_parsers():
                # 如果使用的是环境变量配置的解析器列表，则按配置的顺序执行
                ordered_parsers = self.get_sequential_order()
                parsers_to_execute = [name for name in ordered_parsers if name in parser_names]
            else:
                # 如果是明确指定的解析器列表，则按指定顺序执行
                parsers_to_execute = [name for name in parser_names if self.get_parser(name)]

            self.logger.info(f"串行执行 {len(parsers_to_execute)} 个解析器，顺序: {parsers_to_execute} (重试次数: {self.get_retry_count()})")

            for parser_name in parsers_to_execute:
                try:
                    self.logger.info(f"开始执行解析器: {parser_name}")
                    result = self.parse_with_parser_retry(parser_name, file_info)
                    results[parser_name] = result
                    if result.get('success', False):
                        self.logger.info(f"解析器 {parser_name} 执行完成")
                    else:
                        self.logger.warning(f"解析器 {parser_name} 执行失败: {result.get('error', '未知错误')}")
                except Exception as e:
                    self.logger.error(f"解析器 {parser_name} 执行异常: {e}")
                    results[parser_name] = {
                        "parser_name": parser_name,
                        "success": False,
                        "error": str(e),
                        "filename": file_info.get("filename"),
                        "file_id": file_info.get("file_id"),
                        "original_image": file_info.get("original_image"),
                        "result": None
                    }
        
        self.logger.info(f"多路解析完成，成功: {len([r for r in results.values() if r.get('success', False)])}/{len(results)}")
        return results
    
    def create_default_parsers(self) -> "ParseManager":
        """
        创建默认的解析器配置
        
        Returns:
            解析管理器实例
        """
        # 从环境变量读取配置
        kdc_config = {
            "ak": os.getenv("WPS_AK"),
            "sk": os.getenv("WPS_SK"),
            "company_id": "41000207",  # 从原代码中提取
            "default_cookie": "wps_sid=V02S0BcnvoTpJMCfpBqDo55ak2ttYAE00a1a83a3005d1b1066"  # 从原代码中提取
        }
        
        # 注册KDC解析器
        if kdc_config["ak"] and kdc_config["sk"]:
            self.register_parser(KdcParser(**kdc_config))
            self.register_parser(KdcPlainParser(**kdc_config))
            self.register_parser(KdcKdcParser(**kdc_config))
        else:
            self.logger.warning("WPS AK/SK 未配置，跳过KDC解析器注册")
        
        # 注册MonkeyOCR解析器
        self.register_parser(MonkeyOcrParser())
        self.register_parser(MonkeyOcrLatexParser())
        self.register_parser(LocalMonkeyOcrParser())
        
        # 注册VL LLM解析器
        self.register_parser(VlLlmParser())
        
        # 注册MonkeyOCR（kas）解析器
        self.register_parser(MonkeyOcrKasParser())
        
        # 注册Ocrflux解析器
        self.register_parser(OcrfluxParser())
        
        # 注册Mineru解析器
        self.register_parser(MineruParser())
        
        # 注册Mineru VLM解析器
        self.register_parser(MineruVlmParser())
        
        # 显示配置信息
        enabled_parsers = self.get_available_enabled_parsers()
        execution_mode = self.get_execution_mode()
        self.logger.info(f"解析器配置完成 - 启用: {len(enabled_parsers)}个, 模式: {execution_mode}")
        
        return self 