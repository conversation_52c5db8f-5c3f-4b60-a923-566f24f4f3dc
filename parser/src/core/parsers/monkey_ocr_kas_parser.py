"""
MonkeyOCR（kas）解析器

基于MonkeyOCR（kas） API的图片解析器，支持发送图片URL到远程API并获取解析结果
"""
import os
import json
import requests
import zipfile
import tempfile
import shutil
import uuid
import time
from typing import Dict, Any, Optional
from urllib.parse import urljoin, urlparse
import logging

from .base import BaseParser

# 修复导入 - 改为绝对导入
from clients.ks3_client import KS3Client

logger = logging.getLogger(__name__)


class MonkeyOcrKasParser(BaseParser):
    """MonkeyOCR（kas） API解析器"""

    def __init__(self,
                 api_base_url: str = "http://kmd-api.kas.wps.cn/api/11456-v1/Qa0N8c",
                 timeout: int = 300):
        """
        初始化MonkeyOCR（kas）解析器

        Args:
            api_base_url: MonkeyOCR（kas） API基础地址
            timeout: 请求超时时间（秒）
        """
        super().__init__("monkey_ocr_kas")
        self.api_base_url = api_base_url
        self.timeout = timeout
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'TableRAG-Parser/1.0.0',
            'Content-Type': 'application/json',
            'Accept': '*/*',
            'Connection': 'keep-alive'
        })
        
        # 从环境变量读取配置
        self.api_base_url = os.getenv("MONKEY_OCR_KAS_API_URL", self.api_base_url)
        self.timeout = int(os.getenv("MONKEY_OCR_KAS_TIMEOUT", str(self.timeout)))
        
        # 构建完整的API URL
        self.api_url = f"{self.api_base_url}/parse"
        self.download_base_url = f"{self.api_base_url}/download/"
        
        # 初始化KS3客户端用于图片上传
        self.ks3_client = None
        self._init_ks3_client()
        
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    def _init_ks3_client(self):
        """初始化KS3客户端"""
        try:
            ks3_host = os.getenv("KS3_HOST")
            ks3_ak = os.getenv("KS3_AK") 
            ks3_sk = os.getenv("KS3_SK")
            ks3_bucket = os.getenv("KS3_BUCKET")
            
            if all([ks3_host, ks3_ak, ks3_sk, ks3_bucket]):
                self.ks3_client = KS3Client()
                self.ks3_client.init(ks3_host, ks3_ak, ks3_sk, ks3_bucket)
                self.logger.info("KS3客户端初始化成功")
            else:
                self.logger.warning("KS3配置不完整，将无法上传图片")
        except Exception as e:
            self.logger.warning(f"KS3客户端初始化失败: {e}")
            self.ks3_client = None

    def is_available(self) -> bool:
        """
        检查解析器是否可用
        
        Returns:
            True if available, False otherwise
        """
        try:
            # 简单的连通性测试
            response = self.session.get(
                f"{self.api_base_url}/health",
                timeout=10
            )
            return True
        except Exception as e:
            self.logger.warning(f"MonkeyOCR（kas） API 不可用: {e}")
            return False

    def _upload_image_to_temp_storage(self, image_path: str) -> Optional[str]:
        """
        将本地图片上传到临时存储，获取公网URL
        
        Args:
            image_path: 本地图片路径
            
        Returns:
            图片的公网URL，失败返回None
        """
        if not self.ks3_client:
            self.logger.error("KS3客户端未初始化，无法上传图片")
            return None
        
        try:
            # 生成唯一的文件名
            file_ext = os.path.splitext(image_path)[1].lower()
            unique_filename = f"monkey_ocr_kas/{uuid.uuid4().hex}{file_ext}"
            
            # 上传文件到KS3
            self.logger.info(f"上传图片到KS3: {unique_filename}")
            success = self.ks3_client.upload_from_file(unique_filename, image_path)
            
            if not success:
                self.logger.error("图片上传到KS3失败")
                return None
            
            # 生成访问URL (7天有效期)
            image_url = self.ks3_client.generate_url(unique_filename, timeout=7*24*3600)
            self.logger.info(f"图片上传成功，URL: {image_url}")
            
            return image_url
            
        except Exception as e:
            self.logger.error(f"上传图片异常: {e}")
            return None

    def _send_parse_request(self, image_url: str) -> Optional[Dict[str, Any]]:
        """
        发送解析请求到MonkeyOCR（kas） API
        
        Args:
            image_url: 图片URL
            
        Returns:
            API响应结果，失败返回None
        """
        try:
            payload = {
                "file": image_url
            }
            
            self.logger.info(f"发送解析请求到MonkeyOCR（kas） API: {self.api_url}")
            self.logger.debug(f"请求参数: {payload}")

            response = self.session.post(
                self.api_url,
                json=payload,
                timeout=self.timeout
            )

            response.raise_for_status()
            result = response.json()

            self.logger.info(f"MonkeyOCR（kas） API 响应: {result.get('message', 'Unknown')}")
            self.logger.debug(f"完整API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
            
        except requests.exceptions.Timeout:
            self.logger.error(f"MonkeyOCR（kas） API 请求超时 (>{self.timeout}秒)")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"MonkeyOCR（kas） API 请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            self.logger.error(f"MonkeyOCR（kas） API 响应解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"MonkeyOCR（kas） API 请求异常: {e}")
            return None

    def _download_and_extract_results(self, download_url: str, max_retries: int = 5, retry_delay: int = 10) -> Optional[str]:
        """
        下载并解压结果文件，支持重试等待
        
        Args:
            download_url: 下载链接
            max_retries: 最大重试次数
            retry_delay: 重试间隔（秒）
            
        Returns:
            解压后的临时目录路径，失败返回None
        """
        try:
            # 构建完整的下载URL
            if download_url.startswith('/static/'):
                # 去掉/static前缀，使用预配置的下载基础URL
                clean_url = download_url[8:]  # 去掉"/static/"
                full_url = f"{self.download_base_url}{clean_url}"
            elif download_url.startswith('/'):
                # 其他以/开头的路径，去掉开头的/
                clean_url = download_url[1:]
                full_url = f"{self.download_base_url}{clean_url}"
            else:
                # 如果是相对路径，直接拼接
                full_url = f"{self.download_base_url}{download_url}"
            
            self.logger.info(f"下载解析结果: {full_url}")
            
            # 重试下载机制
            for attempt in range(max_retries):
                try:
                    self.logger.info(f"下载尝试 {attempt + 1}/{max_retries}")
                    response = self.session.get(full_url, timeout=60)
                    response.raise_for_status()
                    
                    # 检查响应内容是否为有效的zip文件
                    if len(response.content) < 100:  # zip文件应该比较大
                        raise ValueError(f"响应内容太小，可能不是有效的zip文件: {len(response.content)} bytes")
                    
                    # 创建临时目录
                    temp_dir = tempfile.mkdtemp(prefix="monkey_ocr_kas_")
                    zip_path = os.path.join(temp_dir, "results.zip")
                    
                    # 保存zip文件
                    with open(zip_path, 'wb') as f:
                        f.write(response.content)
                    
                    # 验证zip文件完整性
                    try:
                        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                            # 测试zip文件完整性
                            zip_ref.testzip()
                            # 解压文件
                            extract_dir = os.path.join(temp_dir, "extracted")
                            zip_ref.extractall(extract_dir)
                        
                        self.logger.info(f"解析结果已解压到: {extract_dir}")
                        return extract_dir
                        
                    except (zipfile.BadZipFile, zipfile.LargeZipFile) as e:
                        self.logger.warning(f"zip文件损坏，尝试重新下载: {e}")
                        shutil.rmtree(temp_dir, ignore_errors=True)
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay)
                            continue
                        else:
                            raise
                    
                except requests.exceptions.HTTPError as e:
                    if e.response.status_code == 404:
                        self.logger.warning(f"文件尚未生成，等待 {retry_delay} 秒后重试 (尝试 {attempt + 1}/{max_retries})")
                        if attempt < max_retries - 1:
                            time.sleep(retry_delay)
                            continue
                        else:
                            self.logger.error(f"文件在 {max_retries} 次尝试后仍未生成")
                            raise
                    else:
                        raise
                except Exception as e:
                    self.logger.warning(f"下载失败: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        continue
                    else:
                        raise
            
        except Exception as e:
            self.logger.error(f"下载解压结果失败: {e}")
            return None

    def _extract_markdown_content(self, extract_dir: str) -> Optional[str]:
        """
        从解压目录中提取markdown内容
        
        Args:
            extract_dir: 解压后的目录路径
            
        Returns:
            markdown内容，失败返回None
        """
        try:
            # 查找.md文件
            md_files = []
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    if file.endswith('.md'):
                        md_files.append(os.path.join(root, file))
            
            if not md_files:
                self.logger.error("未找到markdown文件")
                return None
            
            # 使用第一个找到的md文件
            md_file = md_files[0]
            self.logger.info(f"找到markdown文件: {md_file}")
            
            # 读取内容
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.logger.debug(f"提取到markdown内容 ({len(content)} 字符)")
            return content
            
        except Exception as e:
            self.logger.error(f"提取markdown内容失败: {e}")
            return None

    def _cleanup_temp_dir(self, temp_dir: str):
        """
        清理临时目录
        
        Args:
            temp_dir: 临时目录路径
        """
        try:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                self.logger.debug(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            self.logger.warning(f"清理临时目录失败: {e}")

    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析文件
        
        Args:
            file_info: 文件信息，包含filename、original_image、image_path等
            
        Returns:
            解析结果
        """
        filename = file_info.get("filename", "unknown")
        original_image = file_info.get("original_image")
        image_path = file_info.get("image_path")
        
        self.logger.info(f"开始MonkeyOCR（kas）解析: {filename}")
        
        # 优先使用image_path，回退到original_image
        actual_image_path = image_path or original_image
        
        # 检查原始图片
        if not actual_image_path or not os.path.exists(actual_image_path):
            error_msg = f"原始图片不存在: {actual_image_path}"
            self.logger.error(error_msg)
            return {
                "parser_name": self.name,
                "success": False,
                "error": error_msg,
                "filename": filename,
                "original_image": original_image,
                "result": None
            }
        
        temp_dir = None
        try:
            # 1. 上传图片获取URL
            image_url = self._upload_image_to_temp_storage(actual_image_path)
            
            if not image_url:
                # 如果上传失败，返回错误
                error_msg = "图片上传失败，请检查KS3配置 (KS3_HOST, KS3_AK, KS3_SK, KS3_BUCKET)"
                self.logger.error(error_msg)
                return {
                    "parser_name": self.name,
                    "success": False,
                    "error": error_msg,
                    "filename": filename,
                    "original_image": original_image,
                    "result": None
                }
            
            # 2. 发送解析请求
            api_response = self._send_parse_request(image_url)
            if not api_response or not api_response.get("success"):
                error_msg = f"MonkeyOCR（kas） API 解析失败: {api_response.get('message', 'Unknown error') if api_response else 'No response'}"
                self.logger.error(error_msg)
                return {
                    "parser_name": self.name,
                    "success": False,
                    "error": error_msg,
                    "filename": filename,
                    "original_image": original_image,
                    "result": None
                }
            
            # 3. 下载并解压结果
            # 尝试不同的字段名来获取下载链接
            download_url = (api_response.get("download_url") or 
                          api_response.get("downloadUrl") or 
                          api_response.get("url") or
                          api_response.get("result", {}).get("download_url"))
            
            if not download_url:
                error_msg = f"API响应中缺少下载链接。响应内容: {api_response}"
                self.logger.error(error_msg)
                return {
                    "parser_name": self.name,
                    "success": False,
                    "error": error_msg,
                    "filename": filename,
                    "original_image": original_image,
                    "result": None
                }
            
            # 使用更长的等待时间和更多重试次数
            temp_dir = self._download_and_extract_results(download_url, max_retries=10, retry_delay=20)
            if not temp_dir:
                error_msg = "下载解压结果失败"
                self.logger.error(error_msg)
                return {
                    "parser_name": self.name,
                    "success": False,
                    "error": error_msg,
                    "filename": filename,
                    "original_image": original_image,
                    "result": None
                }
            
            # 4. 提取markdown内容
            markdown_content = self._extract_markdown_content(temp_dir)
            if not markdown_content:
                error_msg = "提取markdown内容失败"
                self.logger.error(error_msg)
                return {
                    "parser_name": self.name,
                    "success": False,
                    "error": error_msg,
                    "filename": filename,
                    "original_image": original_image,
                    "result": None
                }
            
            # 5. 构建成功结果
            self.logger.info(f"MonkeyOCR（kas）解析成功: {filename}")
            
            result = {
                "parser_name": self.name,
                "success": True,
                "filename": filename,
                "original_image": original_image,
                "result": {
                    "content": markdown_content,
                    "format": "markdown",
                    "api_response": api_response,
                    "files": api_response.get("files", []),
                    "output_dir": api_response.get("output_dir")
                }
            }
            
            return result
            
        except Exception as e:
            error_msg = f"MonkeyOCR（kas）解析异常: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {
                "parser_name": self.name,
                "success": False,
                "error": error_msg,
                "filename": filename,
                "original_image": original_image,
                "result": None
            }
        
        finally:
            # 清理临时目录
            if temp_dir:
                self._cleanup_temp_dir(temp_dir) 