"""
Ocrflux解析器

基于Ocrflux API的图片解析器，支持发送图片URL到远程API并获取解析结果
"""
import os
import json
from typing import Dict, Any, Optional
import logging

from .base import BaseParser
from clients.ocrflux_client import OcrfluxClient
from clients.ks3_client import KS3Client

logger = logging.getLogger(__name__)


class OcrfluxParser(BaseParser):
    """Ocrflux API解析器"""
    
    def __init__(self, 
                 api_base_url: str = "http://kmd-api.kas.wps.cn/api/11551/2x1mnc/v1",
                 timeout: int = 300):
        """
        初始化Ocrflux解析器
        
        Args:
            api_base_url: API基础地址
            timeout: 请求超时时间（秒）
        """
        super().__init__("ocrflux")
        self.api_base_url = api_base_url
        self.timeout = timeout
        
        # 从环境变量读取配置
        self.api_base_url = os.getenv("OCRFLUX_API_URL", self.api_base_url)
        self.timeout = int(os.getenv("OCRFLUX_TIMEOUT", str(self.timeout)))
        
        # 初始化客户端
        self._client = None
        self.ks3_client = None
        self._init_clients()
        
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def _init_clients(self):
        """初始化客户端"""
        # 初始化Ocrflux客户端
        self._client = OcrfluxClient(self.api_base_url)
        
        # 初始化KS3客户端
        try:
            ks3_host = os.getenv("KS3_HOST")
            ks3_ak = os.getenv("KS3_AK") 
            ks3_sk = os.getenv("KS3_SK")
            ks3_bucket = os.getenv("KS3_BUCKET")
            
            if all([ks3_host, ks3_ak, ks3_sk, ks3_bucket]):
                self.ks3_client = KS3Client()
                self.ks3_client.init(ks3_host, ks3_ak, ks3_sk, ks3_bucket)
                self.logger.info("KS3客户端初始化成功")
            else:
                self.logger.warning("KS3配置不完整，将无法上传图片")
        except Exception as e:
            self.logger.warning(f"KS3客户端初始化失败: {e}")
            self.ks3_client = None
    
    def is_available(self) -> bool:
        """检查解析器是否可用"""
        return self._client is not None and self.ks3_client is not None
    
    def _upload_image_to_temp_storage(self, image_path: str) -> Optional[str]:
        """
        将本地图片上传到临时存储，获取公网URL
        
        Args:
            image_path: 本地图片路径
            
        Returns:
            图片的公网URL，失败返回None
        """
        if not self.ks3_client:
            self.logger.error("KS3客户端未初始化，无法上传图片")
            return None
        
        try:
            # 生成唯一的文件名
            file_ext = os.path.splitext(image_path)[1].lower()
            unique_filename = f"ocrflux/{os.path.basename(image_path)}"
            
            # 上传文件到KS3
            self.logger.info(f"上传图片到KS3: {unique_filename}")
            success = self.ks3_client.upload_from_file(unique_filename, image_path)
            
            if not success:
                self.logger.error("图片上传到KS3失败")
                return None
            
            # 生成访问URL (7天有效期)
            image_url = self.ks3_client.generate_url(unique_filename, timeout=7*24*3600)
            self.logger.info(f"图片上传成功，URL: {image_url}")
            
            return image_url
            
        except Exception as e:
            self.logger.error(f"上传图片异常: {e}")
            return None
    
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析文件
        
        Args:
            file_info: 文件信息，包含filename、original_image、image_path等
            
        Returns:
            解析结果
        """
        filename = file_info.get("filename", "unknown")
        original_image = file_info.get("original_image")
        image_path = file_info.get("image_path")
        
        self.logger.info(f"开始Ocrflux解析: {filename}")
        
        # 优先使用image_path，回退到original_image
        actual_image_path = image_path or original_image
        
        # 检查原始图片
        if not actual_image_path or not os.path.exists(actual_image_path):
            error_msg = f"原始图片不存在: {actual_image_path}"
            self.logger.error(error_msg)
            return {
                "parser_name": self.name,
                "success": False,
                "error": error_msg,
                "filename": filename,
                "original_image": original_image,
                "result": None
            }
        
        try:
            # 1. 上传图片获取URL
            image_url = self._upload_image_to_temp_storage(actual_image_path)
            
            if not image_url:
                # 如果上传失败，返回错误
                error_msg = "图片上传失败，请检查KS3配置 (KS3_HOST, KS3_AK, KS3_SK, KS3_BUCKET)"
                self.logger.error(error_msg)
                return {
                    "parser_name": self.name,
                    "success": False,
                    "error": error_msg,
                    "filename": filename,
                    "original_image": original_image,
                    "result": None
                }
            
            # 2. 发送解析请求
            api_response = self._client.chat_completion(image_url)
            if not api_response:
                error_msg = "Ocrflux API 解析失败: 无响应"
                self.logger.error(error_msg)
                return {
                    "parser_name": self.name,
                    "success": False,
                    "error": error_msg,
                    "filename": filename,
                    "original_image": original_image,
                    "result": None
                }
            
            # 3. 解析响应内容
            try:
                choices = api_response.get("choices", [])
                if not choices:
                    raise ValueError("API响应中没有choices字段")
                
                message = choices[0].get("message", {})
                content = message.get("content", "")
                
                if not content:
                    raise ValueError("API响应中没有content字段")
                
                # 尝试解析JSON内容
                try:
                    content_data = json.loads(content)
                    # 提取natural_text字段
                    natural_text = content_data.get("natural_text", "")
                    if not natural_text:
                        raise ValueError("解析结果中没有natural_text字段")
                    
                    # 构建成功结果
                    return {
                        "parser_name": self.name,
                        "success": True,
                        "filename": filename,
                        "original_image": original_image,
                        "result": {
                            "content": natural_text,
                            "format": "html",
                            "api_response": api_response
                        }
                    }
                    
                except json.JSONDecodeError:
                    # 如果不是JSON格式，直接使用content作为结果
                    return {
                        "parser_name": self.name,
                        "success": True,
                        "filename": filename,
                        "original_image": original_image,
                        "result": {
                            "content": content,
                            "format": "text",
                            "api_response": api_response
                        }
                    }
                
            except Exception as e:
                error_msg = f"解析API响应失败: {str(e)}"
                self.logger.error(error_msg)
                return {
                    "parser_name": self.name,
                    "success": False,
                    "error": error_msg,
                    "filename": filename,
                    "original_image": original_image,
                    "result": None
                }
            
        except Exception as e:
            error_msg = f"Ocrflux解析异常: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            return {
                "parser_name": self.name,
                "success": False,
                "error": error_msg,
                "filename": filename,
                "original_image": original_image,
                "result": None
            } 