"""
Mineru VLM 解析器

基于 Mineru 的 VLM (Vision-Language Model) 解析器，
使用 '-b vlm-transformers' 参数，生成结果在 vlm/ 目录下
"""
import os
import subprocess
from typing import Dict, Any
import logging
import json
from pathlib import Path

from .base import BaseParser

logger = logging.getLogger(__name__)


class MineruVlmParser(BaseParser):
    """Mineru VLM 解析器"""
    
    def __init__(self):
        super().__init__("mineru_vlm")
        self.logger = logging.getLogger(__name__)
    
    def is_available(self) -> bool:
        """
        检查解析器是否可用
        
        Returns:
            是否可用
        """
        try:
            mineru_path = "/data/deploy/pyvenvs/pyldu/bin/mineru"  # 使用完整路径
            subprocess.run([mineru_path, "--version"], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.logger.warning("Mineru 命令不可用")
            return False
    
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析文件
        
        Args:
            file_info: 文件信息，包含文件路径、ID等
            
        Returns:
            解析结果字典
        """
        self.logger.info(f"开始VLM解析文件: {file_info}")
        try:
            # 获取PDF文件路径
            pdf_dir = os.getenv("PDF_DIR")
            if not pdf_dir:
                return {
                    "success": False,
                    "error": "PDF_DIR 环境变量未设置",
                    "result": None
                }
            
            # 从文件名构建PDF路径
            filename = file_info.get("filename") or file_info.get("fname")
            if not filename:
                return {
                    "success": False,
                    "error": "文件名未提供",
                    "result": None
                }
            
            pdf_path = os.path.join(pdf_dir, filename)
            if not os.path.exists(pdf_path):
                return {
                    "success": False,
                    "error": f"PDF文件不存在: {pdf_path}",
                    "result": None
                }
            
            # 获取原始PDF文件名（不含扩展名）
            pdf_name = Path(pdf_path).stem
            
            # 获取 Mineru VLM 输出目录
            mineru_vlm_output_dir = os.getenv("MINERU_VLM_OUTPUT_DIR")
            if not mineru_vlm_output_dir:
                return {
                    "success": False,
                    "error": "MINERU_VLM_OUTPUT_DIR 环境变量未设置",
                    "result": None
                }
            
            # 构建输出目录路径
            output_dir = os.path.join(mineru_vlm_output_dir, pdf_name)
            os.makedirs(output_dir, exist_ok=True)
            
            # 运行 mineru 命令，添加 VLM 参数
            mineru_path = "/data/deploy/pyvenvs/pyldu/bin/mineru"  # 使用完整路径
            mineru_cmd = [mineru_path, "-p", pdf_path, "-o", output_dir, "-b", "vlm-transformers"]
            try:
                self.logger.info(f"运行 mineru VLM: {' '.join(mineru_cmd)}")
                subprocess.run(mineru_cmd, check=True, capture_output=True)
            except subprocess.CalledProcessError as e:
                return {
                    "success": False,
                    "error": f"Mineru VLM 执行失败: {e.stderr.decode() if e.stderr else str(e)}",
                    "result": None
                }
            
            # 检查 vlm 目录下的 layout.pdf 文件是否存在
            vlm_dir = os.path.join(output_dir, pdf_name, "vlm")
            layout_pdf_path = os.path.join(vlm_dir, f"{pdf_name}_layout.pdf")
            if not os.path.exists(layout_pdf_path):
                return {
                    "success": False,
                    "error": f"VLM Layout PDF 文件不存在: {layout_pdf_path}",
                    "result": None
                }
            
            # 检查 model_output.txt 文件是否存在（包含VLM解析结果）
            model_output_path = os.path.join(vlm_dir, f"{pdf_name}_model_output.txt")
            if not os.path.exists(model_output_path):
                return {
                    "success": False,
                    "error": f"VLM Model Output 文件不存在: {model_output_path}",
                    "result": None
                }
            
            # 读取并解析 model_output.txt 文件
            layout_data = self._parse_vlm_output(model_output_path)
            
            # 返回解析结果
            result = {
                "success": True,
                "result": {
                    "layout_data": layout_data,  # 版式识别结果
                    "page_info": {"width": 1000, "height": 1000},  # 默认页面信息
                    "layout_pdf": layout_pdf_path,  # layout.pdf 文件路径
                    "model_output": model_output_path  # model_output.txt 文件路径
                }
            }
            self.logger.info(f"VLM解析完成，返回结果: {result}")
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": f"VLM解析失败: {str(e)}",
                "result": None
            }
    
    def _parse_vlm_output(self, model_output_path: str) -> list:
        """
        解析VLM输出的model_output.txt文件
        
        Args:
            model_output_path: model_output.txt文件路径
            
        Returns:
            layout_data列表，与原始mineru格式兼容
        """
        import re
        
        layout_data = []
        
        try:
            with open(model_output_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式解析VLM格式
            # 格式: <|box_start|>x1 y1 x2 y2<|box_end|><|ref_start|>type<|ref_end|><|md_start|>content<|md_end|>
            pattern = r'<\|box_start\|>(\d+)\s+(\d+)\s+(\d+)\s+(\d+)<\|box_end\|><\|ref_start\|>([^<]+)<\|ref_end\|><\|md_start\|>(.*?)<\|md_end\|>'
            
            matches = re.findall(pattern, content, re.DOTALL)
            
            for match in matches:
                x1, y1, x2, y2, ref_type, md_content = match
                
                # 转换坐标为整数
                bbox = [int(x1), int(y1), int(x2), int(y2)]
                
                # 处理不同类型的内容
                if ref_type == 'table':
                    # 解析表格内容
                    parsed_content = self._parse_table_content(md_content)
                elif ref_type == 'title':
                    # 解析标题内容
                    parsed_content = md_content.strip()
                elif ref_type == 'text':
                    # 解析文本内容
                    parsed_content = md_content.strip()
                elif ref_type == 'image':
                    # 解析图像内容
                    parsed_content = md_content.strip()
                else:
                    # 其他类型
                    parsed_content = md_content.strip()
                
                # 构建layout_data条目
                layout_item = {
                    "category_id": self._get_category_id(ref_type),
                    "poly": bbox,  # 使用bbox作为多边形
                    "score": 0.9,  # 默认置信度
                    "text": parsed_content,
                    "type": ref_type,
                    "bbox": bbox
                }
                
                layout_data.append(layout_item)
            
            self.logger.info(f"解析VLM输出完成，共解析到 {len(layout_data)} 个元素")
            return layout_data
            
        except Exception as e:
            self.logger.error(f"解析VLM输出失败: {str(e)}")
            return []
    
    def _parse_table_content(self, table_md: str) -> str:
        """
        解析表格的markdown内容，将VLM格式转换为可读文本
        
        Args:
            table_md: 表格的markdown内容
            
        Returns:
            处理后的表格文本
        """
        # 替换VLM特殊标记
        content = table_md
        content = content.replace('<fcel>', '|')  # 单元格分隔符
        content = content.replace('<lcel>', '')   # 行结束标记
        content = content.replace('<ucel>', '|')  # 合并单元格标记
        content = content.replace('<nl>', '\n')   # 换行符
        
        # 清理多余的竖线和空白
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # 确保每行以|开头和结尾
                if not line.startswith('|'):
                    line = '|' + line
                if not line.endswith('|'):
                    line = line + '|'
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def _get_category_id(self, ref_type: str) -> int:
        """
        根据元素类型返回category_id
        
        Args:
            ref_type: 元素类型
            
        Returns:
            category_id
        """
        category_map = {
            'title': 0,
            'text': 1,
            'table': 2,
            'image': 3,
            'figure': 4,
            'formula': 5
        }
        return category_map.get(ref_type, 1)  # 默认为文本类型