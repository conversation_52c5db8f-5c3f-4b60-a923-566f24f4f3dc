"""
Mineru 解析器

用于解析 Mineru 生成的 layout.pdf 文件中的版式识别结果
"""
import os
import subprocess
from typing import Dict, Any
import logging
import json
from pathlib import Path

from .base import BaseParser

logger = logging.getLogger(__name__)


class MineruParser(BaseParser):
    """Mineru 解析器"""
    
    def __init__(self):
        super().__init__("mineru")
        self.logger = logging.getLogger(__name__)
    
    def is_available(self) -> bool:
        """
        检查解析器是否可用
        
        Returns:
            是否可用
        """
        try:
            subprocess.run(["mineru", "--version"], capture_output=True, check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.logger.warning("Mineru 命令不可用")
            return False
    
    def parse(self, file_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析文件
        
        Args:
            file_info: 文件信息，包含文件路径、ID等
            
        Returns:
            解析结果字典
        """
        self.logger.info(f"开始解析文件: {file_info}")
        try:
            # 获取PDF文件路径
            pdf_dir = os.getenv("PDF_DIR")
            if not pdf_dir:
                return {
                    "success": False,
                    "error": "PDF_DIR 环境变量未设置",
                    "result": None
                }
            
            # 从文件名构建PDF路径
            filename = file_info.get("filename") or file_info.get("fname")
            if not filename:
                return {
                    "success": False,
                    "error": "文件名未提供",
                    "result": None
                }
            
            pdf_path = os.path.join(pdf_dir, filename)
            if not os.path.exists(pdf_path):
                return {
                    "success": False,
                    "error": f"PDF文件不存在: {pdf_path}",
                    "result": None
                }
            
            # 获取原始PDF文件名（不含扩展名）
            pdf_name = Path(pdf_path).stem
            
            # 获取 Mineru 输出目录
            mineru_output_dir = os.getenv("MINERU_OUTPUT_DIR")
            if not mineru_output_dir:
                return {
                    "success": False,
                    "error": "MINERU_OUTPUT_DIR 环境变量未设置",
                    "result": None
                }
            
            # 构建输出目录路径
            output_dir = os.path.join(mineru_output_dir, pdf_name)
            os.makedirs(output_dir, exist_ok=True)
            
            # 运行 mineru 命令
            mineru_cmd = ["mineru", "-p", pdf_path, "-o", output_dir]
            try:
                self.logger.info(f"运行 mineru: {' '.join(mineru_cmd)}")
                subprocess.run(mineru_cmd, check=True, capture_output=True)
            except subprocess.CalledProcessError as e:
                return {
                    "success": False,
                    "error": f"Mineru 执行失败: {e.stderr.decode() if e.stderr else str(e)}",
                    "result": None
                }
            
            # 检查 layout.pdf 文件是否存在
            auto_dir = os.path.join(output_dir, pdf_name, "auto")
            layout_pdf_path = os.path.join(auto_dir, f"{pdf_name}_layout.pdf")
            if not os.path.exists(layout_pdf_path):
                return {
                    "success": False,
                    "error": f"Layout PDF 文件不存在: {layout_pdf_path}",
                    "result": None
                }
            
            # 检查 model.json 文件是否存在（包含版式识别结果）
            model_json_path = os.path.join(auto_dir, f"{pdf_name}_model.json")
            if not os.path.exists(model_json_path):
                return {
                    "success": False,
                    "error": f"Model JSON 文件不存在: {model_json_path}",
                    "result": None
                }
            
            # 读取 model.json 文件
            with open(model_json_path, 'r', encoding='utf-8') as f:
                layout_data = json.load(f)
            
            # 返回解析结果
            result = {
                "success": True,
                "result": {
                    "layout_data": layout_data[0]["layout_dets"],  # 版式识别结果
                    "page_info": layout_data[0]["page_info"],  # 页面信息
                    "layout_pdf": layout_pdf_path,  # layout.pdf 文件路径
                    "model_json": model_json_path  # model.json 文件路径
                }
            }
            self.logger.info(f"解析完成，返回结果: {result}")
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": f"解析失败: {str(e)}",
                "result": None
            }