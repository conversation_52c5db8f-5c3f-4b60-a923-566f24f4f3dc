# clients/base_client.py
import os
from typing import Optional, Dict


class BaseClient:
    def __init__(self, api_base_url: str):
        self.base_url = api_base_url
        self._proxies = None
        self.headers = {
            'Content-Type': 'application/json'
        }

    @property
    def proxies(self) -> Optional[Dict]:
        """获取代理配置"""
        if self._proxies is None:
            self._proxies = self._load_proxies()
        return self._proxies

    def _load_proxies(self) -> Optional[Dict]:
        """从环境变量加载代理配置"""
        http_proxy = os.environ.get("http_proxy")
        https_proxy = os.environ.get("https_proxy")
        
        proxies = {}
        if http_proxy:
            proxies["http"] = http_proxy
        if https_proxy:
            proxies["https"] = https_proxy
            
        return proxies if proxies else None

    def set_proxies(self, proxies: Optional[Dict]):
        """手动设置代理"""
        self._proxies = proxies
