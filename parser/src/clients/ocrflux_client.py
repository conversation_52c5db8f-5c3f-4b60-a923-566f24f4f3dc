import os
import requests
from typing import Dict, Optional
from .base_client import BaseClient


class OcrfluxClient(BaseClient):
    """Ocrflux API客户端"""
    
    def __init__(self, api_base_url: str):
        """
        初始化Ocrflux客户端
        
        Args:
            api_base_url: API基础地址
        """
        super().__init__(api_base_url)
        
        # 设置自定义User-Agent和Host
        custom_user_agent = os.getenv('CUSTOM_OCRFLUX_USER_AGENT', 'Apifox/1.0.0 (https://apifox.com)')
        custom_host = os.getenv('CUSTOM_OCRFLUX_HOST', 'kmd-api.kas.wps.cn')
        
        self.headers.update({
            'User-Agent': custom_user_agent,
            'Host': custom_host
        })

    def chat_completion(self, image_url: str, text_prompt: str = None, 
                       model: str = "", max_tokens: int = 4096, 
                       temperature: float = 0) -> Dict:
        """
        发送聊天补全请求
        
        Args:
            image_url: 图片URL
            text_prompt: 文本提示，如果为None则使用默认提示语
            model: 模型名称
            max_tokens: 最大token数
            temperature: 温度参数
            
        Returns:
            Dict: API响应结果
        """
        if text_prompt is None:
            text_prompt = """Below is the image of one page of a document. Just return the plain text representation of this document as if you were reading it naturally.
ALL tables should be presented in HTML format.
If there are images or figures in the page, present them as "<Image>(left,top),(right,bottom)</Image>", (left,top,right,bottom) are the coordinates of the top-left and bottom-right corners of the image or figure.
Present all titles and headings as H1 headings.
Do not hallucinate.
"""
        
        url = f"{self.base_url}/chat/completions"
        
        payload = {
            "model": model,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url
                            }
                        },
                        {
                            "type": "text",
                            "text": text_prompt
                        }
                    ]
                }
            ]
        }
        
        # 从环境变量读取代理配置
        proxy_host = os.getenv('KS3_PROXY_HOST', '127.0.0.1')
        proxy_port = int(os.getenv('KS3_PROXY_PORT', '18899'))
        proxy = f"http://{proxy_host}:{proxy_port}"
        
        response = requests.post(
            url, 
            headers=self.headers, 
            json=payload, 
            proxies={"http": proxy, "https": proxy}
        )
        response.raise_for_status()
        
        return response.json() 