#!/bin/bash

DATASET_NAME=07-16-100-border-only ./table_parser.sh
DATASET_NAME=07-16-100-wire ./table_parser.sh
DATASET_NAME=07-16-150-border-only ./table_parser.sh
DATASET_NAME=07-16-150-three-line ./table_parser.sh
DATASET_NAME=07-16-150-wire ./table_parser.sh
DATASET_NAME=07-16-200-border-only ./table_parser.sh
DATASET_NAME=07-16-200-three-line ./table_parser.sh
DATASET_NAME=07-16-200-wireless ./table_parser.sh
DATASET_NAME=07-16-50-border-only ./table_parser.sh
DATASET_NAME=07-16-50-three-line ./table_parser.sh
DATASET_NAME=07-16-50-wire ./table_parser.sh
