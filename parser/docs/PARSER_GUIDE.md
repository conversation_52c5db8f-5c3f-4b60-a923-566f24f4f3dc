# Parser 解析器开发指南

TableRAG Enhanced 解析器模块的详细开发和使用指南。

## 架构概览

### 核心组件
```
parser/src/
├── main.py                    # 主入口
├── core/                      # 核心模块
│   ├── pipeline.py           # 解析流程控制器
│   ├── parse_manager.py      # 解析器管理器
│   ├── file_manager.py       # 文件管理器
│   ├── result_manager.py     # 结果管理器
│   └── parsers/              # 解析器实现
│       ├── base.py           # 解析器基类
│       ├── kdc_parser.py     # KDC解析器
│       ├── monkey_ocr_parser.py  # MonkeyOCR解析器
│       ├── vl_llm_parser.py  # VL LLM解析器
│       ├── monkey_ocr_kas_parser.py     # MonkeyOCR（kas）解析器
│       └── ocxflux_parser.py # OCXFlux解析器
```

## 解析器类型

### 1. KDC解析器
**支持格式:**
- `kdc_markdown`: Markdown表格格式
- `kdc_plain`: 纯文本格式
- `kdc_kdc`: KDC原生格式

**配置示例:**
```bash
export PARSER_ENABLED_LIST="kdc_markdown,kdc_plain,kdc_kdc"
```

**特点:**
- 高精度表格识别
- 支持复杂表格结构
- 本地处理，速度快

### 2. MonkeyOCR解析器
**支持格式:**
- `monkey_ocr`: HTML表格格式
- `monkey_ocr_latex`: LaTeX格式
- `monkey_ocr_local`: 本地部署版本

**配置示例:**
```bash
export MONKEY_OCR_TIMEOUT=300
export MONKEY_OCR_LOCAL_PATH="/path/to/monkeyocr"
```

**特点:**
- 开源解决方案
- 支持多种输出格式
- 可本地部署

### 3. VL LLM解析器
**配置示例:**
```bash
export CUSTOM_LLM_ENDPOINT="http://your-llm-api/v1/chat/completions"
export LLM_MAX_TOKENS=12000
export LLM_TEMPERATURE=0.8
```

**特点:**
- 基于视觉语言模型
- 理解复杂表格语义
- 支持自然语言描述

### 4. MonkeyOCR（kas）解析器
**配置示例:**
```bash
export MONKEY_OCR_KAS_API_URL="http://kmd-api.example.com/api"
export MONKEY_OCR_KAS_TIMEOUT=300
```

**特点:**
- 企业级API服务
- 高并发处理能力
- 稳定可靠

### 5. OCXFlux解析器
**特点:**
- 高精度结构识别
- 支持复杂布局
- 快速处理

## 开发新解析器

### 1. 创建解析器类
```python
from .base import BaseParser

class MyParser(BaseParser):
    def __init__(self):
        super().__init__("my_parser")
    
    def parse_image(self, image_path: str, **kwargs) -> dict:
        """
        解析图片并返回结果
        
        Args:
            image_path: 图片文件路径
            **kwargs: 额外参数
            
        Returns:
            dict: 解析结果
        """
        try:
            # 实现解析逻辑
            result = self._do_parse(image_path)
            
            return {
                "success": True,
                "content": result,
                "format": "markdown",  # 或 "html", "json"
                "metadata": {
                    "parser": self.name,
                    "version": "1.0.0"
                }
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "metadata": {
                    "parser": self.name
                }
            }
    
    def _do_parse(self, image_path: str):
        """实际的解析逻辑"""
        # TODO: 实现具体解析算法
        pass
```

### 2. 注册解析器
在 `parse_manager.py` 中添加：
```python
from .parsers.my_parser import MyParser

# 在 PARSER_CLASSES 中添加
PARSER_CLASSES = {
    # ... 其他解析器
    "my_parser": MyParser,
}
```

### 3. 配置解析器
在 `config.env` 中添加：
```bash
export PARSER_ENABLED_LIST="...,my_parser"
```

## 解析流程

### 1. 初始化阶段
```python
# 加载配置
config = load_config()

# 创建解析管理器
parse_manager = ParseManager(config)

# 初始化解析器
parse_manager.initialize_parsers()
```

### 2. 文件处理阶段
```python
# 文件管理器处理输入
file_manager = FileManager(dataset_name)
image_files = file_manager.get_image_files()

# 预处理 (如格式转换)
processed_files = file_manager.preprocess_images(image_files)
```

### 3. 解析执行阶段
```python
# 并行解析
if config.execution_mode == "parallel":
    results = parse_manager.parse_parallel(processed_files)
else:
    results = parse_manager.parse_sequential(processed_files)
```

### 4. 结果处理阶段
```python
# 结果管理器处理输出
result_manager = ResultManager(dataset_name)
result_manager.save_results(results)
result_manager.generate_summary()
```

## 配置选项

### 执行模式
```bash
# 并行执行 (默认)
export PARSER_EXECUTION_MODE=parallel

# 串行执行 (调试用)
export PARSER_EXECUTION_MODE=sequential
export PARSER_SEQUENTIAL_ORDER="kdc_markdown,monkey_ocr,vl_llm"
```

### 重试机制
```bash
export PARSER_RETRY_COUNT=3      # 最大重试次数
export PARSER_RETRY_DELAY=2      # 重试间隔(秒)
```

### 超时设置
```bash
export MONKEY_OCR_TIMEOUT=300    # MonkeyOCR超时
export MONKEY_OCR_KAS_TIMEOUT=300           # MonkeyOCR（kas） API超时
export LLM_TIMEOUT=60            # LLM超时
```

## 错误处理

### 1. 解析失败处理
```python
def handle_parse_error(self, error, image_path):
    """处理解析错误"""
    logger.error(f"解析失败: {image_path}, 错误: {error}")
    
    # 记录错误信息
    error_info = {
        "image": image_path,
        "parser": self.name,
        "error": str(error),
        "timestamp": datetime.now().isoformat()
    }
    
    # 保存错误日志
    self.save_error_log(error_info)
    
    # 返回失败结果
    return {
        "success": False,
        "error": str(error),
        "metadata": {"parser": self.name}
    }
```

### 2. 重试机制
```python
def parse_with_retry(self, image_path, max_retries=3):
    """带重试的解析"""
    for attempt in range(max_retries + 1):
        try:
            return self.parse_image(image_path)
        except Exception as e:
            if attempt == max_retries:
                raise e
            
            # 等待后重试
            time.sleep(2 ** attempt)  # 指数退避
```

## 性能优化

### 1. 并行处理
```python
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor

def parse_parallel(self, image_files):
    """并行解析多个文件"""
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = {
            executor.submit(self.parse_image, img): img 
            for img in image_files
        }
        
        results = {}
        for future in as_completed(futures):
            image = futures[future]
            try:
                result = future.result()
                results[image] = result
            except Exception as e:
                results[image] = {"success": False, "error": str(e)}
        
        return results
```

### 2. 缓存机制
```python
import hashlib
import pickle

def get_cache_key(self, image_path):
    """生成缓存键"""
    with open(image_path, 'rb') as f:
        content = f.read()
    return hashlib.md5(content).hexdigest()

def parse_with_cache(self, image_path):
    """带缓存的解析"""
    cache_key = self.get_cache_key(image_path)
    cache_file = f"cache/{self.name}_{cache_key}.pkl"
    
    # 检查缓存
    if os.path.exists(cache_file):
        with open(cache_file, 'rb') as f:
            return pickle.load(f)
    
    # 执行解析
    result = self.parse_image(image_path)
    
    # 保存缓存
    os.makedirs("cache", exist_ok=True)
    with open(cache_file, 'wb') as f:
        pickle.dump(result, f)
    
    return result
```

## 调试技巧

### 1. 日志配置
```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('parser.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
```

### 2. 性能监控
```python
import time
from functools import wraps

def timing_decorator(func):
    """计时装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger.info(f"{func.__name__} 耗时: {end_time - start_time:.2f}秒")
        return result
    return wrapper

@timing_decorator
def parse_image(self, image_path):
    # 解析逻辑
    pass
```

## 测试指南

### 1. 单元测试
```python
import unittest
from unittest.mock import patch, MagicMock

class TestMyParser(unittest.TestCase):
    def setUp(self):
        self.parser = MyParser()
    
    def test_parse_success(self):
        """测试成功解析"""
        result = self.parser.parse_image("test_image.jpg")
        self.assertTrue(result["success"])
        self.assertIn("content", result)
    
    @patch('requests.post')
    def test_api_failure(self, mock_post):
        """测试API失败情况"""
        mock_post.side_effect = Exception("API Error")
        result = self.parser.parse_image("test_image.jpg")
        self.assertFalse(result["success"])
```

### 2. 集成测试
```bash
# 运行完整解析流程
./table_parser.sh test_dataset

# 验证结果
python tests/test_parser_integration.py
```

## 最佳实践

1. **错误处理**: 总是处理异常并返回标准格式
2. **日志记录**: 记录关键操作和错误信息
3. **配置管理**: 使用环境变量管理配置
4. **性能监控**: 监控解析时间和资源使用
5. **测试覆盖**: 编写充分的单元测试和集成测试
6. **文档更新**: 及时更新API文档和使用说明
