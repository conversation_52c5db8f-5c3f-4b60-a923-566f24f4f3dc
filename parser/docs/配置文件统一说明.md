# 配置文件统一说明

## 概述

项目已经统一使用 `config.env` 作为唯一的配置文件，不再使用多个配置文件（如 `.env`、`.env.example` 等）。

## 配置文件位置

```
config.env  (项目根目录)
```

## 配置文件内容

`config.env` 包含以下几个部分的配置：

### 1. KS3 配置
```bash
export KS3_AK=AKLTV3HRQgmmSnm94aEKSo5A
export KS3_SK=OJx3DQrRgDULwK7VbnCg83hlPk47Jksxc9OgdE9V
export KS3_HOST=ks3-cn-beijing.ksyun.com
export KS3_BUCKET=test-platform-qa
export KS3_PROXY_HOST=127.0.0.1
export KS3_PROXY_PORT=18899
```

### 2. 项目路径配置
```bash
export PROJECT_ROOT_DIR=/data/projects/kingsoft/personal/workspace/tablerag/enhance
export PYTHON_VENV_PATH=/data/deploy/pyvenvs/pyldu/bin/activate
export SSL_CERT_PATH=/etc/ssl/certs/ca-certificates.crt
```

### 3. WPS配置
```bash
export WPS_AK=AK20230407UWDQSR
export WPS_SK=a9560a5967c5aaf44c9d8885abbd000c
```

### 4. 代理配置
```bash
export http_proxy=http://localhost:18899
export https_proxy=http://localhost:18899
```

### 5. LLM配置
```bash
export LLM_SERVICE_TYPE=custom
export CUSTOM_LLM_ENDPOINT=http://kmd-api.kas.wps.cn/api/11329-v1/HgDolg/v1/chat/completions
export CUSTOM_LLM_USER_AGENT="Apifox/1.0.0 (https://apifox.com)"
export CUSTOM_LLM_HOST=kmd-api.kas.wps.cn
export LLM_USE_PROXY=true
export LLM_MAX_TOKENS=2000
export LLM_TEMPERATURE=0.8
export LLM_TIMEOUT=60
export LLM_RETRY_COUNT=3
export LLM_RETRY_DELAY=1
```

### 6. 测试集配置
```bash
export DATASET_NAME=kingsoft
```

### 7. MonkeyOCR配置
```bash
export MONKEY_OCR_TIMEOUT=300
```

### 9. 表格生成配置
```bash
export TABLE_DEFAULT_ROWS=5
export TABLE_DEFAULT_COLS=4
```

## 使用方法

### 1. 在脚本中加载配置

所有脚本（`table_parser.sh`、`unit_test.sh` 等）都会自动加载 `../config.env`：

```bash
# 加载公用配置
if [ -f "../config.env" ]; then
    echo "✓ 发现配置文件，加载环境变量..."
    source ../config.env
else
    echo "❌ 未找到配置文件，请确保../config.env存在"
    exit 1
fi
```

### 2. 手动加载配置

```bash
# 在项目根目录
source config.env

# 或在parser目录
source ../config.env
```

### 3. 验证配置加载

```bash
# 在项目根目录
source config.env
echo "LLM端点: $CUSTOM_LLM_ENDPOINT"
echo "MonkeyOCR超时: $MONKEY_OCR_TIMEOUT 秒"
```

## 配置修改

### 1. 编辑配置文件

```bash
vim config.env  # 在项目根目录
```

### 2. 常见配置修改

- **修改MonkeyOCR超时时间**:
  ```bash
  export MONKEY_OCR_TIMEOUT=600  # 改为10分钟
  ```

- **修改测试集**:
  ```bash
  export DATASET_NAME=new_dataset
  ```

- **修改LLM端点**:
  ```bash
  export CUSTOM_LLM_ENDPOINT=http://new-endpoint.com/v1/chat/completions
  ```

## 迁移说明

### 从多配置文件迁移

如果您之前使用多个配置文件（`.env`、`.env.example` 等），现在已经统一到 `config.env`：

1. **已删除的文件**:
   - `parser/config.env` (已合并到根目录)
   - `src/.env`
   - `src/.env.example`

2. **统一的配置文件**:
   - `config.env` (项目根目录，包含所有必要配置)

3. **迁移完成**: 所有配置已经合并到 `config.env` 中，无需手动操作

## 注意事项

1. **版本控制**: `config.env` 包含敏感信息，请注意保护
2. **环境隔离**: 不同环境可以使用不同的 `config.env` 文件
3. **配置检查**: 运行脚本前确保 `config.env` 文件存在且配置正确
4. **统一性**: 所有配置都在一个文件中，便于管理和维护 