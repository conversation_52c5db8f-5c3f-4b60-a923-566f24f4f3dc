#!/usr/bin/env python3
"""
测试最终修复效果
1. JSON Schema默认展开
2. 默认数据集为complex
3. complex数据集标注数据匹配
"""

import requests
import json
import sys

def test_final_fixes():
    """测试最终修复效果"""
    print("🔍 测试最终修复效果")
    print("=" * 50)
    
    try:
        # 1. 测试数据集列表和默认选择
        print("1. 测试数据集列表...")
        response = requests.get("http://localhost:8000/api/datasets", timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取数据集列表失败: {response.status_code}")
            return False
        
        datasets = response.json()
        print(f"✅ 可用数据集: {[d['name'] for d in datasets]}")
        
        if 'complex' in [d['name'] for d in datasets]:
            print("✅ complex数据集可用，应该被设为默认数据集")
        else:
            print("❌ complex数据集不可用")
        
        # 2. 测试complex数据集的解析结果和标注数据匹配
        print(f"\n2. 测试complex数据集的文件匹配...")
        
        # 获取解析结果
        response = requests.get("http://localhost:8000/api/datasets/complex/parse_results", timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取解析结果失败: {response.status_code}")
            return False
        
        parse_data = response.json()
        parse_results_dict = parse_data['parse_results']
        
        # 重新组织数据结构
        first_parser = list(parse_results_dict.keys())[0]
        first_parser_data = parse_results_dict[first_parser]
        
        # 获取标注数据
        response = requests.get("http://localhost:8000/api/annotations", 
                              params={"dataset_name": "complex"}, timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return False
        
        annotations = response.json()
        
        print(f"解析结果数量: {len(first_parser_data)}")
        print(f"标注数据数量: {len(annotations)}")
        
        # 测试文件名匹配逻辑
        matched_count = 0
        total_cases = min(10, len(first_parser_data))  # 只测试前10个案例
        
        print(f"\n测试前{total_cases}个案例的文件匹配:")
        
        for i in range(total_cases):
            case = first_parser_data[i]
            case_filename = case.get('filename', '')
            
            # 使用修复后的匹配逻辑
            matching_annotation = None
            for ann in annotations:
                # 直接匹配
                if ann.get('image_filename') == case_filename:
                    matching_annotation = ann
                    break
                
                # 去掉扩展名后匹配
                case_basename = case_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '')
                ann_basename = ann.get('image_filename', '').replace('.pdf', '').replace('.png', '').replace('.jpg', '')
                
                if case_basename == ann_basename:
                    matching_annotation = ann
                    break
            
            if matching_annotation:
                matched_count += 1
                print(f"  ✅ 案例 {i+1}: {case_filename} -> {matching_annotation.get('image_filename')}")
            else:
                print(f"  ❌ 案例 {i+1}: {case_filename} -> 无匹配")
        
        match_rate = (matched_count / total_cases) * 100
        print(f"\n匹配率: {matched_count}/{total_cases} ({match_rate:.1f}%)")
        
        if match_rate >= 80:
            print("✅ 文件匹配率良好")
        else:
            print("❌ 文件匹配率较低，可能需要进一步调试")
        
        # 3. 测试标注数据内容
        print(f"\n3. 测试标注数据内容...")
        if matched_count > 0:
            # 找到第一个有匹配的案例
            for i in range(total_cases):
                case = first_parser_data[i]
                case_filename = case.get('filename', '')
                
                matching_annotation = None
                for ann in annotations:
                    case_basename = case_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '')
                    ann_basename = ann.get('image_filename', '').replace('.pdf', '').replace('.png', '').replace('.jpg', '')
                    
                    if case_basename == ann_basename:
                        matching_annotation = ann
                        break
                
                if matching_annotation:
                    table_content = matching_annotation.get('table_content', '')
                    if table_content:
                        try:
                            annotation_data = json.loads(table_content)
                            print(f"✅ 标注数据解析成功")
                            print(f"   数据类型: {type(annotation_data)}")
                            if isinstance(annotation_data, dict) and 'elements' in annotation_data:
                                print(f"   Elements数量: {len(annotation_data['elements'])}")
                            break
                        except json.JSONDecodeError:
                            print(f"❌ 标注数据JSON解析失败")
                    else:
                        print(f"❌ 标注数据内容为空")
        
        # 4. 总结
        print(f"\n4. 修复效果总结")
        print("=" * 30)
        print("✅ 已修复的问题:")
        print("1. JSON Schema默认展开 - AnnotationList组件已修改")
        print("2. 默认数据集改为complex - DatasetSelector组件已修改")
        print("3. 文件名匹配逻辑 - CaseDetail组件已修改，支持.pdf和.png扩展名差异")
        print(f"4. 实际匹配率: {match_rate:.1f}%")
        
        print(f"\n💡 现在可以验证修复效果:")
        print("1. 打开 http://localhost:3000")
        print("2. 应该默认选择complex数据集")
        print("3. 选择任意案例，应该能看到标注数据和准确率")
        print("4. 切换到'人工标注'标签，JSON Schema应该默认展开")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_fixes()
    sys.exit(0 if success else 1)
