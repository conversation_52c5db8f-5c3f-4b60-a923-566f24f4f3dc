#!/usr/bin/env python3
"""
调试文件名匹配问题
"""

import json
import requests

def get_parse_results():
    """获取解析结果"""
    try:
        response = requests.get('http://localhost:8000/api/datasets/complex/parse_results')
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取解析结果失败: {response.status_code}")
            return {}
    except Exception as e:
        print(f"❌ 获取解析结果异常: {e}")
        return {}

def get_image_list():
    """获取图片列表"""
    try:
        response = requests.get('http://localhost:8000/api/datasets/complex/images')
        if response.status_code == 200:
            data = response.json()
            return [img['filename'] for img in data]
        else:
            print(f"❌ 获取图片列表失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取图片列表异常: {e}")
        return []

def process_parse_results(parse_data, image_list):
    """模拟前端的processParseResults函数"""
    if not parse_data:
        return []

    # 从parse_results对象中提取实际的解析结果
    parse_results = parse_data.get('parse_results', {})
    processed_files = parse_data.get('processed_files', [])

    print(f"解析结果包含的解析器: {list(parse_results.keys())}")
    print(f"处理的文件数量: {len(processed_files)}")

    # 基于图片列表创建案例
    cases = []
    for index, image_name in enumerate(image_list):
        base_name = image_name.replace('.png', '').replace('.jpg', '').replace('.jpeg', '')

        case_data = {
            'id': f'case_{index}',
            'index': index + 1,
            'fileName': image_name,
            'baseName': base_name
        }

        cases.append(case_data)

    return cases

def get_annotations():
    """获取标注数据"""
    try:
        response = requests.get('http://localhost:8000/api/annotations?dataset_name=complex&limit=200')
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取标注数据异常: {e}")
        return []

def check_filename_matching():
    """检查文件名匹配情况"""
    print("=== 检查文件名匹配情况 ===")

    # 获取数据
    parse_data = get_parse_results()
    image_list = get_image_list()
    annotations = get_annotations()

    if not parse_data:
        print("❌ 无解析结果")
        return

    if not annotations:
        print("❌ 无标注数据")
        return

    print(f"✅ 图片列表: {len(image_list)} 个")
    print(f"✅ 标注数据: {len(annotations)} 个")

    # 处理解析结果
    cases = process_parse_results(parse_data, image_list)
    print(f"✅ 生成案例: {len(cases)} 个")
    
    # 创建标注文件名集合
    annotation_filenames = set()
    annotation_basenames = set()
    
    for ann in annotations:
        filename = ann.get('image_filename', '')
        annotation_filenames.add(filename)
        
        # 去掉扩展名
        basename = filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
        annotation_basenames.add(basename)
    
    print(f"\n标注文件名示例:")
    for i, filename in enumerate(list(annotation_filenames)[:5]):
        print(f"  {i+1}. {filename}")
    
    # 检查前10个案例的匹配情况
    print(f"\n=== 检查前10个案例的匹配情况 ===")

    cases_to_check = cases[:10] if len(cases) >= 10 else cases
    for i, case in enumerate(cases_to_check):
        case_filename = case.get('fileName', '')
        case_basename = case_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
        
        # 直接匹配
        direct_match = case_filename in annotation_filenames
        
        # 基名匹配
        basename_match = case_basename in annotation_basenames
        
        # 查找具体匹配的标注
        matching_annotation = None
        for ann in annotations:
            ann_filename = ann.get('image_filename', '')
            ann_basename = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
            
            if ann_filename == case_filename or ann_basename == case_basename:
                matching_annotation = ann
                break
        
        status = "✅" if matching_annotation else "❌"
        print(f"{status} 案例 {i+1}: {case_filename}")
        print(f"    基名: {case_basename}")
        print(f"    直接匹配: {direct_match}")
        print(f"    基名匹配: {basename_match}")
        if matching_annotation:
            print(f"    匹配标注: {matching_annotation['image_filename']} (ID: {matching_annotation['id']})")
        print()

def check_specific_cases():
    """检查第2个和第3个案例"""
    print("=== 检查第2个和第3个案例 ===")

    parse_data = get_parse_results()
    image_list = get_image_list()
    annotations = get_annotations()
    cases = process_parse_results(parse_data, image_list)

    if len(cases) < 3:
        print("❌ 案例不足3个")
        return
    
    # 检查第2个案例
    case2 = cases[1]
    case2_filename = case2.get('fileName', '')
    print(f"\n第2个案例: {case2_filename}")

    matching_ann2 = None
    for ann in annotations:
        ann_filename = ann.get('image_filename', '')
        ann_basename = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
        case_basename = case2_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')

        if ann_filename == case2_filename or ann_basename == case_basename:
            matching_ann2 = ann
            break

    if matching_ann2:
        print(f"  ✅ 找到标注: {matching_ann2['image_filename']} (ID: {matching_ann2['id']})")
    else:
        print(f"  ❌ 未找到标注")

    # 检查第3个案例
    case3 = cases[2]
    case3_filename = case3.get('fileName', '')
    print(f"\n第3个案例: {case3_filename}")

    matching_ann3 = None
    for ann in annotations:
        ann_filename = ann.get('image_filename', '')
        ann_basename = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
        case_basename = case3_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')

        if ann_filename == case3_filename or ann_basename == case_basename:
            matching_ann3 = ann
            break

    if matching_ann3:
        print(f"  ✅ 找到标注: {matching_ann3['image_filename']} (ID: {matching_ann3['id']})")
    else:
        print(f"  ❌ 未找到标注")

def main():
    print("=== 调试文件名匹配问题 ===")
    
    check_filename_matching()
    check_specific_cases()
    
    print("\n=== 调试完成 ===")

if __name__ == '__main__':
    main()
