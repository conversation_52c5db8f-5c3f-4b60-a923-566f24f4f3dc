#!/usr/bin/env python3
"""
测试complex数据集第129个案例的准确率计算问题
"""

import requests
import json
import sys

def test_case_129():
    """测试第29个案例"""
    print("🔍 测试complex数据集第29个案例")
    print("=" * 50)
    
    try:
        # 1. 获取complex数据集的解析结果
        print("1. 获取complex数据集的解析结果...")
        response = requests.get("http://localhost:8000/api/datasets/complex/parse_results", timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取解析结果失败: {response.status_code}")
            return False
        
        parse_data = response.json()
        parse_results_dict = parse_data['parse_results']
        
        # 重新组织数据结构
        first_parser = list(parse_results_dict.keys())[0]
        first_parser_data = parse_results_dict[first_parser]
        
        if len(first_parser_data) < 29:
            print(f"❌ 数据不足，只有{len(first_parser_data)}个案例")
            return False

        # 获取第29个案例（索引28）
        case_index = 28
        case_129 = {}
        
        for parser_key, parser_data in parse_results_dict.items():
            if case_index < len(parser_data):
                case_129[parser_key] = parser_data[case_index]
        
        filename = case_129[first_parser].get('filename', f'case_{case_index}')
        print(f"✅ 第29个案例文件名: {filename}")
        
        # 2. 获取标注数据
        print(f"\n2. 获取complex数据集的标注数据...")
        response = requests.get("http://localhost:8000/api/annotations", 
                              params={"dataset_name": "complex"}, timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return False
        
        annotations = response.json()
        
        # 查找匹配的标注数据
        matching_annotation = None
        filename_base = filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '')
        
        for ann in annotations:
            ann_filename = ann.get('image_filename', '')
            ann_base = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '')
            if ann_base == filename_base:
                matching_annotation = ann
                break
        
        if not matching_annotation:
            print(f"❌ 未找到匹配的标注数据")
            return False
        
        print(f"✅ 找到匹配的标注数据: {matching_annotation['image_filename']}")
        
        # 3. 分析各解析器的结果
        print(f"\n3. 分析各解析器的结果...")
        
        for parser_key, result in case_129.items():
            print(f"\n--- {parser_key} ---")
            print(f"文件名: {result.get('filename', 'N/A')}")
            print(f"解析器名称: {result.get('parser_name', 'N/A')}")
            print(f"成功状态: {result.get('success', 'N/A')}")
            
            # 检查结果内容
            if 'result' in result:
                result_data = result['result']
                if isinstance(result_data, dict):
                    print(f"结果键: {list(result_data.keys())}")
                    
                    # 检查具体内容
                    if 'html' in result_data:
                        html_content = result_data['html']
                        if html_content:
                            print(f"HTML内容长度: {len(html_content)}")
                            print(f"HTML预览: {html_content[:100]}...")
                        else:
                            print("HTML内容为空")
                    
                    if 'markdown' in result_data:
                        md_content = result_data['markdown']
                        if md_content:
                            print(f"Markdown内容长度: {len(md_content)}")
                            print(f"Markdown预览: {md_content[:100]}...")
                        else:
                            print("Markdown内容为空")
                    
                    if 'data' in result_data:
                        data_content = result_data['data']
                        if data_content:
                            print(f"Data内容类型: {type(data_content)}")
                            if isinstance(data_content, list) and len(data_content) > 0:
                                print(f"Data第一个元素键: {list(data_content[0].keys()) if isinstance(data_content[0], dict) else 'N/A'}")
                        else:
                            print("Data内容为空")
                else:
                    print(f"结果类型: {type(result_data)}")
                    print(f"结果内容: {str(result_data)[:100]}...")
            else:
                print("无result字段")
        
        # 4. 分析标注数据
        print(f"\n4. 分析标注数据...")
        table_content = matching_annotation.get('table_content', '')
        print(f"标注内容长度: {len(table_content)}")
        
        if table_content:
            try:
                annotation_data = json.loads(table_content)
                print(f"标注数据类型: {type(annotation_data)}")
                if isinstance(annotation_data, dict):
                    print(f"标注数据键: {list(annotation_data.keys())}")
                    if 'elements' in annotation_data:
                        print(f"Elements数量: {len(annotation_data['elements'])}")
                elif isinstance(annotation_data, list):
                    print(f"标注数据数组长度: {len(annotation_data)}")
                
                print(f"标注数据预览: {str(annotation_data)[:200]}...")
            except json.JSONDecodeError as e:
                print(f"标注数据JSON解析失败: {e}")
                print(f"原始内容预览: {table_content[:200]}...")
        
        # 5. 检查MonkeyOCR(parse)的具体情况
        print(f"\n5. 检查MonkeyOCR(parse)的具体情况...")
        if 'monkey_ocr' in case_129:
            monkey_result = case_129['monkey_ocr']
            print(f"MonkeyOCR(parse)详细信息:")
            print(f"  成功状态: {monkey_result.get('success', 'N/A')}")
            
            if 'result' in monkey_result:
                result_data = monkey_result['result']
                print(f"  结果类型: {type(result_data)}")
                
                if isinstance(result_data, dict):
                    print(f"  结果键: {list(result_data.keys())}")
                    
                    # 检查HTML内容
                    html_content = result_data.get('html', '')
                    if html_content:
                        print(f"  HTML内容存在，长度: {len(html_content)}")
                        print(f"  HTML内容预览: {html_content[:150]}...")
                        
                        # 检查是否包含表格
                        if '<table' in html_content.lower():
                            print("  ✅ HTML包含表格标签")
                        else:
                            print("  ❌ HTML不包含表格标签")
                    else:
                        print("  ❌ HTML内容为空")
                    
                    # 检查其他字段
                    for key, value in result_data.items():
                        if key != 'html' and value:
                            print(f"  {key}: {str(value)[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_case_129()
    sys.exit(0 if success else 1)
