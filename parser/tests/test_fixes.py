#!/usr/bin/env python3
"""
测试修复后的功能
1. MonkeyOCR(parse)的详细比对表格
2. 准确率计算的一致性
3. TEDS详细分析
4. JSON Schema默认展开
"""

import requests
import json
import sys

def test_fixes():
    """测试修复后的功能"""
    print("🔍 测试修复后的功能")
    print("=" * 50)
    
    try:
        # 1. 获取complex数据集的解析结果
        print("1. 获取complex数据集的解析结果...")
        response = requests.get("http://localhost:8000/api/datasets/complex/parse_results", timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取解析结果失败: {response.status_code}")
            return False
        
        parse_data = response.json()
        parse_results_dict = parse_data['parse_results']
        
        # 重新组织数据结构
        first_parser = list(parse_results_dict.keys())[0]
        first_parser_data = parse_results_dict[first_parser]
        
        # 获取第29个案例（索引28）
        case_index = 28
        case_29 = {}
        
        for parser_key, parser_data in parse_results_dict.items():
            if case_index < len(parser_data):
                case_29[parser_key] = parser_data[case_index]
        
        filename = case_29[first_parser].get('filename', f'case_{case_index}')
        print(f"✅ 测试案例: {filename}")
        
        # 2. 获取标注数据
        print(f"\n2. 获取标注数据...")
        response = requests.get("http://localhost:8000/api/annotations", 
                              params={"dataset_name": "complex"}, timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return False
        
        annotations = response.json()
        
        # 查找匹配的标注数据
        matching_annotation = None
        filename_base = filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '')
        
        for ann in annotations:
            ann_filename = ann.get('image_filename', '')
            ann_base = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '')
            if ann_base == filename_base:
                matching_annotation = ann
                break
        
        if not matching_annotation:
            print(f"❌ 未找到匹配的标注数据")
            return False
        
        print(f"✅ 找到匹配的标注数据")
        
        # 3. 验证MonkeyOCR(parse)数据结构
        print(f"\n3. 验证MonkeyOCR(parse)数据结构...")
        
        # 检查monkey_ocr键
        if 'monkey_ocr' in case_29:
            monkey_result = case_29['monkey_ocr']
            print(f"✅ 找到monkey_ocr数据")
            print(f"   成功状态: {monkey_result.get('success', 'N/A')}")
            
            if 'result' in monkey_result and 'html' in monkey_result['result']:
                html_content = monkey_result['result']['html']
                print(f"   HTML内容长度: {len(html_content)}")
                print(f"   包含表格: {'<table' in html_content.lower()}")
            else:
                print(f"   ❌ 没有HTML内容")
        else:
            print(f"❌ 没有找到monkey_ocr数据")
        
        # 4. 验证标注数据结构
        print(f"\n4. 验证标注数据结构...")
        table_content = matching_annotation.get('table_content', '')
        if table_content:
            try:
                annotation_data = json.loads(table_content)
                print(f"✅ 标注数据解析成功")
                print(f"   数据类型: {type(annotation_data)}")
                if isinstance(annotation_data, dict) and 'elements' in annotation_data:
                    print(f"   Elements数量: {len(annotation_data['elements'])}")
                    
                    # 查找表格元素
                    table_elements = [el for el in annotation_data['elements'] if el.get('type') == 'table']
                    if table_elements:
                        print(f"   表格元素数量: {len(table_elements)}")
                        table_el = table_elements[0]
                        if 'rows' in table_el:
                            print(f"   表格行数: {len(table_el['rows'])}")
                        else:
                            print(f"   ❌ 表格元素没有rows字段")
                    else:
                        print(f"   ❌ 没有找到表格元素")
                else:
                    print(f"   ❌ 标注数据格式不正确")
            except json.JSONDecodeError as e:
                print(f"❌ 标注数据JSON解析失败: {e}")
        else:
            print(f"❌ 没有标注内容")
        
        # 5. 测试建议
        print(f"\n5. 测试建议")
        print("=" * 30)
        print("现在可以在analyzer界面中验证以下修复:")
        print("1. 打开 http://localhost:3000")
        print("2. 选择数据集: complex")
        print(f"3. 选择案例: {filename}")
        print("4. 检查MonkeyOCR(parse)的解析报告:")
        print("   - 应该显示准确率指标")
        print("   - 应该显示详细比对表格")
        print("   - 应该显示TEDS详细分析")
        print("5. 检查人工标注数据:")
        print("   - JSON Schema应该默认展开")
        print("   - 不应该有展开/折叠按钮")
        print("6. 验证准确率一致性:")
        print("   - 解析报告中的准确率")
        print("   - 指标数据中的准确率")
        print("   - 应该显示相同的数值")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_fixes()
    sys.exit(0 if success else 1)
