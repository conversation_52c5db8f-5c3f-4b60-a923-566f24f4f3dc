# Parser Tests

解析器测试套件，包含各种解析器的单元测试、集成测试和修复验证。

## 测试文件说明

### 解析器功能测试
- `test_real_retry.py` - 真实重试功能测试
- `test_retry_functionality.py` - 重试功能测试
- `test_vl_llm_retry.py` - VL LLM重试测试

### 修复验证测试
- `test_all_fixes.py` - 所有修复的综合测试
- `test_case_129.py` - 特定案例129的测试
- `test_final_fixes.py` - 最终修复验证测试
- `test_fixes.py` - 修复功能测试

### 调试工具
- `debug_filename_matching.py` - 文件名匹配调试

### 解析器配置测试
- `test_parser_config.py` - 解析器配置测试

## 运行测试

### 运行所有测试
```bash
cd parser
./unit_test.sh
```

### 运行特定测试
```bash
# 重试功能测试
python tests/test_retry_functionality.py

# VL LLM测试
python tests/test_vl_llm_retry.py

# 修复验证
python tests/test_all_fixes.py

# 调试文件名匹配
python tests/debug_filename_matching.py
```

### 解析器集成测试
```bash
# 运行完整解析流程
./table_parser.sh test_dataset

# 检查解析结果
python tests/test_parser_config.py
```

## 测试环境要求

### 依赖服务
- KDC解析服务
- MonkeyOCR服务 (本地/远程)
- VL LLM服务
- KMD API服务

### 环境变量
确保config.env中配置了正确的：
- `PARSER_ENABLED_LIST` - 启用的解析器列表
- `PARSER_EXECUTION_MODE` - 执行模式 (parallel/sequential)
- `PARSER_RETRY_COUNT` - 重试次数
- `PARSER_RETRY_DELAY` - 重试延迟

### 测试数据
- 测试图片文件在 `dataset/*/images/`
- 解析结果存储在 `parse_results/*/`

## 解析器说明

### 支持的解析器
1. **KDC解析器**
   - `kdc_markdown` - Markdown格式输出
   - `kdc_plain` - 纯文本格式输出
   - `kdc_kdc` - KDC原生格式输出

2. **MonkeyOCR解析器**
   - `monkey_ocr` - HTML格式输出
   - `monkey_ocr_latex` - LaTeX格式输出
   - `monkey_ocr_local` - 本地部署版本

3. **其他解析器**
   - `vl_llm` - 视觉语言模型解析
   - `monkey_ocr_kas` - MonkeyOCR（kas） API解析
   - `ocxflux` - OCXFlux解析

## 注意事项

1. 某些测试需要外部服务可用
2. 网络解析器测试可能受网络状况影响
3. 大数据集测试需要较长时间
4. 并行模式下资源占用较高
5. 重试测试会模拟失败场景
