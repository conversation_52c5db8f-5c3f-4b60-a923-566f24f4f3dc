#!/usr/bin/env python3
"""
测试真实环境下的重试功能

这个脚本用于测试在真实解析环境中重试功能是否正常工作
"""

import os
import sys
import logging

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from core.parse_manager import ParseManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_single_parser_retry():
    """测试单个解析器的重试功能"""
    logger.info("=" * 50)
    logger.info("测试单个解析器重试功能")
    logger.info("=" * 50)
    
    # 设置重试配置
    os.environ["PARSER_RETRY_COUNT"] = "2"
    os.environ["PARSER_RETRY_DELAY"] = "1"
    
    # 创建解析管理器并注册默认解析器
    manager = ParseManager().create_default_parsers()
    
    # 测试文件信息 - 使用一个可能不存在的文件来触发失败
    file_info = {
        "filename": "test_retry.pdf",
        "file_id": "test_retry_001",
        "original_image": "test_retry.png",
        "image_path": "/nonexistent/path/test_retry.png"  # 不存在的路径
    }
    
    # 测试MonkeyOCR解析器（最容易失败的）
    logger.info("测试MonkeyOCR解析器重试...")
    try:
        result = manager.parse_with_parser_retry("monkey_ocr", file_info)
        logger.info(f"MonkeyOCR解析结果: success={result.get('success')}")
        if not result.get('success'):
            logger.info(f"失败原因: {result.get('error', '未知')}")
    except Exception as e:
        logger.error(f"MonkeyOCR解析异常: {e}")
    
    logger.info("单个解析器重试测试完成")


def test_multiple_parsers_retry():
    """测试多路解析器的重试功能"""
    logger.info("=" * 50)
    logger.info("测试多路解析器重试功能")
    logger.info("=" * 50)
    
    # 设置重试配置
    os.environ["PARSER_RETRY_COUNT"] = "2"
    os.environ["PARSER_RETRY_DELAY"] = "1"
    os.environ["PARSER_EXECUTION_MODE"] = "sequential"
    
    # 创建解析管理器并注册默认解析器
    manager = ParseManager().create_default_parsers()
    
    # 测试文件信息 - 使用一个可能不存在的文件来触发失败
    file_info = {
        "filename": "test_multi_retry.pdf",
        "file_id": "test_multi_retry_001",
        "original_image": "test_multi_retry.png",
        "image_path": "/nonexistent/path/test_multi_retry.png"  # 不存在的路径
    }
    
    # 只测试容易失败的解析器
    test_parsers = ["monkey_ocr", "monkey_ocr_latex"]
    
    logger.info(f"测试多路解析器重试: {test_parsers}")
    try:
        results = manager.parse_with_multiple_parsers(
            file_info, 
            parser_names=test_parsers,
            execution_mode="sequential"
        )
        
        logger.info("多路解析结果:")
        for parser_name, result in results.items():
            success = result.get('success', False)
            error = result.get('error', '无错误信息')
            logger.info(f"  {parser_name}: success={success}")
            if not success:
                logger.info(f"    错误: {error}")
                
    except Exception as e:
        logger.error(f"多路解析异常: {e}")
    
    logger.info("多路解析器重试测试完成")


def main():
    """主测试函数"""
    logger.info("开始测试真实环境下的重试功能")
    
    # 显示当前重试配置
    retry_count = os.getenv("PARSER_RETRY_COUNT", "3")
    retry_delay = os.getenv("PARSER_RETRY_DELAY", "2")
    logger.info(f"当前重试配置: 最大重试次数={retry_count}, 重试间隔={retry_delay}秒")
    
    try:
        test_single_parser_retry()
        test_multiple_parsers_retry()
        
        logger.info("=" * 50)
        logger.info("🎉 真实环境重试功能测试完成")
        logger.info("=" * 50)
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
