# TableRAG Enhanced - 表格解析与标注系统

一个完整的表格解析、分析和人工标注系统，支持多种OCR解析器的结果对比和准确率评估。

## 🏗️ 系统架构

```
TableRAG Enhanced
├── parser/              # 表格解析引擎
│   ├── src/            # 解析器核心代码
│   ├── scripts/        # 解析脚本
│   └── tests/          # 解析器测试
├── analyzer/           # React前端分析界面
│   ├── src/            # 前端源码
│   ├── public/         # 静态资源
│   └── tests/          # 前端测试
├── backend/            # 统一后端服务
│   ├── api/            # RESTful API
│   ├── database/       # 数据库模块
│   ├── services/       # 业务逻辑
│   └── tests/          # 后端测试
├── generator/          # 表格数据生成器
│   ├── src/            # 生成器源码
│   └── tests/          # 生成器测试
├── dataset/            # 测试数据集
├── parse_results/      # 解析结果存储
├── docs/               # 项目文档
└── scripts/            # 工具脚本
```

## 🚀 核心功能

### 1. 多路表格解析 (Parser)
- **KDC解析器**: 支持Markdown、Plain、KDC三种格式输出
- **MonkeyOCR解析器**: 支持HTML和LaTeX格式，本地和远程部署
- **VL LLM解析器**: 基于视觉语言模型的智能解析
- **MonkeyOCR（kas）解析器**: 集成MonkeyOCR（kas） API的解析服务
- **OCXFlux解析器**: 高精度表格结构识别

### 2. 智能分析界面 (Analyzer)
- **React现代化界面**: 响应式设计，支持移动端
- **实时结果对比**: 多解析器结果并排展示
- **图片懒加载**: 优化大数据集的加载性能
- **虚拟滚动**: 处理大量数据时保持流畅体验
- **准确率可视化**: 图表展示各解析器性能对比

### 3. 统一后端服务 (Backend)
- **RESTful API**: 标准化的数据访问接口
- **数据集管理**: 自动发现和索引测试数据
- **人工标注**: 支持Markdown/HTML格式的标准答案
- **准确率评估**: 自动计算结构和内容准确率
- **MySQL数据库**: 可靠的数据持久化存储

### 4. 数据生成器 (Generator)
- **LLM驱动生成**: 使用大语言模型生成多样化表格
- **复杂表格支持**: 支持合并单元格、多层表头等复杂结构
- **批量生成**: 支持大规模数据集的自动化生成
- **质量控制**: 内置数据质量检查和过滤机制

## 🛠️ 技术栈

### 前端技术
- **React 18**: 现代化前端框架
- **Material-UI**: 组件库和设计系统
- **Chart.js**: 数据可视化
- **Axios**: HTTP客户端

### 后端技术
- **FastAPI**: 高性能Python Web框架
- **SQLAlchemy**: ORM数据库操作
- **MySQL**: 主数据库
- **Uvicorn**: ASGI服务器

### 解析技术
- **KDC**: 金山文档表格识别引擎
- **MonkeyOCR**: 开源表格OCR解决方案
- **VL LLM**: 视觉语言模型
- **Gradio**: 模型服务接口

## 📦 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- MySQL 8.0+
- 4GB+ RAM

### 一键启动
```bash
# 克隆项目
git clone <repository-url>
cd tablerag-enhance

# 一键启动所有服务
./start_tablerag.sh
```

### 手动启动

#### 1. 启动后端服务
```bash
cd backend
pip install -r requirements.txt
python main.py
```

#### 2. 启动前端服务
```bash
cd analyzer
npm install
npm start
```

#### 3. 运行解析任务
```bash
cd parser
./table_parser.sh kingsoft  # 使用kingsoft数据集
```

### 访问地址
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/docs

## 📋 使用指南

### 1. 数据集管理
```bash
# 创建新数据集
mkdir dataset/my_dataset
mkdir dataset/my_dataset/images

# 添加图片文件到images目录
cp *.jpg dataset/my_dataset/images/

# 运行解析
cd parser
./table_parser.sh my_dataset
```

### 2. 查看解析结果
1. 打开前端界面: http://localhost:3000
2. 选择数据集
3. 查看各解析器的结果对比
4. 分析准确率统计

### 3. 人工标注
1. 切换到"人工标注"标签页
2. 选择图片创建标注
3. 输入标准答案（支持Markdown/HTML）
4. 保存并设置标注状态

### 4. 准确率评估
1. 完成人工标注后
2. 系统自动计算各解析器准确率
3. 查看详细的对比分析报告

## 🔧 配置说明

### 环境变量配置 (config.env)
```bash
# 数据库配置
export DATABASE_URL="mysql+pymysql://user:pass@localhost:3306/tablerag"

# 服务端口配置（远程开发环境固定端口）
export BACKEND_PORT=8000    # 后端服务端口（包含静态文件服务）
export FRONTEND_PORT=3000   # 前端服务端口

# 解析器配置
export PARSER_ENABLED_LIST="kdc_markdown,monkey_ocr,vl_llm"
export PARSER_EXECUTION_MODE="parallel"

# LLM配置
export CUSTOM_LLM_ENDPOINT="http://your-llm-api/v1/chat/completions"
```

### 远程开发环境
本项目支持SSH远程开发，所有服务端口都是固定的：
- 前端: 3000 (通过SSH隧道访问)
- 后端: 8000 (通过SSH隧道访问，包含静态文件服务)

SSH隧道设置示例：
```bash
ssh -L 3000:localhost:3000 -L 8000:localhost:8000 user@remote-server
```

## 📊 性能特性

- **并行解析**: 多解析器并行执行，提升处理速度
- **增量更新**: 支持增量解析，避免重复计算
- **内存优化**: 大数据集的内存友好处理
- **缓存机制**: 智能缓存减少重复请求
- **错误恢复**: 自动重试和错误恢复机制

## 🧪 测试

### 运行所有测试
```bash
# 后端测试
cd backend && python -m pytest tests/

# 前端测试
cd analyzer && npm test

# 解析器测试
cd parser && ./unit_test.sh

# 生成器测试
cd generator && python -m pytest tests/
```

### 集成测试
```bash
# 端到端测试
./scripts/run_integration_tests.sh
```

## 📚 文档

- [开发者指南](docs/DEVELOPER_GUIDE.md)
- [API文档](docs/API_REFERENCE.md)
- [部署指南](docs/DEPLOYMENT.md)
- [故障排除](docs/TROUBLESHOOTING.md)

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 支持

如有问题或建议，请：
1. 查看 [FAQ](docs/FAQ.md)
2. 搜索 [Issues](../../issues)
3. 创建新的 Issue

---

**TableRAG Enhanced** - 让表格解析更智能、更准确、更高效！
