#!/bin/bash

# 测试修复效果的验证脚本

echo "🧪 验证修复效果..."

ANALYZER_DIR="/data/projects/kingsoft/personal/workspace/tablerag/enhance/analyzer"
cd "$ANALYZER_DIR"

echo "📁 当前目录: $(pwd)"

# 1. 检查logger导入路径是否正确
echo ""
echo "1. 🔍 检查logger导入路径..."
IMPORT_ERRORS=0

# 检查annotation目录下的文件
for file in src/components/annotation/*.js; do
    if [ -f "$file" ]; then
        if grep -q "import logger from '../utils/logger'" "$file"; then
            echo "  ❌ $file: 错误的导入路径"
            IMPORT_ERRORS=$((IMPORT_ERRORS + 1))
        elif grep -q "import logger from '../../utils/logger'" "$file"; then
            echo "  ✅ $file: 正确的导入路径"
        fi
    fi
done

# 检查components目录下的文件
for file in src/components/*.js; do
    if [ -f "$file" ] && grep -q "import logger" "$file"; then
        if grep -q "import logger from '../utils/logger'" "$file"; then
            echo "  ✅ $file: 正确的导入路径"
        else
            echo "  ❌ $file: 可能有错误的导入路径"
            IMPORT_ERRORS=$((IMPORT_ERRORS + 1))
        fi
    fi
done

# 2. 检查10000端口引用
echo ""
echo "2. 🔍 检查10000端口引用..."
PORT_REFS=$(grep -r "10000" src/ --include="*.js" --include="*.jsx" | grep -v "KdcCanvasRenderer" | wc -l)
if [ $PORT_REFS -eq 0 ]; then
    echo "  ✅ 已清理所有10000端口引用"
else
    echo "  ⚠️  仍有 $PORT_REFS 个10000端口引用需要检查"
    grep -r "10000" src/ --include="*.js" --include="*.jsx" | grep -v "KdcCanvasRenderer"
fi

# 3. 检查console.log数量
echo ""
echo "3. 🔍 检查console.log数量..."
CONSOLE_LOGS=$(find src/ -name "*.js" -o -name "*.jsx" | xargs grep -c "console\." | awk -F: '{sum += $2} END {print sum}')
LOGGER_CALLS=$(find src/ -name "*.js" -o -name "*.jsx" | xargs grep -c "logger\." | awk -F: '{sum += $2} END {print sum}')

echo "  📊 Console调用: $CONSOLE_LOGS"
echo "  📊 Logger调用: $LOGGER_CALLS"

if [ $CONSOLE_LOGS -lt 10 ]; then
    echo "  ✅ Console调用数量已控制在合理范围"
else
    echo "  ⚠️  Console调用仍然较多，可能影响性能"
fi

# 4. 检查CSS布局类
echo ""
echo "4. 🔍 检查CSS布局类..."
if grep -q "case-info-layout" src/components/CaseDetail.css; then
    echo "  ✅ 新的布局CSS已添加"
else
    echo "  ❌ 缺少新的布局CSS"
fi

if grep -q "app-content.*height.*calc" src/App.css; then
    echo "  ✅ 滚动布局CSS已更新"
else
    echo "  ❌ 滚动布局CSS未更新"
fi

# 5. 语法检查
echo ""
echo "5. 🔍 语法检查..."
SYNTAX_ERRORS=0

# 检查关键文件的语法
KEY_FILES=(
    "src/App.js"
    "src/components/CaseDetail.js"
    "src/components/CaseList.js"
    "src/utils/logger.js"
    "src/services/api.js"
)

for file in "${KEY_FILES[@]}"; do
    if [ -f "$file" ]; then
        if node -c "$file" 2>/dev/null; then
            echo "  ✅ $file: 语法正确"
        else
            echo "  ❌ $file: 语法错误"
            SYNTAX_ERRORS=$((SYNTAX_ERRORS + 1))
        fi
    else
        echo "  ⚠️  $file: 文件不存在"
    fi
done

# 总结
echo ""
echo "📋 修复效果总结:"
echo "=================="

if [ $IMPORT_ERRORS -eq 0 ]; then
    echo "✅ Logger导入路径: 正确"
else
    echo "❌ Logger导入路径: $IMPORT_ERRORS 个错误"
fi

if [ $PORT_REFS -eq 0 ]; then
    echo "✅ 端口引用: 已清理"
else
    echo "⚠️  端口引用: 仍有 $PORT_REFS 个"
fi

if [ $CONSOLE_LOGS -lt 10 ]; then
    echo "✅ Console日志: 已优化"
else
    echo "⚠️  Console日志: 仍然较多 ($CONSOLE_LOGS)"
fi

if [ $SYNTAX_ERRORS -eq 0 ]; then
    echo "✅ 语法检查: 通过"
else
    echo "❌ 语法检查: $SYNTAX_ERRORS 个错误"
fi

echo ""
if [ $IMPORT_ERRORS -eq 0 ] && [ $SYNTAX_ERRORS -eq 0 ]; then
    echo "🎉 所有修复验证通过！可以启动前端服务进行测试"
    echo ""
    echo "💡 下一步:"
    echo "  1. 启动前端: npm start"
    echo "  2. 在浏览器控制台运行: setLogLevel('WARN')"
    echo "  3. 测试图片加载、布局和滚动功能"
else
    echo "⚠️  发现问题，需要手动修复后再测试"
fi

echo ""
echo "✨ 验证完成！"
