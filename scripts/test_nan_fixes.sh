#!/bin/bash

# 测试NaN修复效果的验证脚本

echo "🔧 验证NaN修复效果..."

ANALYZER_DIR="/data/projects/kingsoft/personal/workspace/tablerag/enhance/analyzer/src"
cd "$ANALYZER_DIR"

echo "📁 当前目录: $(pwd)"

# 1. 检查图表组件中的NaN防护
echo ""
echo "1. 🔍 检查图表组件NaN防护..."

# 检查TextboxDistributionChart.js
if grep -q "max_textboxes_per_cell <= 1" components/TextboxDistributionChart.js; then
    echo "  ✅ TextboxDistributionChart.js: 已添加除零防护"
else
    echo "  ❌ TextboxDistributionChart.js: 缺少除零防护"
fi

if grep -q "maxCellCount <= 0" components/TextboxDistributionChart.js; then
    echo "  ✅ TextboxDistributionChart.js: 已添加maxCellCount防护"
else
    echo "  ❌ TextboxDistributionChart.js: 缺少maxCellCount防护"
fi

# 检查AccuracySummaryChart.js
if grep -q "(value || 0).toFixed" components/AccuracySummaryChart.js; then
    echo "  ✅ AccuracySummaryChart.js: 已添加NaN防护"
else
    echo "  ❌ AccuracySummaryChart.js: 缺少NaN防护"
fi

# 检查MetricsSummaryChart.js
if grep -q "(data.accuracy || 0).toFixed" components/MetricsSummaryChart.js; then
    echo "  ✅ MetricsSummaryChart.js: 已添加NaN防护"
else
    echo "  ❌ MetricsSummaryChart.js: 缺少NaN防护"
fi

# 检查CompactStatsChart.js
if grep -q "total > 0 ?" components/CompactStatsChart.js; then
    echo "  ✅ CompactStatsChart.js: 已添加除零防护"
else
    echo "  ❌ CompactStatsChart.js: 缺少除零防护"
fi

# 2. 检查数据处理函数中的NaN防护
echo ""
echo "2. 🔍 检查数据处理函数NaN防护..."

if grep -q "isNaN(value)" utils/dataProcessor.js; then
    echo "  ✅ dataProcessor.js: 已添加NaN检查"
else
    echo "  ❌ dataProcessor.js: 缺少NaN检查"
fi

# 3. 检查可能的问题模式
echo ""
echo "3. 🔍 检查潜在问题模式..."

# 查找未防护的toFixed调用
UNPROTECTED_TOFIXED=$(grep -r "\.toFixed(" components/ utils/ | grep -v "(.*|| 0)" | grep -v "isNaN" | wc -l)
if [ $UNPROTECTED_TOFIXED -eq 0 ]; then
    echo "  ✅ 未发现未防护的toFixed调用"
else
    echo "  ⚠️  发现 $UNPROTECTED_TOFIXED 个可能未防护的toFixed调用"
    grep -r "\.toFixed(" components/ utils/ | grep -v "(.*|| 0)" | grep -v "isNaN" | head -5
fi

# 查找可能的除零操作
DIVISION_PATTERNS=$(grep -r "/ [a-zA-Z]" components/ utils/ | grep -v "// " | wc -l)
echo "  📊 发现 $DIVISION_PATTERNS 个除法操作（需要人工检查是否有除零风险）"

# 4. 检查SVG line元素的属性
echo ""
echo "4. 🔍 检查SVG line元素..."

SVG_LINES=$(grep -r "<line" components/ | wc -l)
echo "  📊 发现 $SVG_LINES 个SVG line元素"

# 检查是否有动态计算的坐标
DYNAMIC_COORDS=$(grep -r "x1={.*}" components/ | wc -l)
echo "  📊 发现 $DYNAMIC_COORDS 个动态计算的坐标"

# 5. 生成测试数据
echo ""
echo "5. 🧪 生成测试数据..."

# 创建一个测试用的边界情况数据
cat > /tmp/test_edge_cases.json << 'EOF'
{
  "textbox_distribution": {
    "total_cells": 0,
    "avg_textboxes_per_cell": 0,
    "max_textboxes_per_cell": 1,
    "distribution": []
  },
  "accuracy_data": [
    {"name": "KDC", "accuracy": null},
    {"name": "MonkeyOCR", "accuracy": undefined},
    {"name": "VL-LLM", "accuracy": NaN}
  ],
  "metrics": {
    "total": 0,
    "success": 0,
    "accuracy": 0,
    "teds": 0
  }
}
EOF

echo "  ✅ 已生成边界情况测试数据: /tmp/test_edge_cases.json"

# 6. 总结
echo ""
echo "📋 修复效果总结:"
echo "=================="

FIXES_APPLIED=0

if grep -q "max_textboxes_per_cell <= 1" components/TextboxDistributionChart.js; then
    FIXES_APPLIED=$((FIXES_APPLIED + 1))
fi

if grep -q "(value || 0).toFixed" components/AccuracySummaryChart.js; then
    FIXES_APPLIED=$((FIXES_APPLIED + 1))
fi

if grep -q "(data.accuracy || 0).toFixed" components/MetricsSummaryChart.js; then
    FIXES_APPLIED=$((FIXES_APPLIED + 1))
fi

if grep -q "total > 0 ?" components/CompactStatsChart.js; then
    FIXES_APPLIED=$((FIXES_APPLIED + 1))
fi

if grep -q "isNaN(value)" utils/dataProcessor.js; then
    FIXES_APPLIED=$((FIXES_APPLIED + 1))
fi

echo "✅ 已应用修复: $FIXES_APPLIED/5"
echo "⚠️  未防护的toFixed: $UNPROTECTED_TOFIXED"
echo "📊 SVG元素数量: $SVG_LINES"

if [ $FIXES_APPLIED -eq 5 ] && [ $UNPROTECTED_TOFIXED -eq 0 ]; then
    echo ""
    echo "🎉 所有NaN修复已完成！"
    echo ""
    echo "💡 下一步:"
    echo "  1. 重新加载前端页面"
    echo "  2. 测试complex数据集"
    echo "  3. 检查控制台是否还有NaN错误"
    echo "  4. 测试图表显示是否正常"
else
    echo ""
    echo "⚠️  仍有部分问题需要处理"
    echo "  请检查上述未修复的项目"
fi

echo ""
echo "🔧 测试建议:"
echo "  - 测试空数据集的情况"
echo "  - 测试只有1个文本框的情况"
echo "  - 测试准确率为null/undefined的情况"
echo "  - 检查所有图表组件的渲染"

echo ""
echo "✨ 验证完成！"
