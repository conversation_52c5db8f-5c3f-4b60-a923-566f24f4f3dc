#!/bin/bash

# 批量替换analyzer前端中的console.log调用
# 用于解决console窗口崩溃问题

echo "🔧 开始批量替换console.log调用..."

ANALYZER_DIR="/data/projects/kingsoft/personal/workspace/tablerag/enhance/analyzer/src"

# 确保在正确的目录
cd "$ANALYZER_DIR"

echo "📁 当前目录: $(pwd)"

# 统计当前的console.log数量
TOTAL_LOGS=$(find . -name "*.js" -o -name "*.jsx" | xargs grep -c "console\." | awk -F: '{sum += $2} END {print sum}')
echo "📊 发现 $TOTAL_LOGS 个console调用"

# 备份重要文件
echo "💾 创建备份..."
cp utils/dataProcessor.js utils/dataProcessor.js.backup
cp components/ParseReport.js components/ParseReport.js.backup
cp components/CaseDetail.js components/CaseDetail.js.backup
cp components/CaseList.js components/CaseList.js.backup

# 替换console.log为logger.debug (除了错误和警告)
echo "🔄 替换console.log为logger.debug..."
find . -name "*.js" -o -name "*.jsx" | xargs sed -i 's/console\.log(/logger.debug(/g'

# 替换console.warn为logger.warn
echo "🔄 替换console.warn为logger.warn..."
find . -name "*.js" -o -name "*.jsx" | xargs sed -i 's/console\.warn(/logger.warn(/g'

# 替换console.error为logger.error
echo "🔄 替换console.error为logger.error..."
find . -name "*.js" -o -name "*.jsx" | xargs sed -i 's/console\.error(/logger.error(/g'

# 替换console.info为logger.info
echo "🔄 替换console.info为logger.info..."
find . -name "*.js" -o -name "*.jsx" | xargs sed -i 's/console\.info(/logger.info(/g'

# 在需要的文件中添加logger导入
echo "📦 添加logger导入..."

# 获取所有使用logger的文件
FILES_USING_LOGGER=$(find . -name "*.js" -o -name "*.jsx" | xargs grep -l "logger\." | grep -v logger.js)

for file in $FILES_USING_LOGGER; do
    # 检查是否已经导入了logger
    if ! grep -q "import logger" "$file"; then
        # 检查是否有其他import语句
        if grep -q "^import" "$file"; then
            # 在最后一个import语句后添加logger导入
            sed -i '/^import.*$/a import logger from '\''../utils/logger'\'';' "$file"
        else
            # 在文件开头添加logger导入
            sed -i '1i import logger from '\''../utils/logger'\'';' "$file"
        fi
        echo "  ✅ 已为 $file 添加logger导入"
    fi
done

# 修复相对路径问题
echo "🔧 修复导入路径..."
find . -name "*.js" -o -name "*.jsx" | xargs sed -i "s|import logger from '../utils/logger';|import logger from './utils/logger';|g"
find . -name "*.js" -o -name "*.jsx" | xargs sed -i "s|import logger from '../../utils/logger';|import logger from '../utils/logger';|g"

# 统计替换后的数量
REMAINING_LOGS=$(find . -name "*.js" -o -name "*.jsx" | xargs grep -c "console\." | awk -F: '{sum += $2} END {print sum}')
LOGGER_CALLS=$(find . -name "*.js" -o -name "*.jsx" | xargs grep -c "logger\." | awk -F: '{sum += $2} END {print sum}')

echo ""
echo "📊 替换完成统计:"
echo "  原始console调用: $TOTAL_LOGS"
echo "  剩余console调用: $REMAINING_LOGS"
echo "  新增logger调用: $LOGGER_CALLS"
echo ""

if [ $REMAINING_LOGS -lt 10 ]; then
    echo "✅ 替换成功！剩余console调用数量已降至可接受范围"
else
    echo "⚠️  仍有较多console调用，可能需要手动处理"
fi

echo ""
echo "💡 使用说明:"
echo "  - 在浏览器控制台运行 setLogLevel('WARN') 来减少日志输出"
echo "  - 运行 setLogLevel('DEBUG') 来启用详细日志"
echo "  - 运行 getLogStats() 查看日志统计信息"
echo ""
echo "🔧 如果需要恢复，可以使用备份文件:"
echo "  cp utils/dataProcessor.js.backup utils/dataProcessor.js"
echo ""

echo "✨ 批量替换完成！"
