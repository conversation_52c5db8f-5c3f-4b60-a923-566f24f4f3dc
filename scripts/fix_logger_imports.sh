#!/bin/bash

# 修复logger导入路径和重复导入问题

echo "🔧 修复logger导入问题..."

ANALYZER_SRC="/data/projects/kingsoft/personal/workspace/tablerag/enhance/analyzer/src"
cd "$ANALYZER_SRC"

echo "📁 当前目录: $(pwd)"

# 获取所有使用logger的文件
FILES_WITH_LOGGER=$(find . -name "*.js" -exec grep -l "import logger" {} \;)

echo "📋 需要修复的文件:"
echo "$FILES_WITH_LOGGER"

for file in $FILES_WITH_LOGGER; do
    echo "🔧 处理文件: $file"
    
    # 移除所有logger导入行
    sed -i '/^import logger from/d' "$file"
    
    # 根据文件位置确定正确的导入路径
    if [[ "$file" == "./utils/"* ]]; then
        # utils目录下的文件
        IMPORT_PATH="./logger"
    elif [[ "$file" == "./components/"* ]]; then
        # components目录下的文件
        IMPORT_PATH="../utils/logger"
    elif [[ "$file" == "./components/annotation/"* ]]; then
        # components/annotation目录下的文件
        IMPORT_PATH="../../utils/logger"
    elif [[ "$file" == "./services/"* ]]; then
        # services目录下的文件
        IMPORT_PATH="../utils/logger"
    elif [[ "$file" == "./hooks/"* ]]; then
        # hooks目录下的文件
        IMPORT_PATH="../utils/logger"
    else
        # 根目录下的文件
        IMPORT_PATH="./utils/logger"
    fi
    
    # 在第一个import语句后添加logger导入
    if grep -q "^import" "$file"; then
        # 找到第一个import语句的行号
        FIRST_IMPORT_LINE=$(grep -n "^import" "$file" | head -1 | cut -d: -f1)
        # 在第一个import语句后插入logger导入
        sed -i "${FIRST_IMPORT_LINE}a import logger from '$IMPORT_PATH';" "$file"
    else
        # 如果没有import语句，在文件开头添加
        sed -i "1i import logger from '$IMPORT_PATH';" "$file"
    fi
    
    echo "  ✅ 已修复 $file (路径: $IMPORT_PATH)"
done

echo ""
echo "🧹 清理重复的导入..."

# 移除重复的logger导入（保留第一个）
for file in $FILES_WITH_LOGGER; do
    # 使用awk移除重复的logger导入行
    awk '!seen[$0]++ || !/^import logger from/' "$file" > "$file.tmp" && mv "$file.tmp" "$file"
done

echo ""
echo "📊 验证修复结果..."

# 统计每个文件的logger导入数量
for file in $FILES_WITH_LOGGER; do
    LOGGER_IMPORTS=$(grep -c "^import logger from" "$file")
    if [ $LOGGER_IMPORTS -eq 1 ]; then
        echo "  ✅ $file: $LOGGER_IMPORTS 个logger导入"
    else
        echo "  ⚠️  $file: $LOGGER_IMPORTS 个logger导入 (应该是1个)"
    fi
done

echo ""
echo "🔍 检查语法错误..."

# 检查是否有语法错误
SYNTAX_ERRORS=0
for file in $FILES_WITH_LOGGER; do
    if node -c "$file" 2>/dev/null; then
        echo "  ✅ $file: 语法正确"
    else
        echo "  ❌ $file: 语法错误"
        SYNTAX_ERRORS=$((SYNTAX_ERRORS + 1))
    fi
done

echo ""
if [ $SYNTAX_ERRORS -eq 0 ]; then
    echo "✅ 所有文件修复完成，无语法错误！"
else
    echo "⚠️  发现 $SYNTAX_ERRORS 个文件有语法错误，请手动检查"
fi

echo ""
echo "💡 下一步:"
echo "  1. 重启前端开发服务器: npm start"
echo "  2. 在浏览器控制台运行: setLogLevel('WARN')"
echo "  3. 检查console日志是否大幅减少"

echo ""
echo "✨ 修复完成！"
