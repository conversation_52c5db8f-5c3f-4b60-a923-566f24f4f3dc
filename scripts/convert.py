import os
import json
import argparse
import shutil
from pathlib import Path

def get_table_style(content):
    """从content中获取表格样式"""
    if "elements" in content:
        for element in content["elements"]:
            if element.get("type") == "table":
                return element.get("style", "default")
    return "default"

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='转换数据集并按style组织文件')
    parser.add_argument('--input_dir', required=True, help='输入目录路径 (包含train_data.json和json_mapping.json的目录)')
    parser.add_argument('--output_dir', help='输出目录路径，默认为输入目录下的output目录')
    parser.add_argument('--prefix', default='', help='分类目录名前缀')
    
    args = parser.parse_args()
    
    # 转换为Path对象并确保输入目录存在
    input_dir = Path(args.input_dir).resolve()  # 获取绝对路径
    if not input_dir.exists():
        print(f"错误: 输入目录 {input_dir} 不存在")
        return
        
    # 设置默认输出目录
    if args.output_dir is None:
        output_dir = input_dir / "output"
    else:
        output_dir = Path(args.output_dir)
    
    output_dir.mkdir(parents=True, exist_ok=True)

    # 读取train_data.json创建映射关系
    train_data_path = input_dir / "train_data.json"
    if not train_data_path.exists():
        print(f"错误: {train_data_path} 不存在")
        return
        
    with open(train_data_path, "r", encoding="utf-8") as f:
        train_data = json.load(f)

    # 创建json_id到图片信息的映射
    id_to_info = {}
    for item in train_data:
        image_path = item["image_path"]
        # 如果是绝对路径，获取相对于input_dir的路径
        if os.path.isabs(image_path):
            try:
                # 尝试将绝对路径转换为相对于input_dir的路径
                rel_path = Path(image_path).relative_to(input_dir)
            except ValueError:
                # 如果不是input_dir的子路径，则直接使用文件名
                rel_path = Path("images") / os.path.basename(image_path)
        else:
            rel_path = Path(image_path)

        # 提取纯文件名（不带路径和扩展名）
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        id_to_info[item["json_id"]] = {
            "image_name": image_name,
            "image_path": rel_path
        }

    # 读取json_mapping.json
    json_mapping_path = input_dir / "json_mapping.json"
    if not json_mapping_path.exists():
        print(f"错误: {json_mapping_path} 不存在")
        return
        
    with open(json_mapping_path, "r", encoding="utf-8") as f:
        json_mapping = json.load(f)

    # 收集所有的style
    styles = set()
    # 更新id_to_info中的style信息
    for str_id, content in json_mapping.items():
        try:
            json_id = int(str_id)
            if json_id in id_to_info:
                # 从json_mapping中获取style信息
                style = get_table_style(content)
                styles.add(style)
                id_to_info[json_id]["style"] = style
        except ValueError:
            continue

    print(f"发现的样式: {sorted(list(styles))}")

    # 为每个style创建目录结构（添加前缀）
    for style in styles:
        style_dir_name = f"{args.prefix}{style}" if args.prefix else style
        style_dir = output_dir / style_dir_name
        (style_dir / "annotations").mkdir(parents=True, exist_ok=True)
        (style_dir / "images").mkdir(parents=True, exist_ok=True)

    # 处理每个json条目
    for str_id, content in json_mapping.items():
        try:
            json_id = int(str_id)  # 将字符串ID转为整数
        except ValueError:
            continue  # 跳过无效ID
            
        if json_id in id_to_info:
            info = id_to_info[json_id]
            style = get_table_style(content)
            style_dir_name = f"{args.prefix}{style}" if args.prefix else style
            style_dir = output_dir / style_dir_name
            
            # 构建目标JSON路径（按style分类）
            json_name = f"{info['image_name']}.json"
            output_path = style_dir / "annotations" / json_name
            
            # 写入JSON文件（修复中文编码问题）
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(content, f, indent=2, ensure_ascii=False)
            print(f"创建标注: {output_path}")
            
            # 复制对应的图片到相应的style目录
            src_image = input_dir / info["image_path"]
            if src_image.exists():
                dst_image = style_dir / "images" / src_image.name
                if not dst_image.exists():  # 避免重复复制
                    shutil.copy2(src_image, dst_image)
                    print(f"复制图片: {dst_image}")
            else:
                print(f"警告: 找不到图片 {src_image}")
        else:
            print(f"跳过 ID {json_id}: 在train_data中找不到对应的图片")

    print("转换完成!")

if __name__ == "__main__":
    main()