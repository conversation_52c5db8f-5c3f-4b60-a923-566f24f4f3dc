# 平均准确率调试功能使用说明

## 问题描述
complex数据集显示平均准确率为"-"，但个别case显示有准确率数据。

## 解决方案
我已经提供了两种调试方式：
1. **HTML报告调试**（推荐）：生成详细的HTML报告，直接显示所有计算过程
2. **控制台日志调试**：在浏览器控制台查看实时计算日志

**推荐使用HTML报告方式，更直观易读！**详见`HTML报告查看说明.md`

## 如何使用

### 1. 启动应用
```bash
cd analyzer
npm start
```

### 2. 打开浏览器控制台
**重要：**在Chrome浏览器中按`F12`或右键点击"检查"，然后切换到"Console"标签页。这样你就能看到详细的调试日志。

### 3. 选择complex数据集
在应用界面中选择"complex"数据集。

### 4. 查看控制台调试信息
当数据加载和计算时，你会在控制台看到大量的调试信息，包括：

#### 基准数据解析过程
- `[parseTableContent] ==== Input type: object`
- `[parseTableContent] ==== Input content: {elements: [...]}` 
- `[parseTableContent] Processing content item: {type: 'table', ...}`

#### 准确率计算过程
- `[calculateAccuracyWithTableComparison] ==== Starting accuracy calculation`
- `[calculateAccuracyWithTableComparison] ==== baselineData type: object`
- `[calculateAccuracyWithTableComparison] ==== baselineTable result: [[...], [...]]`

#### 表格比较过程
- `[getCompleteTableComparison] ==== Input validation:`
- `[getCompleteTableComparison] ==== Result: {accuracy: XX.X}`

### 5. 启用界面调试信息
在统计图表区域，点击右上角的"显示调试信息"按钮查看汇总统计。

## 核心调试流程

### 查看控制台日志的关键信息：

1. **基准数据状态**：
   ```
   [calculateAccuracyWithTableComparison] ==== baselineData type: object
   [calculateAccuracyWithTableComparison] ==== baselineData: {elements: [...]}
   ```

2. **基准表格解析**：
   ```
   [parseTableContent] ==== Input type: object
   [parseTableContent] Processing parsed JSON Schema: {type: 'json_schema', ...}
   [parseTableContent] Processing content item: {type: 'table', content: [...]}
   ```

3. **解析结果**：
   ```
   [calculateAccuracyWithTableComparison] ==== baselineTable result: [[...], [...]]
   [calculateAccuracyWithTableComparison] ==== actualTable result: [[...], [...]]
   ```

4. **最终结果**：
   ```
   [calculateAccuracyWithTableComparison] ==== Comparison result: {accuracy: XX.X}
   ```

## 问题诊断步骤

### 1. 检查基准数据
在控制台搜索 `baselineData type`，看到：
- **正常情况**：`baselineData type: object`
- **异常情况**：`No baseline data, returning null`

### 2. 检查基准表格解析
搜索 `baselineTable result`：
- **正常情况**：`baselineTable result: [[cell1, cell2], [cell3, cell4]]`
- **异常情况**：`baselineTable result: null`

### 3. 检查实际表格解析
搜索 `actualTable result`：
- **正常情况**：`actualTable result: [[cell1, cell2], [cell3, cell4]]`
- **异常情况**：`actualTable result: null`

### 4. 检查最终结果
搜索 `Comparison result`：
- **正常情况**：`Comparison result: {accuracy: 85.5}`
- **异常情况**：`Missing tables, falling back to legacy algorithm`

## 常见问题和解决方案

### 问题1：`baselineTable result: null`
**原因**：JSON Schema解析失败
**解决**：检查控制台中的parseTableContent日志，看是否能找到table类型的content

### 问题2：`actualTable result: null`
**原因**：解析器输出无法解析为表格
**解决**：检查解析器的实际输出内容

### 问题3：`No baseline data, returning null`
**原因**：标注数据不存在或格式不正确
**解决**：检查案例是否有annotation字段

### 问题4：所有解析器都返回null
**原因**：标注数据存在但无法解析
**解决**：查看parseTableContent的详细日志，可能是JSON Schema结构问题

## 快速调试命令

在浏览器控制台中执行以下命令来快速过滤日志：

```javascript
// 查看基准数据类型
console.log("=== 基准数据类型 ===");
// 然后查看控制台中包含 "baselineData type" 的日志

// 查看解析结果
console.log("=== 解析结果 ===");  
// 然后查看控制台中包含 "baselineTable result" 的日志

// 查看准确率结果
console.log("=== 准确率结果 ===");
// 然后查看控制台中包含 "Comparison result" 的日志
```

## 预期的正常日志流程

正常情况下，你应该看到类似这样的日志序列：
```
[calculateAccuracyForCase] 发现直接的JSON Schema格式标注数据
[calculateAccuracyForCase] 成功解析直接JSON Schema人工标注数据
[calculateAccuracyWithTableComparison] ==== baselineData type: object
[parseTableContent] ==== Input type: object
[parseTableContent] Processing parsed JSON Schema: {type: 'json_schema', content: [...]}
[parseTableContent] Found table content array: [[...], [...]]
[calculateAccuracyWithTableComparison] ==== baselineTable result: [[...], [...]]
[calculateAccuracyWithTableComparison] ==== actualTable result: [[...], [...]]
[getCompleteTableComparison] ==== Result: {accuracy: XX.X}
```

如果看到大量的null结果，那就找到了问题所在！ 