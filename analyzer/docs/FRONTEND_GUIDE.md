# Analyzer 前端开发指南

TableRAG Enhanced 前端分析界面的详细开发指南。

## 技术栈

### 核心框架
- **React 18**: 现代化前端框架
- **Create React App**: 项目脚手架
- **JavaScript ES6+**: 编程语言

### UI组件库
- **Material-UI**: 组件库和设计系统
- **React Bootstrap**: 响应式布局
- **Ant Design**: 部分高级组件

### 数据可视化
- **Chart.js**: 图表库
- **React-Chartjs-2**: React封装
- **D3.js**: 自定义可视化

### 工具库
- **Axios**: HTTP客户端
- **Lodash**: 工具函数库
- **Moment.js**: 时间处理
- **React Router**: 路由管理

## 项目结构

```
analyzer/src/
├── components/           # React组件
│   ├── common/          # 通用组件
│   ├── dataset/         # 数据集相关
│   ├── parsing/         # 解析结果相关
│   ├── annotation/      # 标注相关
│   └── charts/          # 图表组件
├── services/            # API服务
├── hooks/               # 自定义Hooks
├── utils/               # 工具函数
├── styles/              # 样式文件
├── contexts/            # React Context
└── constants/           # 常量定义
```

## 核心组件

### 1. 数据集选择器 (DatasetSelector)
```javascript
import React, { useState, useEffect } from 'react';
import { Select, Spin } from 'antd';
import { getDatasets } from '../services/api';

const DatasetSelector = ({ onDatasetChange, defaultDataset }) => {
  const [datasets, setDatasets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDataset, setSelectedDataset] = useState(defaultDataset);

  useEffect(() => {
    loadDatasets();
  }, []);

  const loadDatasets = async () => {
    try {
      setLoading(true);
      const response = await getDatasets();
      setDatasets(response.data);
    } catch (error) {
      console.error('加载数据集失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (value) => {
    setSelectedDataset(value);
    onDatasetChange(value);
  };

  return (
    <Select
      value={selectedDataset}
      onChange={handleChange}
      loading={loading}
      placeholder="选择数据集"
      style={{ width: 200 }}
    >
      {datasets.map(dataset => (
        <Select.Option key={dataset.name} value={dataset.name}>
          {dataset.name} ({dataset.image_count} 张图片)
        </Select.Option>
      ))}
    </Select>
  );
};

export default DatasetSelector;
```

### 2. 案例列表 (CaseList)
```javascript
import React, { useMemo } from 'react';
import { List, Card, Tag, Progress } from 'antd';
import { VirtualList } from './VirtualList';

const CaseList = ({ cases, onCaseSelect, selectedCase }) => {
  const virtualizedCases = useMemo(() => {
    return cases.map((case_, index) => ({
      ...case_,
      key: `case-${index}`,
      accuracy: calculateAverageAccuracy(case_.parsing_results)
    }));
  }, [cases]);

  const renderCaseItem = (case_) => (
    <Card
      size="small"
      hoverable
      className={selectedCase === case_.image_name ? 'selected' : ''}
      onClick={() => onCaseSelect(case_.image_name)}
    >
      <div className="case-header">
        <span className="case-name">{case_.image_name}</span>
        <Tag color={case_.accuracy > 0.8 ? 'green' : 'orange'}>
          {(case_.accuracy * 100).toFixed(1)}%
        </Tag>
      </div>
      <Progress
        percent={case_.accuracy * 100}
        size="small"
        showInfo={false}
      />
      <div className="parser-status">
        {Object.entries(case_.parsing_results).map(([parser, result]) => (
          <Tag
            key={parser}
            color={result.success ? 'green' : 'red'}
            size="small"
          >
            {parser}
          </Tag>
        ))}
      </div>
    </Card>
  );

  return (
    <div className="case-list">
      <VirtualList
        items={virtualizedCases}
        itemHeight={120}
        renderItem={renderCaseItem}
        height={600}
      />
    </div>
  );
};

const calculateAverageAccuracy = (parsingResults) => {
  const accuracies = Object.values(parsingResults)
    .filter(result => result.success && result.accuracy !== undefined)
    .map(result => result.accuracy);
  
  return accuracies.length > 0 
    ? accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length 
    : 0;
};

export default CaseList;
```

### 3. 案例详情 (CaseDetail)
```javascript
import React, { useState } from 'react';
import { Tabs, Card, Image, Table, Collapse } from 'antd';
import { TableRenderer } from './TableRenderer';
import { AccuracyReport } from './AccuracyReport';

const { TabPane } = Tabs;
const { Panel } = Collapse;

const CaseDetail = ({ caseData, annotationData }) => {
  const [activeTab, setActiveTab] = useState('parsing');

  const renderParsingResults = () => (
    <div className="parsing-results">
      {Object.entries(caseData.parsing_results).map(([parser, result]) => (
        <Card
          key={parser}
          title={getParserDisplayName(parser)}
          size="small"
          className="parser-result-card"
          extra={
            <Tag color={result.success ? 'green' : 'red'}>
              {result.success ? '成功' : '失败'}
            </Tag>
          }
        >
          {result.success ? (
            <div className="result-content">
              <Collapse size="small">
                <Panel header="原始文本" key="original">
                  <pre className="original-text">
                    {result.original_text}
                  </pre>
                </Panel>
              </Collapse>
              <div className="rendered-content">
                <TableRenderer
                  content={result.content}
                  format={result.format}
                />
              </div>
              {annotationData && (
                <AccuracyReport
                  parserResult={result}
                  annotationData={annotationData}
                  parser={parser}
                />
              )}
            </div>
          ) : (
            <div className="error-content">
              <p>错误: {result.error}</p>
            </div>
          )}
        </Card>
      ))}
    </div>
  );

  const renderAnnotationView = () => (
    <div className="annotation-view">
      {annotationData ? (
        <Card title="人工标注数据">
          <div className="annotation-info">
            <p><strong>标注员:</strong> {annotationData.annotator}</p>
            <p><strong>状态:</strong> {annotationData.status}</p>
            <p><strong>表格结构:</strong> 
              {annotationData.table_structure.rows} 行 × 
              {annotationData.table_structure.cols} 列
            </p>
          </div>
          <div className="annotation-content">
            <TableRenderer
              content={annotationData.table_content}
              format={annotationData.content_format}
            />
          </div>
        </Card>
      ) : (
        <Card>
          <p>暂无标注数据</p>
        </Card>
      )}
    </div>
  );

  return (
    <div className="case-detail">
      <div className="case-header">
        <div className="image-preview">
          <Image
            src={`/static/dataset/${caseData.dataset}/${caseData.image_name}`}
            alt={caseData.image_name}
            width={300}
            preview={{
              mask: '点击预览'
            }}
          />
        </div>
        <div className="case-info">
          <h3>{caseData.image_name}</h3>
          <p>数据集: {caseData.dataset}</p>
          <p>解析器数量: {Object.keys(caseData.parsing_results).length}</p>
        </div>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="解析分析" key="parsing">
          {renderParsingResults()}
        </TabPane>
        <TabPane tab="人工标注" key="annotation">
          {renderAnnotationView()}
        </TabPane>
      </Tabs>
    </div>
  );
};

const getParserDisplayName = (parser) => {
  const names = {
    'kdc_markdown': 'KDC (Markdown)',
    'kdc_plain': 'KDC (Plain)',
    'kdc_kdc': 'KDC (Native)',
    'monkey_ocr': 'MonkeyOCR (HTML)',
    'monkey_ocr_latex': 'MonkeyOCR (LaTeX)',
    'vl_llm': 'VL LLM',
    'monkey_ocr_kas': 'MonkeyOCR（kas）',
    'ocrflux': 'OCR Flux'
  };
  return names[parser] || parser;
};

export default CaseDetail;
```

## 状态管理

### 1. React Context
```javascript
import React, { createContext, useContext, useReducer } from 'react';

// 创建Context
const AppContext = createContext();

// 初始状态
const initialState = {
  selectedDataset: 'kingsoft',
  selectedCase: null,
  cases: [],
  annotationData: {},
  loading: false,
  error: null
};

// Reducer
const appReducer = (state, action) => {
  switch (action.type) {
    case 'SET_DATASET':
      return {
        ...state,
        selectedDataset: action.payload,
        selectedCase: null,
        cases: []
      };
    
    case 'SET_CASES':
      return {
        ...state,
        cases: action.payload,
        loading: false
      };
    
    case 'SET_SELECTED_CASE':
      return {
        ...state,
        selectedCase: action.payload
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        loading: action.payload
      };
    
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
        loading: false
      };
    
    default:
      return state;
  }
};

// Provider组件
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

// Hook
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within AppProvider');
  }
  return context;
};
```

### 2. 自定义Hooks
```javascript
import { useState, useEffect, useCallback } from 'react';
import { getParseResults, getAnnotationData } from '../services/api';

// 数据加载Hook
export const useDataLoader = (dataset) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadData = useCallback(async () => {
    if (!dataset) return;

    try {
      setLoading(true);
      setError(null);

      const [parseResults, annotations] = await Promise.all([
        getParseResults(dataset),
        getAnnotationData(dataset)
      ]);

      setData({
        parseResults: parseResults.data,
        annotations: annotations.data
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [dataset]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  return { data, loading, error, reload: loadData };
};

// 虚拟滚动Hook
export const useVirtualization = (items, itemHeight, containerHeight) => {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleStart = Math.floor(scrollTop / itemHeight);
  const visibleEnd = Math.min(
    visibleStart + Math.ceil(containerHeight / itemHeight) + 1,
    items.length
  );

  const visibleItems = items.slice(visibleStart, visibleEnd);
  const offsetY = visibleStart * itemHeight;

  return {
    visibleItems,
    offsetY,
    totalHeight: items.length * itemHeight,
    onScroll: (e) => setScrollTop(e.target.scrollTop)
  };
};
```

## 性能优化

### 1. 组件优化
```javascript
import React, { memo, useMemo, useCallback } from 'react';

// 使用memo避免不必要的重渲染
const CaseItem = memo(({ case_, onSelect, isSelected }) => {
  const handleClick = useCallback(() => {
    onSelect(case_.image_name);
  }, [case_.image_name, onSelect]);

  const accuracy = useMemo(() => {
    return calculateAccuracy(case_.parsing_results);
  }, [case_.parsing_results]);

  return (
    <div 
      className={`case-item ${isSelected ? 'selected' : ''}`}
      onClick={handleClick}
    >
      <span>{case_.image_name}</span>
      <span>{(accuracy * 100).toFixed(1)}%</span>
    </div>
  );
});

// 使用useMemo缓存计算结果
const CaseList = ({ cases, selectedCase, onCaseSelect }) => {
  const sortedCases = useMemo(() => {
    return [...cases].sort((a, b) => 
      calculateAccuracy(b.parsing_results) - calculateAccuracy(a.parsing_results)
    );
  }, [cases]);

  const handleCaseSelect = useCallback((caseName) => {
    onCaseSelect(caseName);
  }, [onCaseSelect]);

  return (
    <div className="case-list">
      {sortedCases.map(case_ => (
        <CaseItem
          key={case_.image_name}
          case_={case_}
          onSelect={handleCaseSelect}
          isSelected={selectedCase === case_.image_name}
        />
      ))}
    </div>
  );
};
```

### 2. 懒加载
```javascript
import React, { lazy, Suspense } from 'react';
import { Spin } from 'antd';

// 懒加载组件
const CaseDetail = lazy(() => import('./CaseDetail'));
const AnnotationEditor = lazy(() => import('./AnnotationEditor'));

const App = () => {
  return (
    <div className="app">
      <Suspense fallback={<Spin size="large" />}>
        <CaseDetail />
      </Suspense>
      
      <Suspense fallback={<Spin size="large" />}>
        <AnnotationEditor />
      </Suspense>
    </div>
  );
};
```

## 样式管理

### 1. CSS Modules
```css
/* CaseList.module.css */
.caseList {
  height: 100%;
  overflow-y: auto;
}

.caseItem {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.caseItem:hover {
  background-color: #f5f5f5;
}

.caseItem.selected {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.accuracy {
  float: right;
  font-weight: bold;
}

.accuracy.high {
  color: #52c41a;
}

.accuracy.medium {
  color: #faad14;
}

.accuracy.low {
  color: #ff4d4f;
}
```

### 2. 主题配置
```javascript
// theme.js
export const theme = {
  colors: {
    primary: '#1890ff',
    success: '#52c41a',
    warning: '#faad14',
    error: '#ff4d4f',
    text: '#262626',
    textSecondary: '#8c8c8c',
    border: '#d9d9d9',
    background: '#fafafa'
  },
  
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px'
  },
  
  breakpoints: {
    xs: '480px',
    sm: '576px',
    md: '768px',
    lg: '992px',
    xl: '1200px',
    xxl: '1600px'
  }
};
```

## 测试策略

### 1. 单元测试
```javascript
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CaseList from '../CaseList';

describe('CaseList', () => {
  const mockCases = [
    {
      image_name: 'test1.jpg',
      parsing_results: {
        kdc_markdown: { success: true, accuracy: 0.9 }
      }
    },
    {
      image_name: 'test2.jpg',
      parsing_results: {
        kdc_markdown: { success: true, accuracy: 0.8 }
      }
    }
  ];

  test('renders case list correctly', () => {
    const onCaseSelect = jest.fn();
    
    render(
      <CaseList 
        cases={mockCases} 
        onCaseSelect={onCaseSelect}
        selectedCase={null}
      />
    );

    expect(screen.getByText('test1.jpg')).toBeInTheDocument();
    expect(screen.getByText('test2.jpg')).toBeInTheDocument();
  });

  test('calls onCaseSelect when case is clicked', () => {
    const onCaseSelect = jest.fn();
    
    render(
      <CaseList 
        cases={mockCases} 
        onCaseSelect={onCaseSelect}
        selectedCase={null}
      />
    );

    fireEvent.click(screen.getByText('test1.jpg'));
    expect(onCaseSelect).toHaveBeenCalledWith('test1.jpg');
  });
});
```

### 2. 集成测试
```javascript
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { rest } from 'msw';
import { setupServer } from 'msw/node';
import App from '../App';

// 模拟API服务器
const server = setupServer(
  rest.get('/api/datasets', (req, res, ctx) => {
    return res(ctx.json({
      data: [
        { name: 'test_dataset', image_count: 10 }
      ]
    }));
  }),
  
  rest.get('/api/parse_results/test_dataset', (req, res, ctx) => {
    return res(ctx.json({
      data: {
        cases: [
          {
            image_name: 'test.jpg',
            parsing_results: {}
          }
        ]
      }
    }));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

test('loads and displays data correctly', async () => {
  render(<App />);
  
  await waitFor(() => {
    expect(screen.getByText('test_dataset')).toBeInTheDocument();
  });
  
  await waitFor(() => {
    expect(screen.getByText('test.jpg')).toBeInTheDocument();
  });
});
```

## 部署配置

### 1. 生产构建
```bash
# 构建生产版本
npm run build

# 预览构建结果
npx serve -s build -l 3000
```

### 2. 环境配置
```javascript
// .env.production
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_STATIC_BASE_URL=http://localhost:8000
REACT_APP_VERSION=$npm_package_version
```

### 3. Nginx配置
```nginx
server {
    listen 3000;
    server_name localhost;
    
    root /path/to/build;
    index index.html;
    
    # 处理React Router
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    # 静态文件
    location /static/ {
        proxy_pass http://localhost:8000/static/;
    }
}
```

## 最佳实践

1. **组件设计**: 保持组件单一职责，便于测试和维护
2. **状态管理**: 合理使用Context和自定义Hooks
3. **性能优化**: 使用memo、useMemo、useCallback避免不必要的渲染
4. **错误处理**: 实现错误边界和优雅的错误提示
5. **可访问性**: 遵循WCAG指南，支持键盘导航和屏幕阅读器
6. **代码分割**: 使用懒加载减少初始包大小
7. **测试覆盖**: 编写充分的单元测试和集成测试
