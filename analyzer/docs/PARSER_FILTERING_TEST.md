# 解析器筛选功能测试指南

## 测试目标

验证解析器筛选功能是否在以下组件中正确生效：
1. 解析详情（CaseDetail）
2. 指标汇总图表（MetricsSummaryChart）
3. 数据集报告（DatasetReport）
4. 紧凑统计图表（CompactStatsChart）
5. HTML报告生成器（htmlReportGenerator）

## 测试步骤

### 1. 基础功能测试

1. **打开应用**：访问 http://localhost:3001
2. **选择数据集**：选择一个包含多种解析器结果的数据集
3. **查看解析器选择器**：在数据集选择器下方应该显示"多路解析"选择器
4. **展开选择器**：点击展开，应该看到所有解析器分类和选项

### 2. 解析详情筛选测试

1. **选择一个测试案例**
2. **查看解析详情**：应该显示所有启用的解析器行
3. **取消选择某些解析器**：
   - 在解析器选择器中取消选择几个解析器
   - 观察解析详情是否立即更新，只显示选中的解析器
4. **重新选择解析器**：
   - 重新选择之前取消的解析器
   - 观察对应的解析器行是否重新出现

### 3. 指标汇总图表筛选测试

1. **查看指标汇总图表**：在案例详情页面的指标汇总部分
2. **修改解析器选择**：
   - 取消选择某些解析器
   - 观察图表是否立即更新，只显示选中解析器的数据条
3. **验证数据一致性**：
   - 图表中显示的解析器应该与解析详情中的解析器一致
   - 解析器的顺序应该保持一致

### 4. 数据集报告筛选测试

1. **显示详细报告**：在案例列表页面点击"详细报告"按钮
2. **查看解析器性能表格**：应该只显示选中的解析器
3. **修改解析器选择**：
   - 取消选择某些解析器
   - 观察报告表格是否立即更新
4. **验证统计数据**：
   - 统计数据应该只基于选中的解析器计算
   - 总计数据应该相应调整

### 5. 紧凑统计图表筛选测试

1. **查看紧凑统计图表**：在案例列表页面（非详细报告模式）
2. **修改解析器选择**：
   - 取消选择某些解析器
   - 观察图表是否立即更新
3. **验证图表数据**：
   - 图表应该只显示选中解析器的统计条
   - 数据应该与详细报告中的数据一致

### 6. HTML报告筛选测试

1. **生成HTML报告**：点击"HTML报告"按钮
2. **查看报告内容**：
   - 解析器性能汇总表格应该只包含选中的解析器
   - 详细计算过程应该只包含选中的解析器
   - 案例详情应该只显示选中解析器的结果
3. **修改选择后重新生成**：
   - 修改解析器选择
   - 重新生成HTML报告
   - 验证新报告只包含当前选中的解析器

### 7. 状态持久化测试

1. **修改解析器选择**：选择一个特定的解析器组合
2. **刷新页面**：按F5或手动刷新浏览器
3. **验证状态保持**：
   - 解析器选择应该保持之前的状态
   - 所有相关组件应该基于保存的选择显示内容

### 8. 边界情况测试

1. **全不选测试**：
   - 取消选择所有解析器
   - 观察各组件的行为（应该显示空状态或提示信息）
2. **单选测试**：
   - 只选择一个解析器
   - 验证所有组件都能正常工作
3. **分类选择测试**：
   - 使用分类复选框进行批量选择
   - 验证功能正常工作

## 预期结果

### 正确行为

1. **实时更新**：修改解析器选择后，所有相关组件立即更新
2. **数据一致性**：所有组件显示的解析器列表和顺序保持一致
3. **状态持久化**：页面刷新后选择状态保持
4. **HTML报告同步**：生成的HTML报告反映当前的解析器选择

### 错误行为

1. **不同步更新**：某些组件没有响应解析器选择的变化
2. **数据不一致**：不同组件显示的解析器列表或顺序不一致
3. **状态丢失**：页面刷新后选择状态重置
4. **HTML报告过时**：生成的HTML报告包含未选中的解析器

## 故障排查

### 如果某个组件没有响应筛选

1. **检查组件是否使用useParserContext**：
   ```javascript
   const { enabledParsers } = useParserContext();
   ```

2. **检查useMemo/useCallback依赖项**：
   ```javascript
   useMemo(() => {
     // ...
   }, [data, enabledParsers]); // 确保包含enabledParsers
   ```

3. **检查组件是否在ParserProvider内部**：
   ```javascript
   <ParserProvider>
     <YourComponent />
   </ParserProvider>
   ```

### 如果HTML报告没有筛选

1. **检查generateHTMLReport调用**：
   ```javascript
   generateReportHTML(cases, dataset, annotationData, enabledParsers);
   ```

2. **检查calculateParserStats调用**：
   ```javascript
   calculateParserStats(cases, annotationData, enabledParsers);
   ```

### 如果状态不持久化

1. **检查localStorage权限**
2. **检查ParserContext中的useEffect**
3. **查看浏览器控制台是否有错误**

## 测试完成标准

- [ ] 解析详情正确筛选
- [ ] 指标汇总图表正确筛选
- [ ] 数据集报告正确筛选
- [ ] 紧凑统计图表正确筛选
- [ ] HTML报告正确筛选
- [ ] 状态持久化正常工作
- [ ] 所有组件数据一致
- [ ] 边界情况处理正确

当所有测试项目都通过时，解析器筛选功能实现完成。
