# HTML报告查看说明

## 📊 新功能：详细的平均准确率计算报告

我已经完全重写了HTML报告生成器，现在你可以通过HTML报告查看完整的平均准确率计算过程，不再需要在浏览器控制台查看日志。

## 🚀 如何生成和查看报告

### 1. 生成HTML报告
1. 启动应用，选择`complex`数据集
2. 等待数据加载完成
3. 点击页面上的"生成HTML报告"按钮
4. 系统会自动下载一个HTML文件

### 2. 查看报告内容
用浏览器打开下载的HTML文件，你将看到以下详细内容：

## 📋 报告内容概览

### 🎯 关键指标一览
- **总案例数**：数据集中的总案例数量
- **有标注数据**：有人工标注数据的案例数量
- **基准数据解析成功**：成功解析为基准数据的案例数量  
- **准确率计算成功**：成功计算出准确率的案例数量

### 🔧 解析器性能汇总表
清晰的表格显示每个解析器的：
- **解析成功率**：成功产生结果的比例
- **表格解析率**：成功解析出表格的比例
- **平均准确率**：最终的平均准确率结果
- **有效案例数**：参与平均准确率计算的案例数量

### 🧮 详细计算过程（重点！）
对于每个解析器，报告会显示：

#### 计算公式说明
```
平均准确率 = Σ(有效案例准确率) ÷ 有效案例数量
有效案例定义：解析器有产生结果 AND 有可用的基准数据 AND 准确率计算成功（不为null）
```

#### 具体计算步骤
- **总案例数**：该解析器处理的总案例数量
- **解析成功**：成功产生解析结果的案例数量
- **有效准确率案例**：最终参与平均准确率计算的案例数量（**分母**）
- **准确率总和**：所有有效案例的准确率总和（**分子**）
- **计算过程**：显示具体的除法运算：`总和 ÷ 有效案例数 = 平均准确率`

#### 参与计算的案例列表
- 列出所有参与平均准确率计算的案例
- 显示每个案例的文件名和具体准确率
- 用颜色区分准确率高低（绿色：80%+，黄色：60-80%，红色：<60%）

#### 未参与计算的案例列表
- 列出所有未参与计算的案例
- 显示未参与的原因（无解析结果 / 准确率计算失败）

### 📝 案例明细分析
表格形式显示前50个案例的详细状态：
- 案例编号和文件名
- 是否有标注数据
- 标注数据类型（JSON Schema / Table Content）
- 基准数据是否解析成功
- 准确率计算是否成功

## 🔍 问题诊断指南

### 如果看到平均准确率为"-"

查看该解析器的详细计算过程：

1. **检查"有效准确率案例"数量**
   - 如果为0，说明没有任何案例成功计算出准确率
   - 这是平均准确率显示"-"的直接原因

2. **查看"未参与计算的案例列表"**
   - 如果大部分案例显示"无解析结果"，说明解析器本身有问题
   - 如果大部分案例显示"准确率计算失败"，说明基准数据解析有问题

3. **查看数据集概览中的关键指标**
   - "基准数据解析成功"如果很低，说明标注数据解析有问题
   - "有标注数据"如果很低，说明数据集本身缺少标注

### 典型问题场景

#### 场景1：标注数据覆盖率低
- **现象**：数据集概览显示"有标注数据"比例很低
- **原因**：数据集中大部分案例没有人工标注数据
- **解决**：检查数据集的标注数据完整性

#### 场景2：基准数据解析率低
- **现象**："有标注数据"高但"基准数据解析成功"低
- **原因**：标注数据格式有问题，无法解析为基准表格
- **解决**：检查标注数据的JSON Schema格式是否正确

#### 场景3：解析器表现差异大
- **现象**：不同解析器的平均准确率差异很大
- **原因**：解析器对特定类型表格的处理能力不同
- **解决**：分析"参与计算的案例列表"，看哪些案例准确率特别低

## 💡 使用建议

1. **先查看数据集概览**，了解整体数据质量
2. **重点关注"详细计算过程"**，理解为什么某些解析器平均准确率为"-"
3. **对比不同解析器的"参与计算的案例列表"**，找出各自的优势和劣势
4. **查看"案例明细分析"**，了解数据集中每个案例的状态

这个HTML报告提供了完整的透明度，让你清楚地看到每个平均准确率是如何计算出来的，以及为什么某些解析器显示"-"。 