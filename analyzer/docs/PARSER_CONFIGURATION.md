# 解析器配置管理系统

## 概述

为了解决解析器性能对比、指标汇总、HTML报告的解析器性能对比，跟解析详情里面的多路解析的展示顺序不一致的问题，我们实现了一个统一的解析器配置管理系统。

## 主要功能

### 1. 统一的解析器配置

所有解析器的配置现在统一管理在 `src/utils/parserConfig.js` 文件中：

- **解析器顺序**：统一定义解析器的展示顺序
- **解析器信息**：包括显示名称、数据路径、结果检查函数等
- **解析器分类**：按功能对解析器进行分组

### 2. 多路解析复选框开关

在数据集选择器位置添加了多路解析的复选框开关，用户可以：

- **选择解析器**：通过复选框选择需要展示的解析器
- **分类管理**：按解析器类别进行批量选择
- **状态保存**：选择状态会保存到本地存储

### 3. 动态内容渲染

所有相关组件现在都会根据用户选择的解析器动态渲染内容：

- **解析详情**：只显示选中的解析器结果
- **指标汇总**：只计算和显示选中解析器的指标
- **HTML报告**：只包含选中解析器的性能对比
- **数据集报告**：只统计选中解析器的数据

## 技术实现

### 核心文件

1. **`src/utils/parserConfig.js`** - 解析器配置管理
2. **`src/contexts/ParserContext.js`** - 全局解析器状态管理
3. **`src/components/ParserSelector.js`** - 解析器选择器组件
4. **`src/components/ParserRow.js`** - 动态解析器行组件

### 配置结构

```javascript
const PARSER_CONFIGS = [
  {
    key: 'monkeyOCRLocal',
    displayName: 'MonkeyOCR(local)',
    dataPath: 'monkeyOCRLocal',
    hasResultCheck: (caseData) => !!caseData.monkeyOCRLocal?.result,
    enabled: true,
    category: 'monkey_ocr'
  },
  // ... 其他解析器配置
];
```

### 使用方法

#### 在组件中使用解析器上下文

```javascript
import { useParserContext } from '../contexts/ParserContext';

function MyComponent() {
  const { enabledParsers, updateEnabledParsers } = useParserContext();
  
  // 使用启用的解析器列表
  const availableParsers = getAvailableParserConfigs(caseData, enabledParsers);
  
  return (
    <div>
      {availableParsers.map(config => (
        <ParserRow key={config.key} parserConfig={config} />
      ))}
    </div>
  );
}
```

#### 获取解析器信息

```javascript
import { getParserDisplayName, hasParserResult } from '../utils/parserConfig';

// 获取显示名称
const displayName = getParserDisplayName('monkeyOCRLocal');

// 检查是否有结果
const hasResult = hasParserResult(caseData, 'monkeyOCRLocal');
```

## 用户界面

### 解析器选择器

- **位置**：数据集选择器下方
- **功能**：
  - 显示当前选中的解析器数量
  - 点击展开/收起选择面板
  - 按分类显示解析器选项
  - 支持全选/全不选操作
  - 支持按分类批量选择

### 动态内容展示

- **解析详情**：根据选择的解析器动态生成行
- **指标图表**：只显示选中解析器的数据
- **性能报告**：只包含选中解析器的统计信息

## 数据持久化

用户的解析器选择会自动保存到浏览器的 localStorage 中，下次访问时会自动恢复选择状态。

## 兼容性

系统保持向后兼容，现有的代码可以继续使用，同时逐步迁移到新的配置系统。

## 扩展性

添加新解析器时，只需要在 `PARSER_CONFIGS` 中添加相应配置即可，所有相关组件会自动支持新解析器。

## 注意事项

1. 解析器的 `key` 必须与数据中的字段名对应
2. `hasResultCheck` 函数需要正确检查数据是否存在
3. 修改解析器配置后，相关的类型定义和测试也需要更新
4. 确保所有使用解析器列表的地方都使用统一的配置系统
