# TableRAG Analyzer

基于React的表格解析结果分析工具，用于替代原有的单一HTML报告，提供更好的用户体验和性能。

## 功能特性

- 🚀 **现代化界面**: 基于React构建的响应式用户界面
- 📊 **异步数据加载**: 通过HTTP异步获取数据，避免单一文件过大
- 🖼️ **图片懒加载**: 优化图片加载性能，支持点击放大预览
- 📋 **虚拟滚动**: 处理大量数据时保持流畅的用户体验
- 🔄 **实时切换**: 支持在不同数据集之间快速切换
- 📱 **响应式设计**: 适配各种屏幕尺寸
- ⚡ **性能优化**: 内存管理、组件优化、防抖节流等

## 项目结构

```
analyzer/
├── public/
│   └── index.html          # HTML模板
├── src/
│   ├── components/         # React组件
│   │   ├── DatasetSelector.js    # 数据集选择器
│   │   ├── CaseList.js          # 案例列表
│   │   ├── CaseItem.js          # 案例项
│   │   ├── CaseDetail.js        # 案例详情（3行布局）
│   │   ├── ImageModal.js        # 图片预览模态框
│   │   ├── TableRenderer.js     # 表格渲染器
│   │   ├── LazyImage.js         # 懒加载图片组件
│   │   └── VirtualList.js       # 虚拟滚动列表
│   ├── services/           # API服务
│   │   └── api.js              # HTTP API封装
│   ├── hooks/              # 自定义Hooks
│   │   └── useVirtualization.js # 虚拟化相关Hooks
│   ├── utils/              # 工具函数
│   │   └── dataProcessor.js    # 数据处理工具
│   ├── styles/             # 样式文件
│   │   └── index.css           # 全局样式
│   ├── App.js              # 主应用组件
│   └── index.js            # 应用入口
├── package.json            # 项目配置
└── README.md              # 项目文档
```

## 安装和运行

### 前置条件

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
cd analyzer
npm install
```

### 启动开发服务器

```bash
npm start
```

应用将在 http://localhost:3000 启动。

### 构建生产版本

```bash
npm run build
```

构建文件将输出到 `build/` 目录。

## 使用说明

### 1. 启动HTTP服务器

在项目根目录（enhance/）启动HTTP服务器：

```bash
python -m http.server 10000
```

这将在 http://localhost:10000 提供静态文件服务。

### 2. 访问应用

打开浏览器访问 http://localhost:3000，即可使用TableRAG分析器。

### 3. 功能使用

1. **选择数据集**: 在顶部下拉框中选择要分析的数据集
2. **浏览案例**: 左侧列表显示所有测试案例，点击选择
3. **查看详情**: 右侧显示案例详情，包括：
   - 序号和数据信息（合并单元格，跨3行）
   - 原始文本列：显示各OCR的原始输出
   - 渲染结果列：显示表格渲染结果
   - 准确率报告列：显示各OCR的准确率对比
4. **图片预览**: 点击图片可放大查看
5. **表格渲染**: 支持Markdown表格到HTML的转换

## 技术栈

- **React 18**: 现代化前端框架
- **Axios**: HTTP客户端
- **Styled Components**: CSS-in-JS样式方案
- **React Markdown**: Markdown渲染
- **React Window**: 虚拟滚动
- **Intersection Observer API**: 懒加载实现

## 性能优化

### 1. 虚拟滚动
- 使用`react-window`实现虚拟滚动
- 只渲染可见区域的组件
- 支持大量数据的流畅滚动

### 2. 懒加载
- 图片懒加载，只加载可见图片
- 使用Intersection Observer API
- 减少初始加载时间

### 3. 组件优化
- 使用`React.memo`避免不必要的重渲染
- 使用`useCallback`和`useMemo`优化函数和计算
- 合理的组件拆分和状态管理

### 4. 内存管理
- 及时清理事件监听器
- 避免内存泄漏
- 优化大数据处理

## API接口

应用通过统一后端服务获取数据：

- `GET /api/datasets` - 获取数据集列表
- `GET /api/datasets/{name}/images` - 获取图片列表
- `GET /api/datasets/{name}/parse_results` - 获取解析结果
- `GET /api/annotations` - 获取标注数据
- `GET /static/dataset/{name}/images/{filename}` - 获取图片文件

## 浏览器支持

- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

## 开发指南

### 添加新组件

1. 在`src/components/`目录创建组件文件
2. 创建对应的CSS文件
3. 在需要的地方导入使用

### 添加新API

1. 在`src/services/api.js`中添加API函数
2. 在组件中调用API函数
3. 处理加载状态和错误状态

### 性能优化建议

1. 使用React DevTools分析性能
2. 避免在render中创建新对象
3. 合理使用useCallback和useMemo
4. 考虑代码分割和懒加载

## 故障排除

### 常见问题

1. **无法加载数据集**
   - 检查backend服务是否启动（端口8000）
   - 检查数据集目录是否存在
   - 确认前端代理配置正确

2. **图片无法显示**
   - 检查backend静态文件服务是否正常
   - 确认图片路径使用正确的API格式
   - 检查CORS配置

3. **表格渲染异常**
   - 检查JSON Schema格式是否正确
   - 查看浏览器控制台错误信息
   - 确认数据处理函数正常工作

### 调试模式

开发模式下，应用会在控制台输出详细的调试信息，包括：
- API请求和响应
- 组件渲染信息
- 性能指标

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License
