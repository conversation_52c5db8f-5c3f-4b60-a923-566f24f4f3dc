<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>准确率计算调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .debug-section h3 {
            margin-top: 0;
            color: #333;
        }
        .log-output {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 准确率计算调试页面</h1>
        <p>此页面用于调试analyzer前端的准确率计算问题</p>

        <div class="debug-section">
            <h3>📊 当前状态</h3>
            <div id="status-info" class="status info">
                正在检查系统状态...
            </div>
        </div>

        <div class="debug-section">
            <h3>🎛️ 日志控制</h3>
            <button class="button" onclick="setLogLevel('DEBUG')">启用详细日志 (DEBUG)</button>
            <button class="button" onclick="setLogLevel('INFO')">启用信息日志 (INFO)</button>
            <button class="button" onclick="setLogLevel('WARN')">只显示警告 (WARN)</button>
            <button class="button" onclick="setLogLevel('ERROR')">只显示错误 (ERROR)</button>
            <button class="button" onclick="clearLogs()">清除日志</button>
        </div>

        <div class="debug-section">
            <h3>🧪 测试功能</h3>
            <button class="button" onclick="testImageLoading()">测试图片加载</button>
            <button class="button" onclick="testDataLoading()">测试数据加载</button>
            <button class="button" onclick="testAccuracyCalculation()">测试准确率计算</button>
            <button class="button" onclick="getLogStats()">查看日志统计</button>
        </div>

        <div class="debug-section">
            <h3>📝 实时日志输出</h3>
            <div id="log-output" class="log-output">
                等待日志输出...
            </div>
        </div>

        <div class="debug-section">
            <h3>📋 调试说明</h3>
            <ul>
                <li><strong>图片加载问题</strong>: 检查图片URL是否正确指向backend服务</li>
                <li><strong>准确率计算问题</strong>: 检查标注数据和解析结果的格式匹配</li>
                <li><strong>日志级别</strong>: 
                    <ul>
                        <li>DEBUG: 显示所有调试信息（可能很多）</li>
                        <li>INFO: 显示重要信息和调试信息</li>
                        <li>WARN: 只显示警告和错误</li>
                        <li>ERROR: 只显示错误</li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // 日志输出区域
        const logOutput = document.getElementById('log-output');
        const statusInfo = document.getElementById('status-info');

        // 拦截console输出并显示在页面上
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info
        };

        function addLogEntry(level, message, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${level.toUpperCase()}: ${message} ${args.length > 0 ? JSON.stringify(args) : ''}`;
            logOutput.textContent += logEntry + '\n';
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // 调用原始console方法
            originalConsole[level](message, ...args);
        }

        // 重写console方法
        console.log = (message, ...args) => addLogEntry('log', message, ...args);
        console.warn = (message, ...args) => addLogEntry('warn', message, ...args);
        console.error = (message, ...args) => addLogEntry('error', message, ...args);
        console.info = (message, ...args) => addLogEntry('info', message, ...args);

        // 测试函数
        function testImageLoading() {
            console.info('🖼️ 测试图片加载...');
            const testImageUrl = 'http://localhost:8000/static/dataset/complex/images/00433b9c53b0e0dec4d6068e09735427.png';
            
            const img = new Image();
            img.onload = () => {
                console.info('✅ 图片加载成功:', testImageUrl);
                updateStatus('success', '图片加载测试通过');
            };
            img.onerror = () => {
                console.error('❌ 图片加载失败:', testImageUrl);
                updateStatus('error', '图片加载测试失败');
            };
            img.src = testImageUrl;
        }

        function testDataLoading() {
            console.info('📊 测试数据加载...');
            
            // 测试案例数据
            fetch('/api/datasets/complex/cases')
                .then(response => response.json())
                .then(data => {
                    console.info('✅ 案例数据加载成功，数量:', data.length);
                    
                    // 测试标注数据
                    return fetch('/api/datasets/complex/annotations');
                })
                .then(response => response.json())
                .then(data => {
                    console.info('✅ 标注数据加载成功，数量:', data.length);
                    updateStatus('success', '数据加载测试通过');
                })
                .catch(error => {
                    console.error('❌ 数据加载失败:', error);
                    updateStatus('error', '数据加载测试失败: ' + error.message);
                });
        }

        function testAccuracyCalculation() {
            console.info('🧮 测试准确率计算...');
            
            // 这里需要等待主应用加载完成
            if (typeof window.calculateMetricsForCase === 'function') {
                console.info('✅ 准确率计算函数存在');
                updateStatus('success', '准确率计算函数可用');
            } else {
                console.warn('⚠️ 准确率计算函数不存在，请在主应用中测试');
                updateStatus('info', '请在主应用页面中测试准确率计算');
            }
        }

        function clearLogs() {
            logOutput.textContent = '日志已清除...\n';
        }

        function updateStatus(type, message) {
            statusInfo.className = `status ${type}`;
            statusInfo.textContent = message;
        }

        // 检查系统状态
        function checkSystemStatus() {
            console.info('🔍 检查系统状态...');
            
            // 检查backend连接
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    console.info('✅ Backend服务正常');
                    updateStatus('success', 'Backend服务连接正常');
                })
                .catch(error => {
                    console.warn('⚠️ Backend服务连接失败:', error);
                    updateStatus('error', 'Backend服务连接失败');
                });
        }

        // 页面加载时检查状态
        window.addEventListener('load', () => {
            console.info('🚀 调试页面已加载');
            checkSystemStatus();
        });

        // 全局函数，供按钮调用
        window.setLogLevel = function(level) {
            console.info(`🎛️ 设置日志级别为: ${level}`);
            if (window.tableragLogger) {
                window.tableragLogger.setLevel(level);
                updateStatus('success', `日志级别已设置为: ${level}`);
            } else {
                console.warn('⚠️ Logger未找到，请在主应用中设置');
                updateStatus('info', '请在主应用页面中设置日志级别');
            }
        };

        window.getLogStats = function() {
            console.info('📊 获取日志统计...');
            if (window.tableragLogger) {
                const stats = window.tableragLogger.getStats();
                console.info('日志统计:', stats);
                updateStatus('info', `日志统计: 总计${stats.totalMessages}条消息`);
            } else {
                console.warn('⚠️ Logger未找到');
                updateStatus('info', '请在主应用页面中查看日志统计');
            }
        };
    </script>
</body>
</html>
