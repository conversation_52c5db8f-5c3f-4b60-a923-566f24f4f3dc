import React, { createContext, useContext, useState } from 'react';

/**
 * 解析器上下文
 * 管理全局的解析器选择状态
 */
const ParserContext = createContext();

export const useParserContext = () => {
  const context = useContext(ParserContext);
  if (!context) {
    console.error('useParserContext must be used within a ParserProvider');
    // 返回默认值而不是抛出错误
    return {
      enabledParsers: [],
      updateEnabledParsers: () => {},
      resetToDefault: () => {},
      isParserEnabled: () => false,
      toggleParser: () => {}
    };
  }
  return context;
};

export const ParserProvider = ({ children }) => {
  // 简化版本，使用硬编码的默认值
  const defaultParsers = ['ocrflux', 'vlLLM', 'monkeyOCR', 'monkeyOCRV2', 'monkeyOCRLocal', 'monkeyOcrKas', 'kdcMarkdown', 'kdc<PERSON>lain', 'kdcKdc', 'mineru', 'mineru_vlm'];
  const [enabledParsers, setEnabledParsers] = useState(defaultParsers);

  const value = {
    enabledParsers,
    updateEnabledParsers: (newParsers) => setEnabledParsers(newParsers),
    resetToDefault: () => setEnabledParsers(defaultParsers),
    isParserEnabled: (key) => enabledParsers.includes(key),
    toggleParser: (key) => setEnabledParsers(prev =>
      prev.includes(key) ? prev.filter(k => k !== key) : [...prev, key]
    )
  };

  return (
    <ParserContext.Provider value={value}>
      {children}
    </ParserContext.Provider>
  );
};
