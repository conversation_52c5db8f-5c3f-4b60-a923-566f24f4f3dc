// 模拟 logger
const logger = {
  error: console.log,
  debug: console.log,
  warn: console.warn
};

// 测试数据
const testData = {
  type: 'mineru_vlm',
  renderData: [
    {
      type: "title",
      text: "测试标题",
      bbox: [100, 100, 400, 150]
    },
    {
      type: "table",
      text: "| 列1 | 列2 |\n| --- | --- |\n| 数据1 | 数据2 |",
      bbox: [100, 200, 400, 300]
    }
  ]
};

// 模拟 MineruVlmViewer 组件的渲染逻辑
const renderMineruVlmViewer = (layoutData) => {
  if (!layoutData || !Array.isArray(layoutData)) {
    console.log('无布局数据');
    return;
  }

  console.log('渲染 MineruVlmViewer:');
  layoutData.forEach((item, index) => {
    const { type, text, bbox } = item;
    console.log(`\n[项目 ${index + 1}]`);
    console.log('类型:', type);
    console.log('文本:', text);
    console.log('位置:', bbox);
  });
};

// 测试渲染
console.log('\n=== 测试渲染组件 ===\n');
renderMineruVlmViewer(testData.renderData);