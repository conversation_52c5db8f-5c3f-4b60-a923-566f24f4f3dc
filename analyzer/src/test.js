const fs = require('fs');
const path = require('path');
const { processParseResults } = require('./utils/dataProcessor');

// 读取测试数据
const testData = JSON.parse(fs.readFileSync(path.join(__dirname, '../../parse_results/kingsoft/test_case.json')));

// 测试数据处理
const processedCases = processParseResults(testData, ['test.png']);

// 检查结果
console.log('处理结果:', {
  totalCases: processedCases.length,
  firstCase: processedCases[0],
  hasMineruVlm: !!processedCases[0]?.mineru_vlm,
  mineruVlmSuccess: processedCases[0]?.mineru_vlm?.success,
  hasResult: !!processedCases[0]?.mineru_vlm?.result,
  hasLayoutData: !!processedCases[0]?.mineru_vlm?.result?.layout_data,
  layoutDataType: typeof processedCases[0]?.mineru_vlm?.result?.layout_data,
  isLayoutDataArray: Array.isArray(processedCases[0]?.mineru_vlm?.result?.layout_data),
  layoutDataLength: processedCases[0]?.mineru_vlm?.result?.layout_data?.length,
  fullMineruVlmData: processedCases[0]?.mineru_vlm
});