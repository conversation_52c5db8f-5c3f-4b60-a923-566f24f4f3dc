import React, { useRef, useEffect, useState } from 'react';
import logger from '../utils/logger';

/**
 * KDC Canvas渲染器 - 支持所有类型的bbox渲染并使用不同颜色区分
 */
const KdcCanvasRenderer = ({ kdcData, placeholder = "无KDC渲染结果" }) => {
  const canvasRef = useRef(null);
  const modalCanvasRef = useRef(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [textBlocks, setTextBlocks] = useState([]);
  const [pageSize, setPageSize] = useState({ width: 14750, height: 6510 });

  // 定义不同类型的颜色方案
  const TYPE_COLORS = {
    textbox: {
      background: 'rgba(135, 206, 250, ALPHA)',  // 浅蓝色 - 文本
      border: '#4169E1'                          // 皇家蓝
    },
    table: {
      background: 'rgba(144, 238, 144, ALPHA)',  // 浅绿色 - 表格
      border: '#32CD32'                          // 石灰绿
    },
    component: {
      background: 'rgba(255, 182, 193, ALPHA)',  // 浅粉色 - 组件
      border: '#FF69B4'                          // 热粉色
    },
    unknown: {
      background: 'rgba(255, 255, 0, ALPHA)',    // 浅黄色 - 未知类型
      border: '#FFD700'                          // 金色
    }
  };

  // 收集文本块的函数 - 支持所有类型的bbox渲染
  const collectTextBlocksForCanvas = (block, level = 0, textBlocks = []) => {
    if (!block || typeof block !== 'object') return textBlocks;

    // 处理blocks字段（包含textbox、table、component等类型）
    if (block.blocks && Array.isArray(block.blocks)) {
      for (const subBlock of block.blocks) {
        if (subBlock && typeof subBlock === 'object') {
          // 处理textbox类型的block
          if (subBlock.textbox && subBlock.bounding_box) {
            const bbox = subBlock.bounding_box;
            const textbox = subBlock.textbox;
            if (textbox.blocks && Array.isArray(textbox.blocks)) {
              for (const tbBlock of textbox.blocks) {
                if (tbBlock.para && tbBlock.para.runs) {
                  let textContent = '';
                  let fontSize = 10;
                  let isBold = false;

                  for (const run of tbBlock.para.runs) {
                    if (run.text) {
                      textContent += run.text;
                      // 提取字体信息
                      if (run.prop) {
                        if (run.prop.size) fontSize = run.prop.size;
                        if (run.prop.bold) isBold = run.prop.bold;
                      }
                    }
                  }

                  if (textContent.trim()) {
                    textBlocks.push({
                      text: textContent.trim(),
                      x1: bbox.x1 || 0,
                      y1: bbox.y1 || 0,
                      x2: bbox.x2 || 0,
                      y2: bbox.y2 || 0,
                      level: level,
                      type: 'textbox',
                      fontSize: fontSize,
                      isBold: isBold
                    });
                  }
                }
              }
            }
          }
          // 处理component类型的block（如图片）
          else if (subBlock.component && subBlock.bounding_box) {
            const bbox = subBlock.bounding_box;
            const component = subBlock.component;
            
            // 为component创建一个显示块
            textBlocks.push({
              text: component.type === 'image' ? '🖼️ Image' : `📦 ${component.type || 'Component'}`,
              x1: bbox.x1 || 0,
              y1: bbox.y1 || 0,
              x2: bbox.x2 || 0,
              y2: bbox.y2 || 0,
              level: level,
              type: 'component',
              fontSize: 12,
              isBold: true,
              componentInfo: component
            });
          }
          // 处理table类型的block
          else if (subBlock.table) {
            const table = subBlock.table;
            if (table.rows && Array.isArray(table.rows)) {
              for (const row of table.rows) {
                if (row.cells && Array.isArray(row.cells)) {
                  for (const cell of row.cells) {
                    if (cell.blocks && Array.isArray(cell.blocks)) {
                      // 递归处理cell中的blocks，标记为table类型
                      for (const cellBlock of cell.blocks) {
                        collectTextBlocksForCanvas(cellBlock, level + 2, textBlocks);
                      }
                      
                      // 为每个cell添加一个表格边框指示器
                      if (cell.bounding_box) {
                        const bbox = cell.bounding_box;
                        textBlocks.push({
                          text: '',  // 空文本，只显示边框
                          x1: bbox.x1 || 0,
                          y1: bbox.y1 || 0,
                          x2: bbox.x2 || 0,
                          y2: bbox.y2 || 0,
                          level: level + 1,
                          type: 'table',
                          fontSize: 10,
                          isBold: false,
                          isTableCell: true
                        });
                      }
                    }
                  }
                }
              }
            }
          }
          // 递归处理其他可能的嵌套结构
          else {
            collectTextBlocksForCanvas(subBlock, level + 1, textBlocks);
          }
        }
      }
    }

    // 处理直接的textbox（备用路径）
    if (block.textbox && block.bounding_box) {
      const bbox = block.bounding_box;
      const textbox = block.textbox;
      if (textbox.blocks && Array.isArray(textbox.blocks)) {
        for (const subBlock of textbox.blocks) {
          if (subBlock.para && subBlock.para.runs) {
            let textContent = '';
            let fontSize = 10;
            let isBold = false;

            for (const run of subBlock.para.runs) {
              if (run.text) {
                textContent += run.text;
                if (run.prop) {
                  if (run.prop.size) fontSize = run.prop.size;
                  if (run.prop.bold) isBold = run.prop.bold;
                }
              }
            }

            if (textContent.trim()) {
              textBlocks.push({
                text: textContent.trim(),
                x1: bbox.x1 || 0,
                y1: bbox.y1 || 0,
                x2: bbox.x2 || 0,
                y2: bbox.y2 || 0,
                level: level,
                type: 'textbox',
                fontSize: fontSize,
                isBold: isBold
              });
            }
          }
        }
      }
    }

    // 递归处理子块
    if (block.children && Array.isArray(block.children)) {
      for (const child of block.children) {
        collectTextBlocksForCanvas(child, level + 1, textBlocks);
      }
    }

    return textBlocks;
  };

  // 获取块类型对应的颜色
  const getTypeColors = (type, alpha) => {
    const colors = TYPE_COLORS[type] || TYPE_COLORS.unknown;
    return {
      background: colors.background.replace('ALPHA', alpha),
      border: colors.border
    };
  };

  // 绘制Canvas的函数 - 参考src中的drawCanvas逻辑
  const drawCanvas = (canvas, scaleFactor) => {
    if (!canvas || !textBlocks.length) return;

    const ctx = canvas.getContext('2d');

    // 绘制背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    logger.debug(`Drawing canvas with ${textBlocks.length} text blocks, scale factor: ${scaleFactor}`);
    logger.debug(`Page size: ${pageSize.width} × ${pageSize.height}, Canvas size: ${canvas.width} × ${canvas.height}`);

    // 绘制文本块
    textBlocks.forEach((block, index) => {
      const x1 = Math.floor(block.x1 * scaleFactor);
      const y1 = Math.floor(block.y1 * scaleFactor);
      const x2 = Math.floor(block.x2 * scaleFactor);
      const y2 = Math.floor(block.y2 * scaleFactor);
      const width = x2 - x1;
      const height = y2 - y1;

      logger.debug(`Block ${index}: "${block.text}" [${block.type}] at (${x1}, ${y1}) size ${width}x${height}`);

      // 跳过无效的块
      if (width <= 0 || height <= 0) {
        logger.warn(`Block ${index} has invalid dimensions, skipping`);
        return;
      }

      // 计算透明度（根据层级调整）
      const bgAlpha = Math.min(0.08 + (block.level * 0.03), 0.3);
      
      // 获取块类型对应的颜色
      const typeColors = getTypeColors(block.type || 'unknown', bgAlpha);

      // 绘制背景（使用类型对应的颜色）
      ctx.fillStyle = typeColors.background;
      ctx.fillRect(x1, y1, width, height);

      // 绘制边框（使用类型对应的颜色）
      ctx.strokeStyle = typeColors.border;
      ctx.lineWidth = block.type === 'component' ? 2 : 1; // component类型使用更粗的边框
      ctx.strokeRect(x1, y1, width, height);

      // 对于表格cell，只绘制边框，不绘制文本
      if (block.isTableCell) {
        return;
      }

      // 确保我们有有效的文本
      const text = String(block.text || '').trim();
      if (!text) {
        logger.warn(`Block ${index} has no text content`);
        return;
      }

      // 根据框的实际大小计算合适的字体大小，但确保可读性
      let fontSize = 12;
      if (width < 30) fontSize = 8;
      else if (width < 60) fontSize = 10;
      else if (width < 100) fontSize = 12;
      else if (width < 150) fontSize = 14;
      else if (width < 200) fontSize = 16;
      else if (width < 300) fontSize = 18;
      else fontSize = 20;

      // 根据高度进一步限制字体大小
      const maxFontByHeight = Math.floor(height * 0.5); // 字体不超过框高度的50%
      fontSize = Math.min(fontSize, maxFontByHeight);

      // 确保最小可读字体大小，特别是对于大页面
      const minReadableFont = scaleFactor < 0.1 ? 10 : 8;
      fontSize = Math.max(fontSize, minReadableFont);

      // 最终字体大小限制
      fontSize = Math.max(8, Math.min(fontSize, 24));

      logger.debug(`Block ${index}: fontSize=${fontSize}, text="${text}", type=${block.type}`);

      // 设置文本样式
      ctx.save();  // 保存当前状态
      ctx.font = `${block.isBold ? 'bold' : 'normal'} ${fontSize}px Arial`;
      ctx.fillStyle = '#000000';
      ctx.textAlign = 'left';
      ctx.textBaseline = 'top';

      // 计算文本布局
      const padding = Math.max(4, Math.min(width * 0.1, height * 0.1)); // 动态边距
      const textX = x1 + padding;
      const textY = y1 + padding;
      const maxTextWidth = Math.max(20, width - padding * 2);
      const maxTextHeight = Math.max(fontSize, height - padding * 2);

      // 智能文本换行处理
      const lines = wrapText(ctx, text, maxTextWidth);

      // 限制行数以适应框高度
      const lineHeight = fontSize + 2;
      const maxLines = Math.floor(maxTextHeight / lineHeight);
      const displayLines = lines.slice(0, maxLines);

      // 如果文本被截断，在最后一行添加省略号
      if (lines.length > maxLines && displayLines.length > 0) {
        let lastLine = displayLines[displayLines.length - 1];
        while (ctx.measureText(lastLine + '...').width > maxTextWidth && lastLine.length > 1) {
          lastLine = lastLine.slice(0, -1);
        }
        displayLines[displayLines.length - 1] = lastLine + '...';
      }

      // 绘制多行文本
      if (textX >= 0 && textY >= 0 && textX < canvas.width && textY < canvas.height) {
        displayLines.forEach((line, lineIndex) => {
          const lineY = textY + (lineIndex * lineHeight);
          if (lineY + fontSize <= y2 - padding) {
            logger.debug(`Drawing line ${lineIndex}: "${line}" at (${textX}, ${lineY})`);
            ctx.fillText(line, textX, lineY);
          }
        });

        logger.debug(`Drew ${displayLines.length} lines for block ${index}`);
      } else {
        logger.warn(`Text position (${textX}, ${textY}) is outside canvas bounds`);
      }

      ctx.restore();  // 恢复状态

      // 绘制类型标识和调试信息
      ctx.save();
      ctx.font = 'bold 10px Arial';
      ctx.fillStyle = '#333333';
      ctx.textAlign = 'right';
      ctx.textBaseline = 'bottom';
      // 在右下角显示类型信息
      const typeText = `${block.type}#${index}`;
      ctx.fillText(typeText, x2 - 2, y2 - 2);
      ctx.restore();
    });

    logger.debug('Finished drawing text blocks');
  };

  useEffect(() => {
    logger.debug('KdcCanvasRenderer received kdcData:', kdcData);
    logger.debug('KdcCanvasRenderer data check:', {
      hasKdcData: !!kdcData,
      hasData: !!(kdcData && kdcData.data),
      isDataArray: !!(kdcData && kdcData.data && Array.isArray(kdcData.data)),
      dataLength: kdcData && kdcData.data ? kdcData.data.length : 0
    });
    
    if (!kdcData || !kdcData.data || !Array.isArray(kdcData.data) || kdcData.data.length === 0) {
      logger.debug('KdcCanvasRenderer: Data validation failed, setting loading to false');
      setIsLoading(false);
      return;
    }

    const renderKdcToCanvas = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const kdcDoc = kdcData.data[0];
        if (!kdcDoc || !kdcDoc.doc) {
          setError('KDC文档结构无效');
          setIsLoading(false);
          return;
        }

        const doc = kdcDoc.doc;

        // 获取页面属性
        const pageProps = doc.prop?.page_props?.[0];
        let pageWidth = 14750;
        let pageHeight = 6510;

        if (pageProps && pageProps.size) {
          pageWidth = pageProps.size.width;
          pageHeight = pageProps.size.height;
        }

        setPageSize({ width: pageWidth, height: pageHeight });

        // 收集所有文本块
        const blocks = [];
        const tree = doc.tree;
        if (tree && tree.children) {
          for (const child of tree.children) {
            collectTextBlocksForCanvas(child, 0, blocks);
          }
        }

        setTextBlocks(blocks);

        if (blocks.length === 0) {
          setError('KDC文档中没有找到文本块');
          setIsLoading(false);
          return;
        }

        logger.debug(`Found ${blocks.length} text blocks`);
        setIsLoading(false);
      } catch (err) {
        logger.error('KDC渲染错误:', err);
        setError(`渲染失败: ${err.message}`);
        setIsLoading(false);
      }
    };

    renderKdcToCanvas();
  }, [kdcData]);

  // 文本换行辅助函数（改进版，适合中文）
  const wrapText = (ctx, text, maxWidth) => {
    if (maxWidth <= 0) return [text];

    const lines = [];
    let currentLine = '';

    // 如果文本很短，直接返回
    if (ctx.measureText(text).width <= maxWidth) {
      return [text];
    }

    // 逐字符处理
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const testLine = currentLine + char;
      const testWidth = ctx.measureText(testLine).width;

      if (testWidth > maxWidth && currentLine.length > 0) {
        lines.push(currentLine);
        currentLine = char;
      } else {
        currentLine = testLine;
      }
    }

    if (currentLine.length > 0) {
      lines.push(currentLine);
    }

    // 如果没有成功分行且文本太长，强制截断
    if (lines.length === 0 && text.length > 0) {
      lines.push(text.substring(0, Math.max(1, Math.floor(text.length / 2))));
      if (text.length > 1) {
        lines.push(text.substring(Math.floor(text.length / 2)));
      }
    }

    return lines;
  };

  // 当textBlocks更新时重新绘制canvas
  useEffect(() => {
    if (textBlocks.length > 0 && canvasRef.current) {
      const canvas = canvasRef.current;

      // 计算合适的缩放比例和Canvas尺寸
      const targetWidth = pageSize.width < 10000 ? 1000 : 1400;
      const targetHeight = pageSize.height < 10000 ? 700 : 1000;

      const scaleFactor = Math.min(targetWidth / pageSize.width, targetHeight / pageSize.height);

      // 确保最小缩放比例，避免文字太小
      const minScale = 0.05;
      const finalScale = Math.max(scaleFactor, minScale);

      const canvasWidth = Math.max(800, Math.floor(pageSize.width * finalScale));
      const canvasHeight = Math.max(600, Math.floor(pageSize.height * finalScale));

      canvas.width = canvasWidth;
      canvas.height = canvasHeight;
      canvas.style.width = `${canvasWidth}px`;
      canvas.style.height = `${canvasHeight}px`;

      // 绘制canvas
      drawCanvas(canvas, finalScale);
    }
  }, [textBlocks, pageSize]);

  // 打开大图模态窗口
  const openLargeCanvas = () => {
    setShowModal(true);

    // 延迟绘制大图，确保模态窗口已显示
    setTimeout(() => {
      if (modalCanvasRef.current && textBlocks.length > 0) {
        const modalCanvas = modalCanvasRef.current;

        // 计算模态窗口可用空间
        const modalWidth = window.innerWidth * 0.85;
        const modalHeight = window.innerHeight * 0.75;

        // 对于大页面，使用更智能的缩放策略
        let targetWidth, targetHeight;
        if (pageSize.width > 10000) {
          // 超大页面：使用更大的目标尺寸
          targetWidth = Math.min(modalWidth, 2000);
          targetHeight = Math.min(modalHeight, 1400);
        } else {
          // 普通页面：充分利用模态窗口空间
          targetWidth = modalWidth;
          targetHeight = modalHeight;
        }

        // 计算缩放比例
        const scaleX = targetWidth / pageSize.width;
        const scaleY = targetHeight / pageSize.height;
        let largeFactor = Math.min(scaleX, scaleY) * 0.95; // 留5%边距

        // 确保最小缩放比例，让文字可读
        const minLargeFactor = 0.1; // 最小10%缩放
        const maxLargeFactor = 3.0; // 最大300%缩放
        largeFactor = Math.max(minLargeFactor, Math.min(largeFactor, maxLargeFactor));

        const largeWidth = Math.floor(pageSize.width * largeFactor);
        const largeHeight = Math.floor(pageSize.height * largeFactor);

        logger.debug(`Large canvas: page ${pageSize.width}x${pageSize.height}, modal ${modalWidth}x${modalHeight}, factor ${largeFactor}, canvas ${largeWidth}x${largeHeight}`);

        modalCanvas.width = largeWidth;
        modalCanvas.height = largeHeight;
        modalCanvas.style.width = `${largeWidth}px`;
        modalCanvas.style.height = `${largeHeight}px`;

        // 绘制大图
        drawCanvas(modalCanvas, largeFactor);
      }
    }, 100);
  };

  // 关闭大图模态窗口
  const closeLargeCanvas = () => {
    setShowModal(false);
  };

  if (isLoading) {
    return (
      <div className="kdc-canvas-container">
        <div className="loading-message">正在渲染KDC文档...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="kdc-canvas-container">
        <div className="error-message">KDC渲染错误: {error}</div>
      </div>
    );
  }

  if (!kdcData || !kdcData.data || !Array.isArray(kdcData.data) || kdcData.data.length === 0) {
    return (
      <div className="kdc-canvas-container">
        <div className="placeholder-message">{placeholder}</div>
      </div>
    );
  }

  return (
    <div className="kdc-canvas-container">
      <div style={{ border: '1px solid #ddd', borderRadius: '4px', background: '#f8f9fa', padding: '10px' }}>
        {/* 颜色图例 */}
        <div style={{ marginBottom: '10px', padding: '8px', background: '#ffffff', borderRadius: '4px', border: '1px solid #e0e0e0' }}>
          <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: '5px', color: '#333' }}>类型图例：</div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px', fontSize: '11px' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <div style={{ 
                width: '16px', 
                height: '16px', 
                backgroundColor: TYPE_COLORS.textbox.background.replace('ALPHA', '0.3'),
                border: `1px solid ${TYPE_COLORS.textbox.border}`,
                borderRadius: '2px'
              }}></div>
              <span>文本框 (textbox)</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <div style={{ 
                width: '16px', 
                height: '16px', 
                backgroundColor: TYPE_COLORS.table.background.replace('ALPHA', '0.3'),
                border: `1px solid ${TYPE_COLORS.table.border}`,
                borderRadius: '2px'
              }}></div>
              <span>表格 (table)</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <div style={{ 
                width: '16px', 
                height: '16px', 
                backgroundColor: TYPE_COLORS.component.background.replace('ALPHA', '0.3'),
                border: `2px solid ${TYPE_COLORS.component.border}`,
                borderRadius: '2px'
              }}></div>
              <span>组件 (component)</span>
            </div>
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              <div style={{ 
                width: '16px', 
                height: '16px', 
                backgroundColor: TYPE_COLORS.unknown.background.replace('ALPHA', '0.3'),
                border: `1px solid ${TYPE_COLORS.unknown.border}`,
                borderRadius: '2px'
              }}></div>
              <span>未知 (unknown)</span>
            </div>
          </div>
        </div>
        
        <canvas
          ref={canvasRef}
          style={{
            border: '1px solid #ccc',
            background: 'white',
            display: 'block',
            margin: '0 auto',
            cursor: 'pointer',
            maxWidth: '100%',
            height: 'auto'
          }}
          onClick={openLargeCanvas}
        />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666', textAlign: 'center' }}>
          原始尺寸: {pageSize.width} × {pageSize.height}
          <br />
          <span style={{ color: '#007bff', cursor: 'pointer' }} onClick={openLargeCanvas}>
            🔍 点击图片查看大图
          </span>
          <br />
          <span style={{ fontSize: '10px', color: '#999' }}>
            💡 不同颜色代表不同的数据类型，右下角显示类型标识
          </span>
        </div>
      </div>

      {/* 大图模态窗口 */}
      {showModal && (
        <div
          style={{
            display: 'block',
            position: 'fixed',
            zIndex: 1000,
            left: 0,
            top: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0,0,0,0.8)',
            overflow: 'auto'
          }}
          onClick={closeLargeCanvas}
        >
          <div
            style={{
              position: 'relative',
              margin: '2% auto',
              width: '95%',
              height: '95%',
              background: 'white',
              borderRadius: '8px',
              padding: '20px',
              display: 'flex',
              flexDirection: 'column'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <span
              style={{
                position: 'absolute',
                top: '10px',
                right: '15px',
                color: '#aaa',
                fontSize: '28px',
                fontWeight: 'bold',
                cursor: 'pointer',
                zIndex: 1001
              }}
              onClick={closeLargeCanvas}
            >
              &times;
            </span>
            <h3 style={{ margin: '0 0 15px 0', textAlign: 'center', color: '#333' }}>
              KDC 文档大图预览
            </h3>
            
            {/* 大图模式下的颜色图例 */}
            <div style={{ marginBottom: '10px', padding: '8px', background: '#f8f9fa', borderRadius: '4px', border: '1px solid #e0e0e0' }}>
              <div style={{ fontSize: '12px', fontWeight: 'bold', marginBottom: '5px', color: '#333' }}>类型图例：</div>
              <div style={{ display: 'flex', flexWrap: 'wrap', gap: '15px', fontSize: '11px' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <div style={{ 
                    width: '16px', 
                    height: '16px', 
                    backgroundColor: TYPE_COLORS.textbox.background.replace('ALPHA', '0.3'),
                    border: `1px solid ${TYPE_COLORS.textbox.border}`,
                    borderRadius: '2px'
                  }}></div>
                  <span>文本框 (textbox)</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <div style={{ 
                    width: '16px', 
                    height: '16px', 
                    backgroundColor: TYPE_COLORS.table.background.replace('ALPHA', '0.3'),
                    border: `1px solid ${TYPE_COLORS.table.border}`,
                    borderRadius: '2px'
                  }}></div>
                  <span>表格 (table)</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <div style={{ 
                    width: '16px', 
                    height: '16px', 
                    backgroundColor: TYPE_COLORS.component.background.replace('ALPHA', '0.3'),
                    border: `2px solid ${TYPE_COLORS.component.border}`,
                    borderRadius: '2px'
                  }}></div>
                  <span>组件 (component)</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <div style={{ 
                    width: '16px', 
                    height: '16px', 
                    backgroundColor: TYPE_COLORS.unknown.background.replace('ALPHA', '0.3'),
                    border: `1px solid ${TYPE_COLORS.unknown.border}`,
                    borderRadius: '2px'
                  }}></div>
                  <span>未知 (unknown)</span>
                </div>
              </div>
            </div>
            
            <div
              style={{
                flex: 1,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                overflow: 'auto',
                background: '#f8f9fa',
                borderRadius: '4px'
              }}
            >
              <canvas
                ref={modalCanvasRef}
                style={{
                  border: '1px solid #ccc',
                  background: 'white',
                  maxWidth: '95%',
                  maxHeight: '95%',
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                }}
              />
            </div>
            <div style={{ textAlign: 'center', padding: '10px 0', color: '#666', fontSize: '12px' }}>
              💡 提示：不同颜色代表不同的数据类型，右下角显示类型标识 (#序号)
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default KdcCanvasRenderer;
