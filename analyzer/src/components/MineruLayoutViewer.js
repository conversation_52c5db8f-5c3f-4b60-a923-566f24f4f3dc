import React, { useState, useEffect } from 'react';
import MineruLayoutCanvasRenderer from './MineruLayoutCanvasRenderer';
import './MineruLayoutViewer.css';
import logger from '../utils/logger';

const MineruLayoutViewer = ({ layoutData, caseData }) => {
  const [modelJsonContent, setModelJsonContent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取model.json文件内容
  const fetchModelJsonContent = async () => {
    if (!caseData?.mineru?.result?.model_json) {
      logger.warn('[MineruLayoutViewer] 没有model_json路径');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 从路径中提取相对路径
      const modelJsonPath = caseData.mineru.result.model_json;
      const relativePath = modelJsonPath.replace('/data/projects/kingsoft/personal/workspace/tablerag/enhance/', '');

      const response = await fetch(`http://localhost:8000/static/${relativePath}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const jsonContent = await response.json();
      setModelJsonContent(jsonContent);
      logger.debug('[MineruLayoutViewer] 成功获取model.json内容');
    } catch (err) {
      logger.error('[MineruLayoutViewer] 获取model.json失败:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModelJsonContent();
  }, [caseData]);

  if (!layoutData) {
    return (
      <div className="mineru-layout-empty">
        无版式识别结果
      </div>
    );
  }

  return (
    <div className="mineru-layout-viewer">
      <div className="mineru-layout-sections">
        {/* 原始文本部分 */}
        <div className="mineru-layout-section">
          <div className="mineru-layout-header">
            <h4>Mineru Layout 原始文本 (model.json)</h4>
          </div>
          <div className="mineru-layout-content">
            {loading && <div className="loading-message">正在加载model.json...</div>}
            {error && <div className="error-message">加载失败: {error}</div>}
            {modelJsonContent && (
              <pre className="mineru-layout-json">
                {JSON.stringify(modelJsonContent, null, 2)}
              </pre>
            )}
            {!loading && !error && !modelJsonContent && (
              <div className="placeholder-message">无model.json数据</div>
            )}
          </div>
        </div>

        {/* Canvas渲染部分 */}
        <div className="mineru-layout-section">
          <div className="mineru-layout-header">
            <h4>Mineru Layout 渲染结果</h4>
          </div>
          <div className="mineru-layout-content">
            <MineruLayoutCanvasRenderer
              layoutData={layoutData}
              placeholder="无Mineru Layout渲染结果"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MineruLayoutViewer;