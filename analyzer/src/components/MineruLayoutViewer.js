import React, { useState, useEffect } from 'react';
import MineruLayoutCanvasRenderer from './MineruLayoutCanvasRenderer';
import './MineruLayoutViewer.css';
import logger from '../utils/logger';

/**
 * Mineru Layout结果渲染组件 - 只显示canvas渲染结果
 */
const MineruLayoutViewer = ({ layoutData, caseData }) => {
  if (!layoutData) {
    return (
      <div className="mineru-layout-empty">
        无版式识别结果
      </div>
    );
  }

  // 只显示canvas渲染结果
  return (
    <MineruLayoutCanvasRenderer
      layoutData={layoutData}
      placeholder="无Mineru Layout渲染结果"
    />
  );
};

export default MineruLayoutViewer;