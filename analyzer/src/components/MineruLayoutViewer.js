import React from 'react';
import './MineruLayoutViewer.css';

const MineruLayoutViewer = ({ layoutData }) => {
  if (!layoutData) {
    return (
      <div className="mineru-layout-empty">
        无版式识别结果
      </div>
    );
  }

  // 渲染版式识别结果
  return (
    <div className="mineru-layout-viewer">
      <div className="mineru-layout-header">
        <h4>版式识别结果</h4>
      </div>
      <div className="mineru-layout-content">
        {/* 这里根据 layoutData 的具体结构来渲染内容 */}
        <pre className="mineru-layout-json">
          {JSON.stringify(layoutData, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default MineruLayoutViewer;