.parse-report {
  padding: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.parse-report-section {
  margin-bottom: 16px;
  padding: 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #fafbfc;
}

.parse-report-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-weight: 600;
  font-size: 13px;
  color: #495057;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 表格解析状态 */
.table-parsing-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-icon {
  font-weight: bold;
  font-size: 16px;
}

.status-icon.success {
  color: #28a745;
}

.status-icon.failed {
  color: #dc3545;
}

.status-text {
  font-weight: 500;
}

/* 指标网格 */
.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.metric-label {
  font-size: 13px;
  color: #6c757d;
  font-weight: 500;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.metric-value.good {
  color: #28a745;
}

.metric-value.poor {
  color: #dc3545;
}

.metric-value.medium {
  color: #ffc107;
}

/* 详细比对表格 */
.comparison-details {
  margin-top: 12px;
}

.comparison-table-container {
  margin-top: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  display: block;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
  table-layout: auto;
}

.comparison-table th,
.comparison-table td {
  padding: 4px 6px;
  border: 1px solid #ddd;
  text-align: left;
  vertical-align: top;
  white-space: nowrap;
}

.comparison-table th {
  background-color: #f8f9fa;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 1;
  font-size: 10px;
}

.comparison-table td {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 正确行样式 */
.comparison-table tr.correct-row {
  background-color: #d4edda;
}

.comparison-table tr.correct-row:hover {
  background-color: #c3e6cb;
}

/* 错误行样式 */
.comparison-table tr.incorrect-row {
  background-color: #f8d7da;
}

.comparison-table tr.incorrect-row:hover {
  background-color: #f5c6cb;
}

/* 状态单元格样式 */
.status-cell {
  text-align: center;
  font-weight: bold;
  font-size: 12px;
  width: 40px;
}

.status-cell.correct {
  color: #28a745;
}

.status-cell.incorrect {
  color: #dc3545;
}

/* 空状态 */
.no-annotation-data {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
  background: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 4px;
}

.timeout-status {
  color: #dc3545;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 统计摘要 */
.stats-summary {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  gap: 12px;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: #e9ecef;
  border-radius: 4px;
}

.stat-correct {
  color: #28a745;
}

.stat-incorrect {
  color: #dc3545;
}

.stat-total {
  color: #6c757d;
}

.stat-accuracy {
  color: #007bff;
  font-weight: 600;
}

/* 提示信息 */
.calculation-note {
  font-size: 11px;
  color: #6c757d;
  font-style: italic;
  margin-top: 8px;
  padding: 6px 8px;
  background: #f8f9fa;
  border-left: 3px solid #007bff;
  border-radius: 0 4px 4px 0;
}

/* 比对说明信息 */
.comparison-note {
  font-size: 11px;
  color: #6c757d;
  margin-top: 8px;
  padding: 6px 8px;
  background: #e7f3ff;
  border-left: 3px solid #007bff;
  border-radius: 0 4px 4px 0;
}

/* 表格单元格的改进样式 */
.comparison-table td[title] {
  cursor: help;
}

.comparison-table td[title]:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

/* 行列号的样式 */
.comparison-table td:first-child,
.comparison-table td:nth-child(2) {
  text-align: center;
  font-weight: 500;
  color: #666;
  width: 30px;
  min-width: 30px;
}

/* 状态单元格的改进样式 */
.status-cell {
  text-align: center;
  font-weight: bold;
  font-size: 10px;
  width: 60px;
  min-width: 60px;
}

/* 内容列的样式改进 */
.comparison-table td:nth-child(3),
.comparison-table td:nth-child(4) {
  max-width: 150px;
  word-break: break-word;
  white-space: pre-wrap;
}

/* TEDS详细分析样式 */
.teds-summary {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #f59e0b;
}

.teds-score {
  font-weight: 600;
  color: #f59e0b;
  font-size: 14px;
}

.teds-weights {
  font-size: 12px;
  color: #666;
}

.teds-structure-analysis,
.teds-content-analysis {
  margin-bottom: 15px;
}

.teds-structure-analysis h5,
.teds-content-analysis h5 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #374151;
  font-weight: 600;
}

.structure-comparison {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.structure-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #f9fafb;
  border-radius: 3px;
  font-size: 12px;
}

.structure-label {
  font-weight: 500;
  color: #6b7280;
  min-width: 60px;
}

.structure-value {
  color: #374151;
}

.structure-value.perfect {
  color: #10b981;
  font-weight: 500;
}

.structure-value.partial {
  color: #f59e0b;
}

.structure-diff {
  color: #ef4444;
  font-weight: 500;
}

.content-summary {
  display: flex;
  gap: 15px;
  margin-bottom: 10px;
  padding: 6px 8px;
  background: #f9fafb;
  border-radius: 3px;
  font-size: 12px;
}

.content-stat {
  color: #6b7280;
}

.teds-comparison-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 11px;
  margin-top: 8px;
}

.teds-comparison-table th,
.teds-comparison-table td {
  border: 1px solid #e5e7eb;
  padding: 6px 8px;
  text-align: left;
}

.teds-comparison-table th {
  background: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.teds-comparison-table tr.match-row {
  background: #f0f9ff;
}

.teds-comparison-table tr.diff-row {
  background: #fef2f2;
}

.similarity-cell {
  text-align: center;
  font-weight: 500;
}

.similarity-cell.perfect {
  color: #10b981;
}

.similarity-cell.good {
  color: #059669;
}

.similarity-cell.medium {
  color: #f59e0b;
}

.similarity-cell.poor {
  color: #ef4444;
}

.teds-note {
  font-size: 11px;
  color: #6b7280;
  margin-top: 6px;
  text-align: center;
  font-style: italic;
}

.teds-explanation {
  font-size: 11px;
  color: #6b7280;
  margin-top: 10px;
  padding: 6px 8px;
  background: #e7f3ff;
  border-left: 3px solid #f59e0b;
  border-radius: 0 4px 4px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .metric-item {
    padding: 6px 8px;
  }
  
  .metric-label,
  .metric-value {
    font-size: 12px;
  }
  
  .comparison-table {
    font-size: 10px;
  }
  
  .comparison-table th,
  .comparison-table td {
    padding: 3px 4px;
  }
} 