import React, { useMemo } from 'react';
import { calculateParserStats } from '../utils/dataProcessor';
import { getParserDisplayName } from '../utils/parserConfig';
import { useParserContext } from '../contexts/ParserContext';
import './DatasetReport.css';

/**
 * 数据集详细报告组件
 * 展示各路解析的成功率、表格解析率、平均准确率等统计指标
 */
const DatasetReport = ({ cases = [], dataset = '', annotationData = null }) => {
  const { enabledParsers } = useParserContext();

  const reportData = useMemo(() => {
    if (!cases.length) return null;

    // 使用统一的解析器统计计算函数，传递启用的解析器列表
    const report = calculateParserStats(cases, annotationData, enabledParsers);
    if (report) {
      report.dataset = dataset;
    }
    return report;
  }, [cases, dataset, annotationData, enabledParsers]);

  if (!reportData) {
    return (
      <div className="dataset-report">
        <div className="report-empty">暂无数据生成报告</div>
      </div>
    );
  }

  return (
    <div className="dataset-report">
      <div className="report-header">
        <h2>📊 数据集详细报告</h2>
        <div className="report-meta">
          <span className="dataset-name">数据集: {reportData.dataset}</span>
          <span className="total-cases">总案例数: {reportData.totalCases}</span>
          <span className="annotation-status">
            标注数据: {reportData.hasAnnotationData ? '✅ 有' : '❌ 无'}
          </span>
        </div>
      </div>

      <div className="report-summary">
        <h3>🔍 解析器性能总览</h3>
        <div className="summary-table-container">
          <table className="summary-table">
            <thead>
              <tr>
                <th>解析器</th>
                <th>解析成功率</th>
                <th>表格解析率</th>
                <th>平均准确率</th>
                <th>平均TEDS</th>
                <th>TEDS结构得分</th>
                <th>TEDS内容得分</th>
                <th>有效案例数</th>
              </tr>
            </thead>
            <tbody>
              {Object.values(reportData.parsers).map(parser => (
                <tr key={parser.name}>
                  <td className="parser-name">{parser.name}</td>
                  <td className="success-rate">
                    {parser.successRate}% 
                    <span className="detail">({parser.successCases}/{parser.totalCases})</span>
                  </td>
                  <td className="table-rate">
                    {parser.tableParsingRate}% 
                    <span className="detail">({parser.tableParsingCases}/{parser.totalCases})</span>
                  </td>
                  <td className="accuracy-rate">
                    {parser.avgAccuracy !== null ? `${parser.avgAccuracy}%` : '-'}
                    {parser.validAccuracyCases > 0 && (
                      <span className="detail">({parser.validAccuracyCases}个有效案例)</span>
                    )}
                  </td>
                  <td className="teds-rate">
                    {parser.avgTeds !== null ? `${parser.avgTeds}%` : '-'}
                    {parser.validTedsCount > 0 && (
                      <span className="detail">({parser.validTedsCount}个有效案例)</span>
                    )}
                  </td>
                  <td className="teds-structure-rate">
                    {parser.avgTedsStructure !== null ? `${parser.avgTedsStructure}%` : '-'}
                  </td>
                  <td className="teds-content-rate">
                    {parser.avgTedsContent !== null ? `${parser.avgTedsContent}%` : '-'}
                  </td>
                  <td className="valid-cases">
                    准确率: {parser.validAccuracyCases}<br/>
                    TEDS: {parser.validTedsCount}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="report-details">
        <h3>📋 详细计算过程</h3>
        {Object.values(reportData.parsers).map(parser => (
          <div key={parser.name} className="parser-detail-section">
            <h4>{parser.name} 详细分析</h4>
            <div className="calculation-summary">
              <div className="calc-item">
                <strong>解析成功率:</strong> {parser.successCases} ÷ {parser.totalCases} = {parser.successRate}%
              </div>
              <div className="calc-item">
                <strong>表格解析率:</strong> {parser.tableParsingCases} ÷ {parser.totalCases} = {parser.tableParsingRate}%
              </div>
              <div className="calc-item">
                <strong>平均准确率:</strong> 
                {parser.avgAccuracy !== null ? (
                  <span>{parser.totalAccuracy.toFixed(1)} ÷ {parser.validAccuracyCases} = {parser.avgAccuracy}%</span>
                ) : (
                  <span>无有效准确率数据</span>
                )}
              </div>
            </div>
            
            <div className="cases-breakdown">
              <h5>案例明细 (显示前10个案例)</h5>
              <div className="cases-table-container">
                <table className="cases-table">
                  <thead>
                    <tr>
                      <th>案例#</th>
                      <th>文件名</th>
                      <th>有解析结果</th>
                      <th>解析出表格</th>
                      <th>准确率</th>
                    </tr>
                  </thead>
                  <tbody>
                    {parser.details.slice(0, 10).map((detail, index) => (
                      <tr key={index}>
                        <td>{detail.caseIndex}</td>
                        <td className="file-name" title={detail.fileName}>
                          {detail.fileName.length > 20 ? 
                            detail.fileName.substring(0, 20) + '...' : 
                            detail.fileName
                          }
                        </td>
                        <td className={`status ${detail.hasResult ? 'success' : 'failed'}`}>
                          {detail.hasResult ? '✅' : '❌'}
                        </td>
                        <td className={`status ${detail.hasTable ? 'success' : 'failed'}`}>
                          {detail.hasTable ? '✅' : '❌'}
                        </td>
                        <td className="accuracy">
                          {detail.accuracy !== null && detail.accuracy !== undefined ? 
                            `${detail.accuracy.toFixed(1)}%` : 
                            '-'
                          }
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              {parser.details.length > 10 && (
                <div className="more-cases">
                  还有 {parser.details.length - 10} 个案例未显示...
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};



export default DatasetReport; 