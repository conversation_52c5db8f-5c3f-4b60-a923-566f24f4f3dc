import React, { useMemo } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { calculateMetricsForCase } from '../utils/dataProcessor';
import { getParserDisplayName } from '../utils/parserConfig';
import { useParserContext } from '../contexts/ParserContext';
import './CompactStatsChart.css';

const CompactStatsChart = ({ cases = [], dataset = '', annotationData = [] }) => {
  const { enabledParsers } = useParserContext();

  const chartData = useMemo(() => {
    if (!cases.length) return [];

    const total = cases.length;

    // 动态初始化启用的解析器统计
    const stats = {};
    enabledParsers.forEach(parserKey => {
      stats[parserKey] = {
        success: 0,
        accuracy: 0,
        teds: 0,
        validAccuracyCount: 0,
        validTedsCount: 0,
        tedsStructure: 0,
        tedsContent: 0,
        validTedsDetailCount: 0
      };
    });

    cases.forEach((caseData, index) => {
      // 修复：通过传入的annotationData查找匹配的annotation
      let caseAnnotation = null;
      if (annotationData && Array.isArray(annotationData)) {
        caseAnnotation = annotationData.find(ann => 
          ann.image_filename === caseData.fileName
        );
      }

      const metricsResult = calculateMetricsForCase(caseData, caseAnnotation);
      if (!metricsResult || !metricsResult.accuracies) return;

      const { accuracies, tedsScores, tedsDetails, hasTableResults } = metricsResult;

      // 通用统计更新函数 - 只更新启用的解析器
      const updateStats = (statKey, hasResult, accuracy, teds, tedsDetail) => {
        // 只处理启用的解析器
        if (!stats[statKey]) return;

        if (hasResult) {
          stats[statKey].success++;
          if (accuracy !== null && accuracy !== undefined) {
            stats[statKey].accuracy += accuracy;
            stats[statKey].validAccuracyCount++;
          }
          if (teds !== null && teds !== undefined) {
            stats[statKey].teds += teds;
            stats[statKey].validTedsCount++;
          }
          // 添加TEDS结构和内容得分统计
          if (tedsDetail && tedsDetail.structureAnalysis && tedsDetail.contentAnalysis) {
            stats[statKey].tedsStructure += tedsDetail.structureAnalysis.structureSimilarity || 0;
            stats[statKey].tedsContent += tedsDetail.contentAnalysis.contentSimilarity || 0;
            stats[statKey].validTedsDetailCount++;
          }
        }
      };

      // OCR Flux
      const hasOcrfluxResult = hasTableResults.ocrflux;
      updateStats('ocrflux', hasOcrfluxResult, accuracies.ocrflux, tedsScores.ocrflux, tedsDetails.ocrflux);

      // VL LLM
      const hasVlLLMResult = hasTableResults.vlLLM;
      updateStats('vlLLM', hasVlLLMResult, accuracies.vlLLM, tedsScores.vlLLM, tedsDetails.vlLLM);

      // MonkeyOCR (table)
      const isMonkeyOCRSuccess = hasTableResults.monkeyOCR;
      updateStats('monkeyOCR', isMonkeyOCRSuccess, accuracies.monkeyOCR, tedsScores.monkeyOCR, tedsDetails.monkeyOCR);

      // MonkeyOCR (parse)
      const isMonkeyOCRV2Success = hasTableResults.monkeyOCRV2;
      updateStats('monkeyOCRV2', isMonkeyOCRV2Success, accuracies.monkeyOCRV2, tedsScores.monkeyOCRV2, tedsDetails.monkeyOCRV2);

      // MonkeyOCR Local
      const hasMonkeyOCRLocalResult = hasTableResults.monkeyOCRLocal;
      updateStats('monkeyOCRLocal', hasMonkeyOCRLocalResult, accuracies.monkeyOCRLocal, tedsScores.monkeyOCRLocal, tedsDetails.monkeyOCRLocal);

      // MonkeyOCR（kas）
      const hasMonkeyOcrKasResult = hasTableResults.monkeyOcrKas;
      updateStats('monkeyOcrKas', hasMonkeyOcrKasResult, accuracies.monkeyOcrKas, tedsScores.monkeyOcrKas, tedsDetails.monkeyOcrKas);

      // KDC Markdown
      const hasKdcMarkdownResult = hasTableResults.kdcMarkdown;
      updateStats('kdcMarkdown', hasKdcMarkdownResult, accuracies.kdcMarkdown, tedsScores.kdcMarkdown, tedsDetails.kdcMarkdown);

      // KDC Plain
      const hasKdcPlainResult = hasTableResults.kdcPlain;
      updateStats('kdcPlain', hasKdcPlainResult, accuracies.kdcPlain, tedsScores.kdcPlain, tedsDetails.kdcPlain);

      // KDC KDC
      const hasKdcKdcResult = hasTableResults.kdcKdc;
      updateStats('kdcKdc', hasKdcKdcResult, accuracies.kdcKdc, tedsScores.kdcKdc, tedsDetails.kdcKdc);
    });

    // 转换为图表数据格式 - 只包含启用的解析器
    return enabledParsers.map(key => {
      const stat = stats[key];
      if (!stat) return null; // 防御性编程

      return {
        name: getParserDisplayName(key),
        successRate: total > 0 ? parseFloat(((stat.success / total) * 100).toFixed(1)) : 0,
        avgAccuracy: stat.validAccuracyCount > 0 ? parseFloat((stat.accuracy / stat.validAccuracyCount).toFixed(1)) : 0,
        avgTeds: stat.validTedsCount > 0 ? parseFloat((stat.teds / stat.validTedsCount).toFixed(1)) : 0,
        avgTedsStructure: stat.validTedsDetailCount > 0 ? parseFloat((stat.tedsStructure / stat.validTedsDetailCount).toFixed(1)) : 0,
        avgTedsContent: stat.validTedsDetailCount > 0 ? parseFloat((stat.tedsContent / stat.validTedsDetailCount).toFixed(1)) : 0
      };
    }).filter(Boolean); // 过滤掉null值
  }, [cases, annotationData, enabledParsers]);

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="custom-tooltip">
          <p className="label">{label}</p>
          <p className="success-rate">
            解析成功率: {data.successRate}%
          </p>
          <p className="avg-accuracy">
            平均准确率: {data.avgAccuracy > 0 ? `${data.avgAccuracy}%` : '无数据'}
          </p>
          <p className="avg-teds">
            平均TEDS: {data.avgTeds > 0 ? `${data.avgTeds}%` : '无数据'}
          </p>
        </div>
      );
    }
    return null;
  };

  if (!chartData.length) {
    return (
      <div className="compact-stats-chart">
        <div className="chart-empty">暂无数据显示统计图表</div>
      </div>
    );
  }

  return (
    <div className="compact-stats-chart">
      <div className="chart-header">
        <h3>解析器性能对比 - {dataset}</h3>
      </div>

      <div className="chart-container">
        <ResponsiveContainer width="100%" height={400}>
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" angle={-45} textAnchor="end" height={100} />
            <YAxis label={{ value: '百分比 (%)', angle: -90, position: 'insideLeft' }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {/* 成功率 - 蓝色 */}
            <Bar 
              dataKey="successRate" 
              fill="#6366f1" 
              name="解析成功率 (%)" 
              radius={[2, 2, 0, 0]}
            />
            
            {/* 准确率 - 绿色 */}
            <Bar 
              dataKey="avgAccuracy" 
              fill="#10b981" 
              name="平均准确率 (%)" 
              radius={[2, 2, 0, 0]}
            />
            
            {/* TEDS指标 - 橙色 */}
            <Bar
              dataKey="avgTeds"
              fill="#f59e0b"
              name="平均TEDS (%)"
              radius={[2, 2, 0, 0]}
            />

            {/* TEDS结构得分 - 紫色 */}
            <Bar
              dataKey="avgTedsStructure"
              fill="#8b5cf6"
              name="平均TEDS结构得分 (%)"
              radius={[2, 2, 0, 0]}
            />

            {/* TEDS内容得分 - 青色 */}
            <Bar
              dataKey="avgTedsContent"
              fill="#06b6d4"
              name="平均TEDS内容得分 (%)"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default CompactStatsChart;
