import React, { useState } from 'react';
import './CollapsibleText.css';

/**
 * 可收缩的文本组件
 * 用于显示大量文本内容，默认收缩，点击可展开
 */
const CollapsibleText = ({
  content,
  placeholder = "无数据",
  maxLines = 3,
  className = ""
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  // 确保content是字符串类型
  const textContent = typeof content === 'string' ? content :
                     content != null ? String(content) : '';

  if (!textContent || textContent.trim() === '') {
    return (
      <div className={`collapsible-text collapsible-text-empty ${className}`}>
        <div className="collapsible-text-placeholder">{placeholder}</div>
      </div>
    );
  }

  // 计算是否需要收缩（基于行数）
  const lines = textContent.split('\n');
  const needsCollapse = lines.length > maxLines;

  // 如果不需要收缩，直接显示全部内容
  if (!needsCollapse) {
    return (
      <div className={`collapsible-text ${className}`}>
        <pre className="collapsible-text-content">{textContent}</pre>
      </div>
    );
  }

  // 收缩时显示的内容
  const collapsedContent = lines.slice(0, maxLines).join('\n');
  const displayContent = isExpanded ? textContent : collapsedContent;

  return (
    <div className={`collapsible-text ${className}`}>
      <div className="collapsible-text-header">
        <button 
          className="collapsible-text-toggle"
          onClick={toggleExpanded}
          title={isExpanded ? "收缩文本" : "展开文本"}
        >
          <span className={`collapsible-text-arrow ${isExpanded ? 'expanded' : 'collapsed'}`}>
            ▶
          </span>
          <span className="collapsible-text-status">
            {isExpanded ? '已展开' : '已收缩'} ({lines.length} 行)
          </span>
        </button>
      </div>
      
      <div className={`collapsible-text-content-wrapper ${isExpanded ? 'expanded' : 'collapsed'}`}>
        <pre className="collapsible-text-content">{displayContent}</pre>
        {!isExpanded && (
          <div className="collapsible-text-fade">
            <div className="collapsible-text-more">
              <button 
                className="collapsible-text-more-btn"
                onClick={toggleExpanded}
              >
                ... 点击展开更多 ({lines.length - maxLines} 行)
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollapsibleText;
