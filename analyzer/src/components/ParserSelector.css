.parser-selector {
  margin-bottom: 16px;
}

.parser-selector-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.parser-selector-label {
  font-weight: 500;
  color: #333;
  min-width: 80px;
}

.parser-selector-summary {
  flex: 1;
}

.parser-selector-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.parser-selector-toggle:hover:not(:disabled) {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.parser-selector-toggle:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  color: #999;
}

.parser-selector-toggle.expanded {
  border-color: #40a9ff;
}

.toggle-text {
  color: #666;
}

.toggle-arrow {
  font-size: 12px;
  color: #999;
  transition: transform 0.2s ease;
}

.toggle-arrow.expanded {
  transform: rotate(180deg);
}

.parser-selector-panel {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
  padding: 16px;
  margin-top: 8px;
}

.parser-selector-controls {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
}

.parser-selector-btn {
  padding: 4px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.parser-selector-btn:hover:not(:disabled) {
  border-color: #40a9ff;
  color: #40a9ff;
}

.parser-selector-btn:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  color: #999;
}

.parser-selector-btn-all:hover:not(:disabled) {
  background: #e6f7ff;
}

.parser-selector-btn-none:hover:not(:disabled) {
  background: #fff2e8;
  border-color: #ff7a45;
  color: #ff7a45;
}

.parser-selector-categories {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.parser-category {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
}

.parser-category-header {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.parser-category-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.parser-category-label input[type="checkbox"] {
  margin: 0;
}

.parser-category-name {
  font-size: 14px;
}

.parser-category-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
}

.parser-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.parser-item:hover {
  background: #f5f5f5;
}

.parser-item input[type="checkbox"] {
  margin: 0;
}

.parser-item-name {
  font-size: 13px;
  color: #666;
}

.parser-item input[type="checkbox"]:checked + .parser-item-name {
  color: #333;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .parser-selector-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .parser-selector-label {
    min-width: auto;
  }
  
  .parser-selector-summary {
    width: 100%;
  }
  
  .parser-category-items {
    grid-template-columns: 1fr;
  }
}
