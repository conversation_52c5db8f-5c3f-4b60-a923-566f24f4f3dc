import React from 'react';
import CollapsibleText from './CollapsibleText';
import <PERSON><PERSON>enderer from './TableRenderer';
import JsonViewer from './JsonViewer';
import ParseReport from './ParseReport';
import MineruLayoutViewer from './MineruLayoutViewer';
import MineruVlmViewer from './MineruVlmViewer';
import MineruTextViewer from './MineruTextViewer';

/**
 * 解析器行组件
 * 根据解析器配置动态渲染解析结果
 */
const ParserRow = ({ 
  caseData, 
  annotationData, 
  parserConfig, 
  showOriginalText,
  rowIndex 
}) => {
  const { key, displayName, dataPath } = parserConfig;
  
  // 获取解析器数据
  const getParserData = () => {
    const data = caseData[dataPath];
    if (!data) return null;
    
    switch (key) {
      case 'kdcMarkdown':
        return {
          text: data.result?.data?.[0]?.markdown || data.data?.[0]?.markdown || '',
          renderData: data.result?.data?.[0]?.markdown || data.data?.[0]?.markdown,
          type: 'markdown'
        };
      case 'kdcPlain':
        return {
          text: data.result?.data?.[0]?.plain || data.data?.[0]?.plain || '',
          renderData: data.result?.data?.[0]?.plain || data.data?.[0]?.plain,
          type: 'plain'
        };
      case 'kdcKdc':
        const kdcDoc = data.result?.data?.[0]?.doc || data.data?.[0]?.doc;
        return {
          text: kdcDoc ? JSON.stringify(kdcDoc, null, 2) : '',
          renderData: kdcDoc ? { data: [{ doc: kdcDoc }] } : null,  // 包装成KdcCanvasRenderer期望的格式
          type: 'kdc'
        };
      case 'monkeyOCR':
        return {
          text: data.result?.html || data.html || '',
          renderData: data.result?.html || data.html,
          type: 'html',
          isTimeout: data.result?.is_timeout,
          processingTime: data.result?.processing_time
        };
      case 'monkeyOCRV2':
        return {
          text: data.result?.html || data.html || '',
          renderData: data.result?.html || data.html,
          type: 'html',
          isTimeout: data.result?.is_timeout,
          processingTime: data.result?.processing_time
        };
      case 'monkeyOCRLocal':
        return {
          text: data.results?.[0]?.result || data.result || '',
          renderData: data.results?.[0]?.result || data.result,
          type: data.result?.type || 'html'
        };
      case 'vlLLM':
        // VL-LLM数据结构复杂，需要提取实际的文本内容
        let vlLLMText = '';
        if (data.result) {
          // 尝试多种可能的路径提取文本
          const possiblePaths = [
            () => data.result.content?.choices?.[0]?.message?.content,
            () => data.result.choices?.[0]?.message?.content,
            () => data.result.content,
            () => data.result.text,
            () => data.result.markdown,
            () => typeof data.result === 'string' ? data.result : null
          ];

          for (const getPath of possiblePaths) {
            try {
              const text = getPath();
              if (text && typeof text === 'string' && text.trim()) {
                vlLLMText = text;
                break;
              }
            } catch (e) {
              // 忽略错误，继续尝试下一个路径
            }
          }
        }

        return {
          text: vlLLMText || '',
          renderData: vlLLMText || data.result,
          type: 'markdown'
        };
      case 'monkeyOcrKas':
        return {
          text: data.result?.content || data.content || '',
          renderData: data.result?.content || data.content,
          type: data.result?.type || data.type || 'markdown'
        };
      case 'ocrflux':
        return {
          text: data.result?.content || data.content || '',
          renderData: data.result?.content || data.content,
          type: 'html'
        };
      case 'mineru':
        return {
          text: 'model.json', // 占位符，实际内容由组件异步加载
          renderData: data?.result?.layout_data,
          type: 'mineru_layout',
          modelJsonPath: data?.result?.model_json
        };
      case 'mineru_vlm':
        return {
          text: 'model_output.txt', // 占位符，实际内容由组件异步加载
          renderData: data?.result?.layout_data,
          type: 'mineru_vlm',
          modelOutputPath: data?.result?.model_output
        };
      default:
        return null;
    }
  };

  const parserData = getParserData();
  if (!parserData) return null;

  const { text, renderData, type, isTimeout, processingTime } = parserData;

  return (
    <div className={`case-detail-row case-detail-row-${rowIndex}`}>
      {showOriginalText && (
        <div className="case-detail-cell case-detail-original-text">
          <div className="cell-header">{displayName} 原始文本</div>
          <div className="cell-content">
            {key === 'kdcKdc' ? (
              <JsonViewer
                data={renderData}
                placeholder={`无${displayName}数据`}
                collapsed={false}
              />
            ) : key === 'mineru' ? (
              <MineruTextViewer
                type="model.json"
                filePath={caseData?.mineru?.result?.model_json}
                placeholder={`无${displayName}数据`}
              />
            ) : key === 'mineru_vlm' ? (
              <MineruTextViewer
                type="model_output.txt"
                filePath={caseData?.mineru_vlm?.result?.model_output}
                placeholder={`无${displayName}数据`}
              />
            ) : (
              <CollapsibleText
                content={text}
                placeholder={`无${displayName}数据`}
                maxLines={3}
              />
            )}
          </div>
        </div>
      )}

      <div className="case-detail-cell case-detail-rendered-result">
        <div className="cell-header">{displayName} 渲染结果</div>
        <div className={`cell-content ${renderData ? 'has-table' : ''}`}>
          {type === 'mineru_layout' ? (
            <MineruLayoutViewer layoutData={renderData} caseData={caseData} />
          ) : type === 'mineru_vlm' ? (
            <MineruVlmViewer layoutData={renderData} caseData={caseData} />
          ) : (
            <TableRenderer
              content={renderData}
              type={type}
              placeholder={`无${displayName}渲染结果`}
            />
          )}
        </div>
      </div>

      <div className="case-detail-cell case-detail-parse-report">
        <div className="cell-header">解析报告</div>
        <div className="cell-content">
          <ParseReport
            caseData={caseData}
            annotationData={annotationData}
            parserName={displayName}
            actualText={text}
            isTimeout={isTimeout || false}
            processingTime={processingTime || null}
          />
        </div>
      </div>
    </div>
  );
};

export default ParserRow;
