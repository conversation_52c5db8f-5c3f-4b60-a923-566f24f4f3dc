import React from 'react';
import logger from '../utils/logger';
import { formatMetricValue } from '../utils/dataProcessor';
import './TedsMetrics.css';

/**
 * TEDS指标组件
 * 显示TEDS (Tree Edit Distance based Similarity) 评估结果
 */
const TedsMetrics = ({ parserName, tedsResult, hasAnnotationData }) => {
  logger.error(`[TedsMetrics] 🔥 ${parserName}: 渲染TEDS组件`, {
    hasTedsResult: !!tedsResult,
    hasAnnotationData,
    tedsResult: tedsResult,
    tedsResultType: typeof tedsResult,
    tedsResultKeys: tedsResult ? Object.keys(tedsResult) : 'null'
  });

  if (!hasAnnotationData) {
    return (
      <div className="teds-metrics no-data">
        <div className="metrics-header">
          <h5>📊 TEDS指标</h5>
        </div>
        <div className="metrics-content">
          <p className="no-data-message">无标注数据，无法计算TEDS</p>
        </div>
      </div>
    );
  }

  if (!tedsResult) {
    return (
      <div className="teds-metrics no-result">
        <div className="metrics-header">
          <h5>📊 TEDS指标</h5>
        </div>
        <div className="metrics-content">
          <p className="no-result-message">
            无法计算TEDS - 可能是表格结构解析失败
          </p>
        </div>
      </div>
    );
  }

  const {
    score,
    teds_score,
    structure_score,
    content_score,
    edit_distance,
    max_nodes,
    calculation_details,
    structureAnalysis,
    contentAnalysis,
    weights
  } = tedsResult;

  return (
    <div className="teds-metrics">
      <div className="metrics-header">
        <h5>📊 TEDS指标</h5>
      </div>
      
      <div className="metrics-summary">
        <div className="metrics-row">
          <div className="metric-item primary">
            <span className="metric-label">TEDS得分:</span>
            <span className="metric-value highlight">
              {formatMetricValue(score || teds_score)}
            </span>
          </div>
          {(structureAnalysis?.structureSimilarity !== undefined || structure_score !== undefined) && (
            <div className="metric-item">
              <span className="metric-label">结构得分:</span>
              <span className="metric-value">
                {formatMetricValue(structureAnalysis?.structureSimilarity || structure_score)}
              </span>
            </div>
          )}
          {(contentAnalysis?.contentSimilarity !== undefined || content_score !== undefined) && (
            <div className="metric-item">
              <span className="metric-label">内容得分:</span>
              <span className="metric-value">
                {formatMetricValue(contentAnalysis?.contentSimilarity || content_score)}
              </span>
            </div>
          )}
        </div>
      </div>
      
      {calculation_details && (
        <div className="calculation-details">
          <h6>计算详情</h6>
          <div className="detail-grid">
            {edit_distance !== undefined && (
              <div className="detail-item">
                <span className="detail-label">编辑距离:</span>
                <span className="detail-value">{edit_distance}</span>
              </div>
            )}
            
            {max_nodes !== undefined && (
              <div className="detail-item">
                <span className="detail-label">最大节点数:</span>
                <span className="detail-value">{max_nodes}</span>
              </div>
            )}
            
            {calculation_details.tree_comparison && (
              <div className="detail-item full-width">
                <span className="detail-label">树结构比较:</span>
                <div className="tree-comparison">
                  <div className="tree-info">
                    <span>预期节点: {calculation_details.expected_nodes || 0}</span>
                    <span>实际节点: {calculation_details.actual_nodes || 0}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      
      <div className="teds-explanation">
        <small>
          TEDS (Tree Edit Distance based Similarity) 是评估表格结构相似性的指标，
          考虑了表格的层次结构和内容匹配度。得分越高表示结构越相似。
        </small>
      </div>
    </div>
  );
};

export default TedsMetrics;
