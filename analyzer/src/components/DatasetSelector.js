import React, { useState, useEffect } from 'react';
import logger from '../utils/logger';
import { getDatasetList } from '../services/api';
import ParserSelector from './ParserSelector';
import { useParserContext } from '../contexts/ParserContext';
import './DatasetSelector.css';

const DatasetSelector = ({ selectedDataset, onDatasetChange, disabled = false }) => {
  const [datasets, setDatasets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 使用解析器上下文
  const { enabledParsers, updateEnabledParsers } = useParserContext();

  useEffect(() => {
    loadDatasets();
  }, []);

  const loadDatasets = async () => {
    try {
      setLoading(true);
      setError(null);
      const datasetList = await getDatasetList();
      setDatasets(datasetList);
      
      // 如果没有选中的数据集且有可用数据集，优先选择complex
      if (!selectedDataset && datasetList.length > 0) {
        const complexDataset = datasetList.find(dataset => dataset === 'kingsoft');
        onDatasetChange(complexDataset || datasetList[0]);
      }
    } catch (err) {
      logger.error('Failed to load datasets:', err);
      setError('加载数据集列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDatasetChange = (event) => {
    const newDataset = event.target.value;
    onDatasetChange(newDataset);
  };

  const handleRefresh = () => {
    loadDatasets();
  };

  if (loading) {
    return (
      <div className="dataset-selector">
        <div className="dataset-selector-header">
          <label className="dataset-selector-label">数据集:</label>
          <div className="dataset-selector-loading">加载中...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dataset-selector">
        <div className="dataset-selector-header">
          <label className="dataset-selector-label">数据集:</label>
          <div className="dataset-selector-error">
            {error}
            <button 
              className="dataset-selector-refresh-btn"
              onClick={handleRefresh}
              title="重新加载"
            >
              ↻
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="dataset-selector">
      <div className="dataset-selector-header">
        <label className="dataset-selector-label" htmlFor="dataset-select">
          数据集:
        </label>
        <div className="dataset-selector-controls">
          <select
            id="dataset-select"
            className="dataset-selector-select"
            value={selectedDataset || ''}
            onChange={handleDatasetChange}
            disabled={disabled}
          >
            <option value="" disabled>
              请选择数据集
            </option>
            {datasets.map((dataset) => (
              <option key={dataset} value={dataset}>
                {dataset}
              </option>
            ))}
          </select>
          <button 
            className="dataset-selector-refresh-btn"
            onClick={handleRefresh}
            disabled={disabled}
            title="刷新数据集列表"
          >
            ↻
          </button>
        </div>
      </div>
      
      {selectedDataset && (
        <div className="dataset-selector-info">
          <span className="dataset-selector-current">
            当前数据集: <strong>{selectedDataset}</strong>
          </span>
          <span className="dataset-selector-count">
            共 {datasets.length} 个数据集
          </span>
        </div>
      )}

      {/* 解析器选择器 */}
      <ParserSelector
        enabledParsers={enabledParsers}
        onParsersChange={updateEnabledParsers}
        disabled={disabled}
      />
    </div>
  );
};

export default DatasetSelector;
