.teds-metrics {
  margin: 10px 0;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.teds-metrics .metrics-header {
  margin-bottom: 10px;
}

.teds-metrics .metrics-header h5 {
  margin: 0;
  color: #333;
  font-size: 14px;
}

.teds-metrics .metrics-summary {
  padding: 5px 0;
}

.teds-metrics .metrics-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;
  flex-wrap: nowrap;
  width: 100%;
}

.teds-metrics .metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  flex-shrink: 0;
}

.teds-metrics .metric-label {
  color: #666;
  font-size: 13px;
  margin-right: 4px;
}

.teds-metrics .metric-value {
  font-weight: 500;
  color: #333;
  font-size: 13px;
  min-width: 50px;
  text-align: right;
}

.teds-metrics .metric-value.highlight {
  color: #2196f3;
  font-weight: 600;
}

.teds-metrics.no-data,
.teds-metrics.no-result {
  background-color: #f8f9fa;
}

.teds-metrics .no-data-message,
.teds-metrics .no-result-message {
  color: #666;
  font-size: 13px;
  margin: 0;
  padding: 10px 0;
} 