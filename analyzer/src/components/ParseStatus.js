import React from 'react';
import logger from '../utils/logger';

/**
 * 解析状态组件
 * 显示解析器的运行状态和基本信息
 */
const ParseStatus = ({ parserName, result, isSuccess }) => {
  logger.debug(`[ParseStatus] ${parserName}: 渲染状态组件`, {
    hasResult: !!result,
    isSuccess
  });

  const getStatusIcon = () => {
    if (isSuccess) {
      return '✅';
    } else {
      return '❌';
    }
  };

  const getStatusText = () => {
    if (isSuccess) {
      return '解析成功';
    } else {
      return '解析失败';
    }
  };

  const getStatusClass = () => {
    if (isSuccess) {
      return 'status-success';
    } else {
      return 'status-error';
    }
  };

  return (
    <div className={`parse-status ${getStatusClass()}`}>
      <div className="status-header">
        <span className="status-icon">{getStatusIcon()}</span>
        <span className="status-text">{getStatusText()}</span>
      </div>
      
      {isSuccess && result && (
        <div className="status-details">
          <div className="detail-item">
            <span className="detail-label">内容长度:</span>
            <span className="detail-value">
              {result && result.content ? result.content.length : 0} 字符
            </span>
          </div>
          
          {result.format && (
            <div className="detail-item">
              <span className="detail-label">格式:</span>
              <span className="detail-value">{result.format}</span>
            </div>
          )}
          
          {result.processing_time && (
            <div className="detail-item">
              <span className="detail-label">处理时间:</span>
              <span className="detail-value">{result.processing_time}ms</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ParseStatus;
