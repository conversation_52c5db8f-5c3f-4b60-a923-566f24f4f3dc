import React, { useMemo } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { calculateMetricsForCase } from '../utils/dataProcessor';
import { getAvailableParserConfigs, getParserDisplayName } from '../utils/parserConfig';
import { useParserContext } from '../contexts/ParserContext';
import './MetricsSummaryChart.css';

/**
 * 指标汇总图表组件
 * 显示当前案例的各解析器准确率和TEDS指标对比
 * 准确率基于与人工标注数据的单元格级别比对
 */
const MetricsSummaryChart = ({ caseData, index, fileName, annotationData }) => {
  const { enabledParsers } = useParserContext();

  const chartData = useMemo(() => {
    if (!caseData) return [];

    // 使用统一的指标计算函数，确保与AccuracyReport组件使用相同的计算逻辑
    const metricsResult = calculateMetricsForCase(caseData, annotationData);
    if (!metricsResult || !metricsResult.accuracies) return [];

    const { accuracies, tedsScores, tedsDetails } = metricsResult;

    // 使用统一的解析器配置，只显示启用的解析器
    const availableParsers = getAvailableParserConfigs(caseData, enabledParsers);

    // 转换为图表数据格式 - 包含准确率和TEDS指标
    const chartData = [];

    availableParsers.forEach(({ key }) => {
      const displayName = getParserDisplayName(key);
      const accuracy = accuracies[key] || 0;
      const teds = tedsScores[key] || 0;
      const tedsDetail = tedsDetails[key];
      const tedsStructure = tedsDetail?.structureAnalysis?.structureSimilarity || 0;
      const tedsContent = tedsDetail?.contentAnalysis?.contentSimilarity || 0;

      chartData.push({
        name: displayName,
        accuracy: accuracy,
        teds: teds,
        tedsStructure: tedsStructure,
        tedsContent: tedsContent
      });
    });

    return chartData;
  }, [caseData, annotationData, enabledParsers]);

  if (!chartData.length) {
    return (
      <div className="metrics-summary-chart">
        <div className="chart-header">
          <h4>📊 指标汇总</h4>
          <div className="chart-subtitle">案例 #{index} - {fileName}</div>
        </div>
        <div className="chart-empty">暂无指标数据</div>
      </div>
    );
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="custom-tooltip">
          <p className="label">{label}</p>
          <p className="accuracy">准确率: {(data.accuracy || 0).toFixed(1)}%</p>
          <p className="teds">TEDS: {(data.teds || 0).toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="metrics-summary-chart">
      <div className="chart-header">
        <h4>📊 指标汇总</h4>
        <div className="chart-subtitle">案例 #{index} - {fileName}</div>
      </div>
      
      <div className="chart-container">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 40 }}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" angle={-45} textAnchor="end" height={60} fontSize={11} />
            <YAxis domain={[0, 100]} fontSize={11} label={{ value: '百分比 (%)', angle: -90, position: 'insideLeft' }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            
            {/* 准确率 - 绿色 */}
            <Bar 
              dataKey="accuracy" 
              fill="#10b981" 
              name="准确率 (%)" 
              radius={[2, 2, 0, 0]}
            />
            
            {/* TEDS指标 - 橙色 */}
            <Bar
              dataKey="teds"
              fill="#f59e0b"
              name="TEDS指标 (%)"
              radius={[2, 2, 0, 0]}
            />

            {/* TEDS结构得分 - 紫色 */}
            <Bar
              dataKey="tedsStructure"
              fill="#8b5cf6"
              name="TEDS结构得分 (%)"
              radius={[2, 2, 0, 0]}
            />

            {/* TEDS内容得分 - 青色 */}
            <Bar
              dataKey="tedsContent"
              fill="#06b6d4"
              name="TEDS内容得分 (%)"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
        
        {/* 图例说明 */}
        <div className="chart-legend">
          <div className="legend-item">
            <div className="legend-color accuracy"></div>
            <span>准确率 - 基于与人工标注数据的单元格级别比对</span>
          </div>
          <div className="legend-item">
            <div className="legend-color teds"></div>
            <span>TEDS指标 - 评估表格结构相似性</span>
          </div>
          <div className="legend-item">
            <div className="legend-color teds-structure"></div>
            <span>TEDS结构得分 - 表格行列结构相似性</span>
          </div>
          <div className="legend-item">
            <div className="legend-color teds-content"></div>
            <span>TEDS内容得分 - 单元格内容相似性</span>
          </div>
          {annotationData && (
            <div className="legend-note">
              💡 准确率计算基于annotation数据库中的人工标注数据
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MetricsSummaryChart; 