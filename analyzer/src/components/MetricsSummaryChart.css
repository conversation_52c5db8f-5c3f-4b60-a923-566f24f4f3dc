.metrics-summary-chart {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.metrics-summary-chart .chart-header {
  padding: 12px 16px 8px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.metrics-summary-chart .chart-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
}

.metrics-summary-chart .chart-subtitle {
  margin: 4px 0 0;
  font-size: 11px;
  opacity: 0.9;
  font-weight: 400;
}

.metrics-summary-chart .chart-container {
  padding: 16px;
  background: #fafafa;
}

.metrics-summary-chart .chart-empty {
  padding: 30px 16px;
  text-align: center;
  color: #999;
  font-size: 14px;
}

/* 指标切换标签 */
.metrics-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 16px;
  background: #e9ecef;
  padding: 4px;
  border-radius: 6px;
}

.metrics-tab {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.metrics-tab:hover {
  background: #dee2e6;
  color: #495057;
}

.metrics-tab.active {
  background: #007bff;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* 图例样式 */
.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.accuracy {
  background-color: #10b981;
}

.legend-color.teds {
  background-color: #f59e0b;
}

.legend-color.teds-structure {
  background-color: #8b5cf6;
}

.legend-color.teds-content {
  background-color: #06b6d4;
}

.legend-color.success {
  background-color: #10b981;
}

.legend-color.failed {
  background-color: #ef4444;
}

.legend-note {
  font-size: 11px;
  color: #888;
  font-style: italic;
  margin-top: 4px;
  padding: 4px 6px;
  background: #f8f9fa;
  border-radius: 3px;
  border-left: 2px solid #007bff;
  flex-basis: 100%;
  text-align: center;
}

/* 自定义Recharts样式 */
.metrics-summary-chart .recharts-cartesian-grid-horizontal line,
.metrics-summary-chart .recharts-cartesian-grid-vertical line {
  stroke: #e8e8e8;
  stroke-width: 1;
}

.metrics-summary-chart .recharts-tooltip-wrapper {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metrics-summary-chart .recharts-default-tooltip {
  background: white !important;
  border: 1px solid #e8e8e8 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.metrics-summary-chart .recharts-tooltip-label {
  color: #333 !important;
  font-weight: 600 !important;
  margin-bottom: 4px !important;
}

.metrics-summary-chart .recharts-tooltip-item {
  color: #666 !important;
}

/* 柱状图动态颜色 */
.metrics-summary-chart .recharts-bar-rectangle {
  transition: opacity 0.2s ease;
}

.metrics-summary-chart .recharts-bar-rectangle:hover {
  opacity: 0.8;
}

/* 指标对比视图 */
.metrics-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-top: 16px;
}

.comparison-section {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
}

.comparison-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  text-align: center;
}

.comparison-chart {
  height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .metrics-summary-chart .chart-header {
    padding: 10px 12px 6px;
  }
  
  .metrics-summary-chart .chart-header h4 {
    font-size: 14px;
  }
  
  .metrics-summary-chart .chart-subtitle {
    font-size: 10px;
  }
  
  .metrics-summary-chart .chart-container {
    padding: 12px;
  }
  
  .chart-legend {
    gap: 12px;
    flex-wrap: wrap;
  }
  
  .legend-item {
    font-size: 11px;
  }
  
  .metrics-tabs {
    margin-bottom: 12px;
  }
  
  .metrics-tab {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .metrics-comparison {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* 动画效果 */
.metrics-summary-chart {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 