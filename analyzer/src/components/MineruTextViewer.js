import React, { useState, useEffect } from 'react';
import CollapsibleText from './CollapsibleText';
import logger from '../utils/logger';

/**
 * Mineru原始文本查看器 - 用于显示model.json和model_output.txt内容
 */
const MineruTextViewer = ({ type, filePath, placeholder = "无数据" }) => {
  const [content, setContent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取文件内容
  const fetchFileContent = async () => {
    if (!filePath) {
      logger.warn(`[MineruTextViewer] 没有${type}文件路径`);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 从路径中提取相对路径
      const relativePath = filePath.replace('/data/projects/kingsoft/personal/workspace/tablerag/enhance/', '');
      
      const response = await fetch(`http://localhost:8000/static/${relativePath}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      let fileContent;
      if (type === 'model.json') {
        const jsonContent = await response.json();
        fileContent = JSON.stringify(jsonContent, null, 2);
      } else if (type === 'model_output.txt') {
        fileContent = await response.text();
      } else {
        fileContent = await response.text();
      }
      
      setContent(fileContent);
      logger.debug(`[MineruTextViewer] 成功获取${type}内容`);
    } catch (err) {
      logger.error(`[MineruTextViewer] 获取${type}失败:`, err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFileContent();
  }, [filePath, type]);

  if (loading) {
    return <div className="loading-message">正在加载{type}...</div>;
  }

  if (error) {
    return <div className="error-message">加载失败: {error}</div>;
  }

  if (!content) {
    return <div className="placeholder-message">{placeholder}</div>;
  }

  return (
    <CollapsibleText
      content={content}
      placeholder={placeholder}
      maxLines={3}
    />
  );
};

export default MineruTextViewer;
