.dataset-report {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.report-empty {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

/* 报告头部 */
.report-header {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.report-header h2 {
  margin: 0 0 15px 0;
  font-size: 24px;
  font-weight: 600;
}

.report-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  opacity: 0.9;
}

.report-meta span {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 6px;
  backdrop-filter: blur(5px);
}

/* 性能总览 */
.report-summary {
  margin-bottom: 40px;
}

.report-summary h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.summary-table-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  font-size: 14px;
}

.summary-table th {
  background: #f8f9fa;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
  white-space: nowrap;
}

.summary-table td {
  padding: 12px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: top;
}

.summary-table tr:hover {
  background: #f8f9fa;
}

.parser-name {
  font-weight: 600;
  color: #495057;
  min-width: 140px;
}

.success-rate,
.table-rate,
.accuracy-rate {
  font-weight: 500;
}

.detail {
  font-size: 12px;
  color: #6c757d;
  margin-left: 8px;
}

.valid-cases {
  text-align: center;
  font-weight: 500;
  color: #28a745;
}

/* 详细分析 */
.report-details h3 {
  margin-bottom: 25px;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.parser-detail-section {
  margin-bottom: 35px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.parser-detail-section h4 {
  margin: 0;
  padding: 15px 20px;
  background: #f8f9fa;
  color: #495057;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid #e9ecef;
}

.calculation-summary {
  padding: 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.calc-item {
  margin-bottom: 10px;
  padding: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.calc-item strong {
  display: inline-block;
  width: 100px;
  color: #495057;
}

/* 案例明细 */
.cases-breakdown {
  padding: 20px;
  background: #fafbfc;
}

.cases-breakdown h5 {
  margin: 0 0 15px 0;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
}

.cases-table-container {
  overflow-x: auto;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.cases-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
  background: white;
}

.cases-table th {
  background: #f1f3f4;
  padding: 10px 8px;
  text-align: left;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  font-size: 12px;
}

.cases-table td {
  padding: 8px;
  border-bottom: 1px solid #f1f3f4;
  vertical-align: middle;
}

.cases-table tr:hover {
  background: #f8f9fa;
}

.file-name {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-family: monospace;
  font-size: 12px;
  color: #6c757d;
}

.status {
  text-align: center;
  font-size: 16px;
  width: 40px;
}

.status.success {
  color: #28a745;
}

.status.failed {
  color: #dc3545;
}

.accuracy {
  text-align: center;
  font-weight: 500;
  min-width: 60px;
}

.more-cases {
  margin-top: 15px;
  text-align: center;
  color: #6c757d;
  font-size: 13px;
  font-style: italic;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dataset-report {
    padding: 15px;
  }

  .report-header {
    padding: 15px;
  }

  .report-header h2 {
    font-size: 20px;
  }

  .report-meta {
    flex-direction: column;
    gap: 10px;
  }

  .summary-table,
  .cases-table {
    font-size: 12px;
  }

  .summary-table th,
  .summary-table td {
    padding: 8px 6px;
  }

  .cases-table th,
  .cases-table td {
    padding: 6px 4px;
  }

  .calculation-summary {
    padding: 15px;
  }

  .cases-breakdown {
    padding: 15px;
  }

  .calc-item strong {
    width: 80px;
    font-size: 13px;
  }
}

/* 打印样式 */
@media print {
  .dataset-report {
    padding: 0;
    max-width: none;
  }

  .report-header {
    background: #f8f9fa !important;
    color: #333 !important;
    box-shadow: none;
  }

  .parser-detail-section {
    break-inside: avoid;
    margin-bottom: 20px;
  }

  .cases-table,
  .summary-table {
    break-inside: avoid;
  }
} 