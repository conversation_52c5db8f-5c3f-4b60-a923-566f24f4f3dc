.case-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  height: calc(100vh - 200px);
  min-height: 600px;
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 将滚动条移到整个CaseList组件 */
}

.case-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  border-radius: 8px 8px 0 0;
}

.case-list-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.case-list-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.case-list-count {
  font-size: 14px;
  color: #666;
  white-space: nowrap;
}

.case-list-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.case-list-auto-refresh-btn {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s ease;
  min-width: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.case-list-auto-refresh-btn:hover {
  background-color: #f0f8ff;
  border-color: #007bff;
  color: #007bff;
}

.case-list-auto-refresh-btn.active {
  background-color: #007bff;
  border-color: #007bff;
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.case-list-refresh-btn {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s ease;
  min-width: 36px;
}

.case-list-refresh-btn:hover:not(:disabled) {
  background-color: #f8f9fa;
  border-color: #007bff;
  color: #007bff;
}

.case-list-refresh-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.case-list-report-btn {
  padding: 6px 12px;
  border: 1px solid #28a745;
  border-radius: 4px;
  background-color: #28a745;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.case-list-report-btn:hover {
  background-color: #218838;
  border-color: #218838;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.case-list-report-btn.active {
  background-color: #dc3545;
  border-color: #dc3545;
}

.case-list-report-btn.active:hover {
  background-color: #c82333;
  border-color: #c82333;
}

.case-list-html-report-btn {
  padding: 6px 12px;
  border: 1px solid #6f42c1;
  border-radius: 4px;
  background-color: #6f42c1;
  color: white;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.case-list-html-report-btn:hover {
  background-color: #5a2d91;
  border-color: #5a2d91;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.case-list-report {
  flex: 1;
  overflow-y: auto;
  background-color: white;
}

.case-list-loading {
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-bottom: 16px;
  font-size: 14px;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background-color: #e9ecef;
  border-radius: 2px;
  margin: 0 auto;
  overflow: hidden;
}

.loading-progress-bar {
  height: 100%;
  background-color: #007bff;
  transition: width 0.3s ease;
}

.case-list-error {
  padding: 20px;
  text-align: center;
}

.error-message {
  color: #dc3545;
  margin-bottom: 12px;
}

.error-retry-btn {
  padding: 8px 16px;
  border: 1px solid #dc3545;
  border-radius: 4px;
  background-color: white;
  color: #dc3545;
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-retry-btn:hover {
  background-color: #dc3545;
  color: white;
}

.case-list-empty {
  padding: 40px 20px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.case-list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 移除overflow: hidden，让内容可以正常显示 */
}

.case-list-stats {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  background-color: #f8f9fa;
  flex-shrink: 0;
  /* 移除max-height和overflow-y，让图表完整展示 */
}

.case-list-content {
  flex: 1;
  /* 移除overflow-y: auto，滚动由父级.case-list处理 */
  padding: 8px;
  min-height: 0; /* 确保flex子元素可以收缩 */
}

/* 调整统计面板在案例列表中的样式 */
.case-list .stats-panel {
  box-shadow: none;
  background: transparent;
  padding: 0;
  margin: 0;
}

.case-list .stats-panel-header {
  padding: 0 0 12px 0;
  margin-bottom: 12px;
}

.case-list .stats-grid {
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.case-list .stat-item {
  padding: 8px;
}

.case-list .stat-label {
  font-size: 11px;
}

.case-list .stat-value {
  font-size: 14px;
}

/* Case item styles */
.case-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: white;
  font-size: 14px;
}

.case-item:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.case-item.selected {
  background-color: #e3f2fd;
  border-color: #007bff;
}

.case-item-index {
  font-weight: 600;
  color: #007bff;
  min-width: 40px;
  text-align: center;
}

.case-item-info {
  flex: 1;
  margin-left: 12px;
}

.case-item-filename {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  word-break: break-all;
}

.case-item-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #666;
  flex-wrap: wrap;
}

.case-item-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-width: 60px;
}

.case-item-status span {
  font-size: 10px;
  text-align: center;
  white-space: nowrap;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.has-image {
  background-color: #28a745;
}

.status-indicator.no-image {
  background-color: #dc3545;
}

.status-indicator.has-vl-llm {
  background-color: #17a2b8;
}

.status-indicator.no-vl-llm {
  background-color: #ffc107;
}

/* 解析状态指示器 */
.parse-status {
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 6px;
  font-size: 14px;
  border: 1px solid transparent;
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.parse-status.in_progress {
  background-color: #e7f3ff;
  border-color: #b3d9ff;
  color: #0066cc;
}

.parse-status.completed {
  background-color: #e8f5e8;
  border-color: #c3e6c3;
  color: #2d5a2d;
}

.parse-status.unknown {
  background-color: #fff3cd;
  border-color: #ffeeba;
  color: #856404;
}

.parse-status-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.status-icon {
  font-size: 16px;
  line-height: 1;
}

.status-text {
  font-weight: 500;
  flex: 1;
  min-width: 200px;
}

.auto-refresh-indicator {
  font-size: 12px;
  color: #666;
  opacity: 0.8;
  font-style: italic;
  animation: fadeInOut 3s infinite;
}

@keyframes fadeInOut {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .case-list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .case-list-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .case-list-buttons {
    justify-content: center;
  }
  
  .case-item {
    flex-direction: column;
    align-items: stretch;
    text-align: left;
  }
  
  .case-item-index {
    text-align: left;
    margin-bottom: 8px;
  }
  
  .case-item-info {
    margin-left: 0;
  }
  
  .case-item-meta {
    flex-wrap: wrap;
  }
  
  .parse-status-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .status-text {
    min-width: auto;
  }
  
  .auto-refresh-indicator {
    align-self: stretch;
    text-align: center;
  }
}

/* 过滤器样式 */
.case-list-filter {
  margin: 12px 0;
  padding: 12px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
}

.filter-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-input {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  background: white;
}

.filter-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-select {
  flex: 1;
  padding: 6px 8px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-clear-btn {
  padding: 6px 8px;
  border: 1px solid #dc3545;
  background: #dc3545;
  color: white;
  border-radius: 4px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-clear-btn:hover:not(:disabled) {
  background: #c82333;
  border-color: #c82333;
}

.filter-clear-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.filter-help {
  font-size: 11px;
  color: #6c757d;
  font-style: italic;
}
