import React from 'react';
import logger from '../utils/logger';
import { formatMetricValue } from '../utils/dataProcessor';
import './AccuracyMetrics.css';

/**
 * 准确率指标组件
 * 显示解析结果与标注数据的比对结果
 */
const AccuracyMetrics = ({ parserName, comparisonResult, hasAnnotationData }) => {
  logger.debug(`[AccuracyMetrics] ${parserName}: 渲染准确率组件`, {
    hasComparisonResult: !!comparisonResult,
    hasAnnotationData
  });

  if (!hasAnnotationData) {
    return (
      <div className="accuracy-metrics no-data">
        <div className="metrics-header">
          <h5>🔍 准确率指标</h5>
        </div>
        <div className="metrics-content">
          <p className="no-data-message">无标注数据，无法计算准确率</p>
        </div>
      </div>
    );
  }

  if (!comparisonResult) {
    return (
      <div className="accuracy-metrics no-result">
        <div className="metrics-header">
          <h5>🔍 准确率指标</h5>
        </div>
        <div className="metrics-content">
          <p className="no-result-message">
            无法生成比对结果 - 可能是表格解析失败或数据格式不兼容
          </p>
        </div>
      </div>
    );
  }

  const { totalCells, correctCells, accuracy, cells = [] } = comparisonResult;

  return (
    <div className="accuracy-metrics">
      <div className="metrics-header">
        <h5>🔍 准确率指标</h5>
      </div>
      
      <div className="metrics-summary">
        <div className="metrics-row">
          <div className="metric-item">
            <span className="metric-label">总单元格数:</span>
            <span className="metric-value">{totalCells}</span>
          </div>
          <div className="metric-item">
            <span className="metric-label">正确单元格:</span>
            <span className="metric-value">{correctCells}</span>
          </div>
          <div className="metric-item primary">
            <span className="metric-label">准确率:</span>
            <span className="metric-value highlight">{formatMetricValue(accuracy)}</span>
          </div>
        </div>
      </div>

      {cells.length > 0 && (
        <div className="cell-comparison">
          <h6>单元格比对详情</h6>
          <div className="comparison-table-container">
            <table className="comparison-table">
              <thead>
                <tr>
                  <th>行</th>
                  <th>列</th>
                  <th>预期内容</th>
                  <th>实际内容</th>
                  <th>结果</th>
                </tr>
              </thead>
              <tbody>
                {cells.map((cell, index) => (
                  <tr
                    key={`cell-${index}`}
                    className={(cell.isCorrect || cell.is_correct) ? 'correct-row' : 'incorrect-row'}
                  >
                    <td>{cell.row}</td>
                    <td>{cell.col}</td>
                    <td className="cell-content expected">
                      {(cell.expectedContent || cell.expected_content) || <em>(空)</em>}
                    </td>
                    <td className="cell-content actual">
                      {(cell.actualContent || cell.actual_content) || <em>(空)</em>}
                    </td>
                    <td className={`status-cell ${(cell.isCorrect || cell.is_correct) ? 'correct' : 'incorrect'}`}>
                      {(cell.isCorrect || cell.is_correct) ? '✓' : '✗'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccuracyMetrics;
