import React from 'react';
import './CaseList.css'; // 导入正确的CSS文件

const CaseItem = ({ caseData, onClick, selected = false }) => {
  const {
    index,
    fileName,
    baseName,
    imagePath,
    vlLLMResult,
    kdcMarkdown,
    kdcPlain,
    kdcKdc,
    monkeyOCR,
    monkeyOCRV2,
    monkeyOCRLocal,
    monkeyOcrKas,
    ocrflux,
    mineru,
    mineru_vlm
  } = caseData;

  const handleClick = () => {
    if (onClick) {
      onClick(caseData);
    }
  };

  // 计算处理状态 - 适配新的数据结构
  const hasImage = !!imagePath;

  // 检查各解析器的状态
  const checkParseStatus = (data, type) => {
    if (!data) return 'empty';
    
    switch (type) {
      case 'mineru':
      case 'mineru_vlm':
        // Mineru类型：检查success字段和layout_data（使用对象格式，不是数组）
        if (data && data.success === true && data.result?.layout_data?.length > 0) {
          return 'success';
        }
        return 'error';
      case 'kdc':
        // KDC类型：检查新格式和旧格式
        if (data.result && data.result.data) {
          const resultData = data.result.data || [];
          return (Array.isArray(resultData) && resultData.length > 0 && resultData[0].markdown) ? 'success' : 'error';
        }
        // 兼容旧格式
        const dataArray = data.data || [];
        return (Array.isArray(dataArray) && dataArray.length > 0 && dataArray[0].markdown) ? 'success' : 'error';
      
      case 'plain':
        // KDC Plain类型：检查新格式和旧格式
        if (data.result && data.result.data) {
          const resultData = data.result.data || [];
          return (Array.isArray(resultData) && resultData.length > 0 && resultData[0].plain) ? 'success' : 'error';
        }
        // 兼容旧格式
        const plainArray = data.data || [];
        return (Array.isArray(plainArray) && plainArray.length > 0 && plainArray[0].plain) ? 'success' : 'error';
      
      case 'kdckdc':
        // KDC KDC类型：检查新格式和旧格式
        if (data.result && data.result.data) {
          const resultData = data.result.data || [];
          return (Array.isArray(resultData) && resultData.length > 0 && resultData[0].doc) ? 'success' : 'error';
        }
        // 兼容旧格式
        const kdcArray = data.data || [];
        return (Array.isArray(kdcArray) && kdcArray.length > 0 && kdcArray[0].doc) ? 'success' : 'error';
      
      case 'monkey':
        // MonkeyOCR：检查success字段和旧格式
        if (data.result) {
          // 检查新的success字段
          if (data.result.success === false) {
            return 'error';
          }
          // 检查超时
          if (data.result.is_timeout) {
            return 'error';
          }
          // 检查是否有实际内容
          if (data.result.html && data.result.html.trim() && !data.result.html.includes('MonkeyOCR文件上传失败')) {
            return 'success';
          }
          return 'error';
        }
        // 兼容旧格式
        if (data.html && data.html.trim() && !data.html.includes('MonkeyOCR文件上传失败')) {
          return 'success';
        }
        return 'error';
      
      case 'monkey_local':
        // MonkeyOCR Local：检查新格式和旧格式
        if (data.results && Array.isArray(data.results) && data.results.length > 0) {
          const firstResult = data.results[0];
          if (firstResult.result) {
            // 检查success字段
            if (firstResult.result.success === false) {
              return 'error';
            }
            // 检查是否有实际内容
            if ((firstResult.result.html && firstResult.result.html.trim()) || 
                (firstResult.result.markdown && firstResult.result.markdown.trim())) {
              return 'success';
            }
          }
          return 'error';
        }
        // 兼容旧格式
        if (data.result) {
          // 检查success字段
          if (data.result.success === false) {
            return 'error';
          }
          if ((data.result.html && data.result.html.trim()) || 
              (data.result.markdown && data.result.markdown.trim())) {
            return 'success';
          }
        }
        return 'error';
      
      case 'vl_llm':
        // VL LLM类型 - 处理两种不同的数据结构
        if (data.result) {
          let choices = [];

          // 结构1: result.content.choices (tables数据集)
          if (data.result.content && data.result.content.choices) {
            choices = data.result.content.choices;
          }
          // 结构2: result.choices (kingsoft数据集)
          else if (data.result.choices) {
            choices = data.result.choices;
          }

          if (choices.length > 0) {
            const content = choices[0].message?.content;
            return (content && content.trim()) ? 'success' : 'error';
          }
        }
        return 'error';

      case 'monkey_ocr_kas':
        // MonkeyOCR（kas）类型：检查result.content字段
        if (data.result && data.result.content) {
          return (data.result.content.trim()) ? 'success' : 'error';
        }
        // 兼容其他可能的格式
        if (data.content) {
          return (data.content.trim()) ? 'success' : 'error';
        }
        if (data.markdown) {
          return (data.markdown.trim()) ? 'success' : 'error';
        }
        if (data.text) {
          return (data.text.trim()) ? 'success' : 'error';
        }
        return 'error';

      case 'ocrflux':
        // OCR Flux类型：检查result.content字段
        if (data.result && data.result.content) {
          return (data.result.content.trim()) ? 'success' : 'error';
        }
        if (data.content) {
          return (data.content.trim()) ? 'success' : 'error';
        }
        return 'error';
      
      default:
        return 'empty';
    }
  };

  return (
    <div 
      className={`case-item ${selected ? 'selected' : ''}`}
      onClick={handleClick}
    >
      <div className="case-item-index">
        #{index}
      </div>
      
      <div className="case-item-info">
        <div className="case-item-filename" title={fileName}>
          {fileName}
        </div>
        
        <div className="case-item-meta">
          <div className="case-item-status">
            <div className={`status-indicator ${hasImage ? 'has-image' : 'no-image'}`}></div>
            <span>图片</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(ocrflux, 'ocrflux') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>OCR Flux</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${
              checkParseStatus(vlLLMResult, 'vl_llm') === 'success' ? 'has-vl-llm' :
              checkParseStatus(vlLLMResult, 'vl_llm') === 'empty' ? 'no-image' : 'no-vl-llm'
            }`}></div>
            <span>VL-LLM</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(monkeyOCR, 'monkey') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>Monkey(table)</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(monkeyOCRV2, 'monkey') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>Monkey(parse)</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(monkeyOCRLocal, 'monkey_local') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>Monkey(local)</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(monkeyOcrKas, 'monkey_ocr_kas') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>MonkeyOCR（kas）</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(kdcMarkdown, 'kdc') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>KDC MD</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(kdcPlain, 'plain') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>KDC Plain</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(kdcKdc, 'kdckdc') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>KDC KDC</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(mineru, 'mineru') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>Mineru Layout</span>
          </div>

          <div className="case-item-status">
            <div className={`status-indicator ${checkParseStatus(mineru_vlm, 'mineru_vlm') === 'success' ? 'has-image' : 'no-image'}`}></div>
            <span>Mineru（VLM）</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseItem;