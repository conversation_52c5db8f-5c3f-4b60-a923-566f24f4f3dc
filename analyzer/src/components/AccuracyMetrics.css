.accuracy-metrics {
  margin: 10px 0;
  padding: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.accuracy-metrics .metrics-header {
  margin-bottom: 10px;
}

.accuracy-metrics .metrics-header h5 {
  margin: 0;
  color: #333;
  font-size: 14px;
}

.accuracy-metrics .metrics-summary {
  padding: 5px 0;
  margin-bottom: 15px;
}

.accuracy-metrics .metrics-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 24px;
  flex-wrap: nowrap;
  width: 100%;
}

.accuracy-metrics .metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  flex-shrink: 0;
}

.accuracy-metrics .metric-label {
  color: #666;
  font-size: 13px;
  margin-right: 4px;
}

.accuracy-metrics .metric-value {
  font-weight: 500;
  color: #333;
  font-size: 13px;
  min-width: 50px;
  text-align: right;
}

.accuracy-metrics .metric-value.highlight {
  color: #2196f3;
  font-weight: 600;
}

.accuracy-metrics.no-data,
.accuracy-metrics.no-result {
  background-color: #f8f9fa;
}

.accuracy-metrics .no-data-message,
.accuracy-metrics .no-result-message {
  color: #666;
  font-size: 13px;
  margin: 0;
  padding: 10px 0;
}

/* 单元格比对详情样式 */
.accuracy-metrics .cell-comparison {
  margin-top: 15px;
  border-top: 1px solid #eee;
  padding-top: 15px;
}

.accuracy-metrics .cell-comparison h6 {
  margin: 0 0 10px 0;
  font-size: 13px;
  color: #666;
}

.accuracy-metrics .comparison-table-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.accuracy-metrics .comparison-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.accuracy-metrics .comparison-table th,
.accuracy-metrics .comparison-table td {
  padding: 6px 8px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.accuracy-metrics .comparison-table th {
  background-color: #f8f9fa;
  font-weight: 500;
  color: #666;
}

.accuracy-metrics .comparison-table .correct-row {
  background-color: #f1f8f1;
}

.accuracy-metrics .comparison-table .incorrect-row {
  background-color: #fff4f4;
}

.accuracy-metrics .comparison-table .cell-content {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.accuracy-metrics .comparison-table .status-cell {
  text-align: center;
  font-weight: bold;
}

.accuracy-metrics .comparison-table .status-cell.correct {
  color: #4caf50;
}

.accuracy-metrics .comparison-table .status-cell.incorrect {
  color: #f44336;
}

.accuracy-metrics .comparison-table em {
  color: #999;
  font-style: italic;
} 