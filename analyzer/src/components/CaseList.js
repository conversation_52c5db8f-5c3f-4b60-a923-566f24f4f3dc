import React, { useState, useEffect, useCallback, useRef } from 'react';
import logger from '../utils/logger';
import { getParseResults, getImageList, getDatasetAnnotations } from '../services/api';
import { processParseResults } from '../utils/dataProcessor';
import { generateHTMLReport as generateReportHTML } from '../utils/htmlReportGenerator';
import { useParserContext } from '../contexts/ParserContext';
import CaseItem from './CaseItem';
import CompactStatsChart from './CompactStatsChart';
import DatasetReport from './DatasetReport';
import './CaseList.css';

const CaseList = ({ 
  selectedDataset, 
  onCaseSelect, 
  selectedCase,
  onCasesUpdate
}) => {
  const [cases, setCases] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [loadingProgress, setLoadingProgress] = useState({ current: 0, total: 0 });
  const [parseStatus, setParseStatus] = useState(null);
  const [annotationData, setAnnotationData] = useState([]);
  const [autoRefreshEnabled, setAutoRefreshEnabled] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);
  const intervalRef = useRef(null);

  // 使用解析器上下文
  const { enabledParsers } = useParserContext();

  // 过滤器状态
  const [textboxThreshold, setTextboxThreshold] = useState('');
  const [tableDetectionFilter, setTableDetectionFilter] = useState(''); // 'all', 'detected', 'not_detected'
  const [filteredCases, setFilteredCases] = useState([]);

  // 详细报告状态
  const [showDetailedReport, setShowDetailedReport] = useState(false);

  const loadCases = useCallback(async (showLoadingIndicator = true) => {
    if (!selectedDataset) {
      setCases([]);
      if (onCasesUpdate) onCasesUpdate([]);
      return;
    }

    try {
      if (showLoadingIndicator) {
        setLoading(true);
        setError(null);
        setLoadingProgress({ current: 0, total: 3 });
      }

      // 步骤1: 加载解析结果
      if (showLoadingIndicator) setLoadingProgress({ current: 1, total: 3 });
      const parseResults = await getParseResults(selectedDataset);
      
      // 检查解析状态
      const status = parseResults?.metadata?.status || 'unknown';
      const progress = parseResults?.progress || {};
      setParseStatus({
        status,
        current: progress.completed_count || 0,
        total: parseResults?.metadata?.total_files || 0,
        currentFile: progress.current_file || '',
        currentParser: progress.current_parser || ''
      });

      // 步骤2: 加载图片列表
      if (showLoadingIndicator) setLoadingProgress({ current: 2, total: 4 });
      const imageList = await getImageList(selectedDataset);

      // 步骤3: 加载annotation数据
      if (showLoadingIndicator) setLoadingProgress({ current: 3, total: 4 });
      let loadedAnnotationData = [];
      try {
        loadedAnnotationData = await getDatasetAnnotations(selectedDataset);
        logger.debug('加载annotation数据:', loadedAnnotationData.length, '条');
      } catch (error) {
        logger.warn('加载annotation数据失败:', error);
      }
      setAnnotationData(loadedAnnotationData);

      // 使用新的数据处理方式 - 直接基于图片列表
      logger.debug('图片列表:', imageList);
      logger.debug('解析结果:', parseResults);
      logger.debug('解析状态:', status, `(${progress.completed_count || 0}/${parseResults?.metadata?.total_files || 0})`);

      // 使用processParseResults处理数据，传入图片列表
      const processedCases = processParseResults(parseResults, imageList);

      // 为每个案例添加图片路径信息
      const allCases = processedCases.map((caseData, index) => {
        const encodedFileName = encodeURIComponent(caseData.fileName);
        return {
          ...caseData,
          imagePath: `http://localhost:8000/static/dataset/${selectedDataset}/images/${encodedFileName}`
        };
      });

      logger.debug('生成的案例列表:', allCases);
      setCases(allCases);

      // 应用过滤器
      applyFilter(allCases, textboxThreshold, tableDetectionFilter);

      if (onCasesUpdate) onCasesUpdate(allCases);
      setLastUpdateTime(new Date().toLocaleTimeString());

      // 智能自动刷新逻辑
      if (status === 'in_progress') {
        if (!autoRefreshEnabled) {
          setAutoRefreshEnabled(true);
          logger.debug('🔄 检测到解析任务进行中，启动自动刷新');
        }
      } else if (status === 'completed') {
        if (autoRefreshEnabled) {
          setAutoRefreshEnabled(false);
          logger.debug('✅ 解析任务已完成，停止自动刷新');
        }
      }

      // 如果有案例且没有选中的案例，选择第一个
      if (allCases.length > 0 && onCaseSelect && !selectedCase) {
        onCaseSelect(allCases[0]);
      }
      
    } catch (err) {
      logger.error('Failed to load cases:', err);
      setError(`加载数据失败: ${err.message}`);
      setCases([]);
      if (onCasesUpdate) onCasesUpdate([]);
      setAutoRefreshEnabled(false);
    } finally {
      if (showLoadingIndicator) {
        setLoading(false);
        setLoadingProgress({ current: 0, total: 0 });
      }
    }
  }, [selectedDataset, onCaseSelect, selectedCase, onCasesUpdate, autoRefreshEnabled, textboxThreshold]);

  // 过滤函数
  const applyFilter = useCallback((casesToFilter, threshold, tableFilter) => {
    let filtered = casesToFilter;

    // 应用文本框数量阈值过滤
    if (threshold && threshold !== '') {
      const thresholdNum = parseInt(threshold, 10);
      if (!isNaN(thresholdNum)) {
        filtered = filtered.filter(caseItem => {
          // 检查是否有文本框数量分布特征
          const features = caseItem.features?.kdc;
          if (!features || !features.textbox_count_distribution) {
            return false;
          }

          const distribution = features.textbox_count_distribution;
          const maxTextboxes = distribution.max_textboxes_per_cell || 0;

          return maxTextboxes > thresholdNum;
        });
      }
    }

    // 应用表格检测过滤
    if (tableFilter && tableFilter !== '' && tableFilter !== 'all') {
      filtered = filtered.filter(caseItem => {
        const features = caseItem.features?.kdc;
        if (!features || !features.table_detection) {
          return tableFilter === 'not_detected';
        }

        const hasTable = features.table_detection.has_table;
        if (tableFilter === 'detected') {
          return hasTable;
        } else if (tableFilter === 'not_detected') {
          return !hasTable;
        }
        return true;
      });
    }

    setFilteredCases(filtered);
  }, []);

  // 处理过滤器变化
  const handleThresholdChange = (e) => {
    const value = e.target.value;
    setTextboxThreshold(value);
    applyFilter(cases, value, tableDetectionFilter);
  };

  const handleTableDetectionChange = (e) => {
    const value = e.target.value;
    setTableDetectionFilter(value);
    applyFilter(cases, textboxThreshold, value);
  };

  // 清除过滤器
  const clearFilter = () => {
    setTextboxThreshold('');
    setTableDetectionFilter('');
    setFilteredCases(cases);
  };

  // 自动刷新效果
  useEffect(() => {
    if (autoRefreshEnabled && selectedDataset) {
      // 每10秒自动刷新
      intervalRef.current = setInterval(() => {
        logger.debug('🔄 自动刷新解析结果...');
        loadCases(false); // 后台刷新，不显示加载指示器
      }, 10000);

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      };
    }
  }, [autoRefreshEnabled, selectedDataset, loadCases]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    loadCases();
  }, [selectedDataset]);

  const handleCaseClick = (caseItem) => {
    if (onCaseSelect) {
      onCaseSelect(caseItem);
    }
  };

  const handleRefresh = () => {
    loadCases();
  };

  const toggleAutoRefresh = () => {
    setAutoRefreshEnabled(!autoRefreshEnabled);
  };

  // 生成HTML报告
  const generateHTMLReport = useCallback(async () => {
    const currentCases = (textboxThreshold || tableDetectionFilter) ? filteredCases : cases;
    
    if (currentCases.length === 0) {
      alert('暂无数据可生成报告');
      return;
    }

    try {
      // 使用已经加载的annotationData
      logger.debug('HTML报告生成 - 使用已加载的annotation数据:', annotationData.length, '条');

      // 生成HTML报告，传递启用的解析器列表
      const htmlContent = generateReportHTML(currentCases, selectedDataset, annotationData, enabledParsers);
      
      // 创建Blob并在新标签页中打开
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      
      // 在新标签页中打开
      window.open(url, '_blank');
      
      // 延迟释放URL
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);
    } catch (error) {
      logger.error('生成HTML报告失败:', error);
      alert(`生成HTML报告失败: ${error.message}`);
    }
  }, [cases, filteredCases, selectedDataset, textboxThreshold, tableDetectionFilter, annotationData, enabledParsers]);

  if (!selectedDataset) {
    return (
      <div className="case-list">
        <div className="case-list-header">
          <h3>测试案例</h3>
        </div>
        <div className="case-list-empty">
          请先选择一个数据集
        </div>
      </div>
    );
  }

  return (
    <div className="case-list">
      <div className="case-list-header">
        <h3>测试案例</h3>
        <div className="case-list-controls">
          <span className="case-list-count">
            {cases.length > 0 ? (
              (textboxThreshold || tableDetectionFilter) ?
                `筛选出 ${filteredCases.length} / ${cases.length} 个案例` :
                `共 ${cases.length} 个案例`
            ) : ''}
          </span>
          <div className="case-list-buttons">
            {/* 详细报告按钮 */}
            {cases.length > 0 && (
              <button 
                className={`case-list-report-btn ${showDetailedReport ? 'active' : ''}`}
                onClick={() => setShowDetailedReport(!showDetailedReport)}
                title={showDetailedReport ? '隐藏详细报告' : '显示详细报告'}
              >
                📊 {showDetailedReport ? '隐藏报告' : '详细报告'}
              </button>
            )}
            {/* 生成HTML报告按钮 */}
            {cases.length > 0 && (
              <button 
                className="case-list-html-report-btn"
                onClick={generateHTMLReport}
                title="生成独立HTML报告文件"
              >
                📄 HTML报告
              </button>
            )}
            {/* 自动刷新开关 */}
            {parseStatus?.status === 'in_progress' && (
              <button 
                className={`case-list-auto-refresh-btn ${autoRefreshEnabled ? 'active' : ''}`}
                onClick={toggleAutoRefresh}
                title={autoRefreshEnabled ? '关闭自动刷新' : '开启自动刷新'}
              >
                {autoRefreshEnabled ? '🔄' : '⏸️'}
              </button>
            )}
            <button 
              className="case-list-refresh-btn"
              onClick={handleRefresh}
              disabled={loading}
              title="手动刷新"
            >
              ↻
            </button>
          </div>
        </div>
      </div>

      {/* 解析进度状态 */}
      {parseStatus && (
        <div className={`parse-status ${parseStatus.status}`}>
          <div className="parse-status-content">
            {parseStatus.status === 'in_progress' ? (
              <>
                <span className="status-icon">🔄</span>
                <span className="status-text">
                  解析进行中: {parseStatus.current}/{parseStatus.total} 
                  {parseStatus.currentFile && ` - 当前文件: ${parseStatus.currentFile}`}
                </span>
                {autoRefreshEnabled && (
                  <span className="auto-refresh-indicator">
                    (自动刷新中 {lastUpdateTime && `- 最后更新: ${lastUpdateTime}`})
                  </span>
                )}
              </>
            ) : parseStatus.status === 'completed' ? (
              <>
                <span className="status-icon">✅</span>
                <span className="status-text">解析已完成: {parseStatus.total}/{parseStatus.total}</span>
              </>
            ) : (
              <>
                <span className="status-icon">❓</span>
                <span className="status-text">状态未知</span>
              </>
            )}
          </div>
        </div>
      )}

      {/* 过滤器 */}
      {!loading && !error && cases.length > 0 && (
        <div className="case-list-filter">
          <div className="filter-group">
            <label htmlFor="textbox-threshold">文本框数量阈值过滤：</label>
            <div className="filter-input-group">
              <input
                id="textbox-threshold"
                type="number"
                min="0"
                placeholder="输入阈值"
                value={textboxThreshold}
                onChange={handleThresholdChange}
                className="filter-input"
              />
              <button
                className="filter-clear-btn"
                onClick={clearFilter}
                disabled={!textboxThreshold && !tableDetectionFilter}
                title="清除过滤器"
              >
                ✕
              </button>
            </div>
            <div className="filter-help">
              筛选出单元格最大文本框数量超过阈值的案例
            </div>
          </div>

          <div className="filter-group">
            <label htmlFor="table-detection-filter">表格检测过滤：</label>
            <div className="filter-input-group">
              <select
                id="table-detection-filter"
                value={tableDetectionFilter}
                onChange={handleTableDetectionChange}
                className="filter-select"
              >
                <option value="">全部案例</option>
                <option value="detected">检测到表格</option>
                <option value="not_detected">未检测到表格</option>
              </select>
            </div>
            <div className="filter-help">
              根据KDC是否检测到表格来筛选案例
            </div>
          </div>
        </div>
      )}

      {loading && (
        <div className="case-list-loading">
          <div className="loading-spinner"></div>
          <div className="loading-text">
            正在加载案例数据... ({loadingProgress.current}/{loadingProgress.total})
          </div>
          <div className="loading-progress">
            <div 
              className="loading-progress-bar"
              style={{ 
                width: `${(loadingProgress.current / loadingProgress.total) * 100}%` 
              }}
            ></div>
          </div>
        </div>
      )}

      {error && (
        <div className="case-list-error">
          <div className="error-message">{error}</div>
          <button className="error-retry-btn" onClick={handleRefresh}>
            重试
          </button>
        </div>
      )}

      {!loading && !error && cases.length === 0 && (
        <div className="case-list-empty">
          {parseStatus?.status === 'in_progress' ? 
            '解析正在进行中，暂无可显示的案例' : 
            '当前数据集没有找到测试案例'
          }
        </div>
      )}

      {!loading && !error && cases.length > 0 && (
        <div className="case-list-container">
          {/* 详细报告 */}
          {showDetailedReport && (
            <div className="case-list-report">
              <DatasetReport 
                cases={(textboxThreshold || tableDetectionFilter) ? filteredCases : cases}
                dataset={selectedDataset}
                annotationData={annotationData}
              />
            </div>
          )}

          {/* 统计图表 */}
          {!showDetailedReport && (
            <div className="case-list-stats">
              <CompactStatsChart cases={(textboxThreshold || tableDetectionFilter) ? filteredCases : cases} dataset={selectedDataset} annotationData={annotationData} />
            </div>
          )}

          {/* 案例列表 */}
          {!showDetailedReport && (
            <div className="case-list-content">
              {((textboxThreshold || tableDetectionFilter) ? filteredCases : cases).map((caseItem) => (
                <CaseItem
                  key={caseItem.id}
                  caseData={caseItem}
                  selected={selectedCase && selectedCase.id === caseItem.id}
                  onClick={() => handleCaseClick(caseItem)}
                />
              ))}
            </div>
          )}

          {/* 过滤结果为空的提示 */}
          {!showDetailedReport && (textboxThreshold || tableDetectionFilter) && filteredCases.length === 0 && (
            <div className="case-list-empty">
              {textboxThreshold && tableDetectionFilter ?
                `没有找到同时满足文本框数量超过 ${textboxThreshold} 且表格检测条件的案例` :
                textboxThreshold ?
                  `没有找到单元格最大文本框数量超过 ${textboxThreshold} 的案例` :
                  `没有找到符合表格检测条件的案例`
              }
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CaseList;
