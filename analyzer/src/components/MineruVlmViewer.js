import React, { useState, useEffect } from 'react';
import MineruVlmCanvasRenderer from './MineruVlmCanvasRenderer';
import logger from '../utils/logger';

/**
 * Mineru VLM结果渲染组件 - 只显示canvas渲染结果
 */
const MineruVlmViewer = ({ layoutData, caseData }) => {
  const [modelOutputContent, setModelOutputContent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取model_output.txt文件内容
  const fetchModelOutputContent = async () => {
    if (!caseData?.mineru_vlm?.result?.model_output) {
      logger.warn('[MineruVlmViewer] 没有model_output路径');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 从路径中提取相对路径
      const modelOutputPath = caseData.mineru_vlm.result.model_output;
      const relativePath = modelOutputPath.replace('/data/projects/kingsoft/personal/workspace/tablerag/enhance/', '');

      const response = await fetch(`http://localhost:8000/static/${relativePath}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const textContent = await response.text();
      setModelOutputContent(textContent);
      logger.debug('[MineruVlmViewer] 成功获取model_output.txt内容');
    } catch (err) {
      logger.error('[MineruVlmViewer] 获取model_output.txt失败:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModelOutputContent();
  }, [caseData]);

  // 详细调试数据
  logger.debug('[MineruVlmViewer] 渲染数据详情:', {
    hasLayoutData: !!layoutData,
    isArray: Array.isArray(layoutData),
    length: layoutData?.length || 0,
    hasCaseData: !!caseData,
    hasModelOutput: !!caseData?.mineru_vlm?.result?.model_output
  });

  if (!layoutData) {
    logger.debug('[MineruVlmViewer] 无数据');
    return <div className="empty-message">无数据</div>;
  }

  if (loading) {
    return <div className="loading-message">正在加载Mineru VLM数据...</div>;
  }

  if (error) {
    return <div className="error-message">加载失败: {error}</div>;
  }

  // 只显示canvas渲染结果
  return (
    <MineruVlmCanvasRenderer
      modelOutputText={modelOutputContent}
      placeholder="无Mineru VLM渲染结果"
    />
  );
};

export default MineruVlmViewer;
