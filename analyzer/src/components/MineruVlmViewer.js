import React, { useState, useEffect } from 'react';
import MineruVlmCanvasRenderer from './MineruVlmCanvasRenderer';
import logger from '../utils/logger';

/**
 * Mineru VLM结果渲染组件
 */
const MineruVlmViewer = ({ layoutData, caseData }) => {
  const [modelOutputContent, setModelOutputContent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取model_output.txt文件内容
  const fetchModelOutputContent = async () => {
    if (!caseData?.mineru_vlm?.result?.model_output) {
      logger.warn('[MineruVlmViewer] 没有model_output路径');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // 从路径中提取相对路径
      const modelOutputPath = caseData.mineru_vlm.result.model_output;
      const relativePath = modelOutputPath.replace('/data/projects/kingsoft/personal/workspace/tablerag/enhance/', '');

      const response = await fetch(`http://localhost:8000/static/${relativePath}`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const textContent = await response.text();
      setModelOutputContent(textContent);
      logger.debug('[MineruVlmViewer] 成功获取model_output.txt内容');
    } catch (err) {
      logger.error('[MineruVlmViewer] 获取model_output.txt失败:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchModelOutputContent();
  }, [caseData]);

  // 详细调试数据
  logger.debug('[MineruVlmViewer] 渲染数据详情:', {
    hasLayoutData: !!layoutData,
    isArray: Array.isArray(layoutData),
    length: layoutData?.length || 0,
    hasCaseData: !!caseData,
    hasModelOutput: !!caseData?.mineru_vlm?.result?.model_output
  });

  if (!layoutData) {
    logger.debug('[MineruVlmViewer] 无数据');
    return <div className="empty-message">无数据</div>;
  }

  return (
    <div className="mineru-vlm-viewer">
      <div className="mineru-vlm-sections">
        {/* 原始文本部分 */}
        <div className="mineru-vlm-section" style={{ marginBottom: '20px' }}>
          <div className="mineru-vlm-header" style={{
            padding: '10px',
            borderBottom: '1px solid #e0e0e0',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px 4px 0 0'
          }}>
            <h4 style={{ margin: 0, fontSize: '16px', color: '#333' }}>
              Mineru（VLM） 原始文本 (model_output.txt)
            </h4>
          </div>
          <div className="mineru-vlm-content" style={{
            padding: '15px',
            border: '1px solid #e0e0e0',
            borderTop: 'none',
            borderRadius: '0 0 4px 4px',
            backgroundColor: '#fff'
          }}>
            {loading && <div className="loading-message">正在加载model_output.txt...</div>}
            {error && <div className="error-message">加载失败: {error}</div>}
            {modelOutputContent && (
              <pre style={{
                margin: 0,
                fontFamily: 'monospace',
                fontSize: '13px',
                lineHeight: '1.5',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-all',
                color: '#333',
                backgroundColor: '#f8f9fa',
                padding: '10px',
                borderRadius: '4px',
                maxHeight: '300px',
                overflow: 'auto'
              }}>
                {modelOutputContent}
              </pre>
            )}
            {!loading && !error && !modelOutputContent && (
              <div className="placeholder-message">无model_output.txt数据</div>
            )}
          </div>
        </div>

        {/* Canvas渲染部分 */}
        <div className="mineru-vlm-section">
          <div className="mineru-vlm-header" style={{
            padding: '10px',
            borderBottom: '1px solid #e0e0e0',
            backgroundColor: '#f5f5f5',
            borderRadius: '4px 4px 0 0'
          }}>
            <h4 style={{ margin: 0, fontSize: '16px', color: '#333' }}>
              Mineru（VLM） 渲染结果
            </h4>
          </div>
          <div className="mineru-vlm-content" style={{
            padding: '15px',
            border: '1px solid #e0e0e0',
            borderTop: 'none',
            borderRadius: '0 0 4px 4px',
            backgroundColor: '#fff'
          }}>
            <MineruVlmCanvasRenderer
              modelOutputText={modelOutputContent}
              placeholder="无Mineru VLM渲染结果"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MineruVlmViewer;
