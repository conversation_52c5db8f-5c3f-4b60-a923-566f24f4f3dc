import React from 'react';
import logger from '../utils/logger';

/**
 * Mineru VLM结果渲染组件
 */
const MineruVlmViewer = ({ layoutData }) => {
  // 详细调试数据
  logger.debug('[MineruVlmViewer] 渲染数据详情:', {
    hasLayoutData: !!layoutData,
    isArray: Array.isArray(layoutData),
    length: layoutData?.length || 0,
    firstItem: layoutData?.[0]
  });

  if (!layoutData) {
    logger.debug('[MineruVlmViewer] 无数据');
    return <div className="empty-message">无数据</div>;
  }

  // 确保是数组
  const items = Array.isArray(layoutData) ? layoutData : [layoutData];
  if (items.length === 0) {
    logger.debug('[MineruVlmViewer] 数组为空');
    return <div className="empty-message">无布局数据</div>;
  }

  return (
    <div className="mineru-vlm-viewer" style={{ padding: '10px' }}>
      {items.map((item, index) => {
        const itemType = item?.type || 'unknown';
        const itemText = item?.text || item?.content || '';
        const itemBbox = item?.bbox || [];

        logger.debug(`[MineruVlmViewer] 渲染项目 ${index}:`, {
          type: itemType,
          text: itemText?.substring(0, 50),
          bbox: itemBbox
        });

        const style = {
          margin: '10px 0',
          whiteSpace: 'pre-wrap',
          position: 'relative',
          padding: '8px',
          border: '1px solid #eee',
          borderRadius: '4px'
        };

        if (itemType === 'title') {
          style.fontWeight = 'bold';
          style.fontSize = '1.5em';
          style.borderColor = '#ccc';
        } else if (itemType === 'table') {
          style.fontFamily = 'monospace';
          style.backgroundColor = '#f8f9fa';
        }

        return (
          <div key={index} className={`vlm-${itemType}`} style={style}>
            <div className="item-type" style={{ fontSize: '0.8em', color: '#666', marginBottom: '4px' }}>
              {itemType.toUpperCase()}
            </div>
            <div className="item-content">
              {itemText}
            </div>
            {itemBbox.length === 4 && (
              <div className="item-bbox" style={{ fontSize: '0.8em', color: '#666', marginTop: '4px' }}>
                位置: [{itemBbox.join(', ')}]
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default MineruVlmViewer;
