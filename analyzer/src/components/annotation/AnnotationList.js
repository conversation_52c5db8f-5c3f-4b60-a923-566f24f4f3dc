import React, { useState, useEffect } from 'react';
import './AnnotationList.css';

const AnnotationList = ({ annotations, onEdit, onDelete, loading }) => {
  // 默认展开所有标注
  const [expandedAnnotations, setExpandedAnnotations] = useState(() => {
    if (annotations && annotations.length > 0) {
      return new Set(annotations.map(ann => ann.id));
    }
    return new Set();
  });

  // 当annotations变化时，确保新的标注也默认展开
  useEffect(() => {
    if (annotations && annotations.length > 0) {
      setExpandedAnnotations(new Set(annotations.map(ann => ann.id)));
    }
  }, [annotations]);

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleString('zh-CN');
    } catch {
      return dateString;
    }
  };

  const getStatusText = (status) => {
    const statusMap = {
      'draft': '草稿',
      'completed': '完成',
      'reviewed': '已审核'
    };
    return statusMap[status] || status;
  };

  const getStatusClass = (status) => {
    return `status-${status}`;
  };

  const truncateContent = (content, maxLength = 100) => {
    if (!content) return '';
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const toggleExpansion = (annotationId) => {
    const newExpanded = new Set(expandedAnnotations);
    if (newExpanded.has(annotationId)) {
      newExpanded.delete(annotationId);
    } else {
      newExpanded.add(annotationId);
    }
    setExpandedAnnotations(newExpanded);
  };

  const formatJsonContent = (content) => {
    try {
      if (typeof content === 'string') {
        const parsed = JSON.parse(content);
        return JSON.stringify(parsed, null, 2);
      }
      return JSON.stringify(content, null, 2);
    } catch {
      return content;
    }
  };

  const renderContentPreview = (annotation) => {
    const isExpanded = expandedAnnotations.has(annotation.id);
    const content = annotation.table_content;
    
    if (!content) {
      return <div className="no-content">无内容</div>;
    }

    if (isExpanded) {
      return (
        <div className="annotation-content-expanded">
          <div className="content-header">
            <span className="content-type">JSON Schema 数据</span>
            <button 
              className="toggle-btn"
              onClick={() => toggleExpansion(annotation.id)}
            >
              收起
            </button>
          </div>
          <pre className="json-content">{formatJsonContent(content)}</pre>
        </div>
      );
    } else {
      return (
        <div className="annotation-content-collapsed">
          <div className="content-preview">
            <pre className="json-preview">{truncateContent(content, 120)}</pre>
          </div>
          <button 
            className="toggle-btn"
            onClick={() => toggleExpansion(annotation.id)}
          >
            展开查看完整内容
          </button>
        </div>
      );
    }
  };

  if (loading) {
    return (
      <div className="annotation-list">
        <div className="annotation-list-loading">
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (annotations.length === 0) {
    return (
      <div className="annotation-list">
        <div className="annotation-list-empty">
          <p>暂无标注数据</p>
          <p>点击"新建标注"开始标注</p>
        </div>
      </div>
    );
  }

  return (
    <div className="annotation-list">
      <div className="annotation-list-header">
        <h5>标注列表 ({annotations.length})</h5>
      </div>
      
      <div className="annotation-list-content">
        {annotations.map((annotation) => (
          <div key={annotation.id} className="annotation-item">
            <div className="annotation-item-header">
              <div className="annotation-item-info">
                <span className="annotation-id">#{annotation.id}</span>
                <span className="annotation-annotator">{annotation.annotator}</span>
                <span className={`annotation-status ${getStatusClass(annotation.status)}`}>
                  {getStatusText(annotation.status)}
                </span>
              </div>
              <div className="annotation-item-actions">
                <button
                  className="btn btn-small btn-primary"
                  onClick={() => onEdit(annotation)}
                >
                  编辑
                </button>
                <button
                  className="btn btn-small btn-danger"
                  onClick={() => onDelete(annotation.id)}
                >
                  删除
                </button>
              </div>
            </div>
            
            <div className="annotation-item-content">
              <div className="annotation-structure">
                {(() => {
                  try {
                    const structure = typeof annotation.table_structure === 'string' 
                      ? JSON.parse(annotation.table_structure)
                      : annotation.table_structure;
                    return (
                      <span className="structure-info">
                        表格: {structure.rows || 0}行 × {structure.cols || 0}列
                        {structure.totalElements && (
                          <span className="elements-info">
                            {' '}| 元素: {structure.totalElements}个
                            {structure.titles && structure.titles.length > 0 && (
                              <span> (含{structure.titles.length}个标题)</span>
                            )}
                          </span>
                        )}
                      </span>
                    );
                  } catch {
                    return <span className="structure-info">结构信息解析失败</span>;
                  }
                })()}
              </div>
              
              <div className="annotation-content-preview">
                {renderContentPreview(annotation)}
              </div>
            </div>
            
            <div className="annotation-item-footer">
              <span className="annotation-date">
                创建: {formatDate(annotation.created_at)}
              </span>
              {annotation.updated_at !== annotation.created_at && (
                <span className="annotation-date">
                  更新: {formatDate(annotation.updated_at)}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AnnotationList;
