/* 标注列表样式 */
.annotation-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.annotation-list-header {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.annotation-list-header h5 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.annotation-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.annotation-list-loading,
.annotation-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #6c757d;
  font-style: italic;
}

.annotation-list-empty p {
  margin: 4px 0;
}

/* 标注项样式 */
.annotation-item {
  margin-bottom: 12px;
  padding: 16px;
  background: #fff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  transition: box-shadow 0.2s;
}

.annotation-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.annotation-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.annotation-item-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.annotation-id {
  font-size: 12px;
  font-weight: 600;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
}

.annotation-annotator {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
}

.annotation-status {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-draft {
  background: #fff3cd;
  color: #856404;
}

.status-completed {
  background: #d4edda;
  color: #155724;
}

.status-reviewed {
  background: #d1ecf1;
  color: #0c5460;
}

.annotation-item-actions {
  display: flex;
  gap: 8px;
}

.annotation-item-content {
  margin-bottom: 12px;
}

.annotation-structure {
  margin-bottom: 8px;
}

/* 结构信息增强 */
.structure-info {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.elements-info {
  color: #007bff;
  font-size: 13px;
}

/* 内容预览区域 */
.annotation-content-preview {
  margin-top: 12px;
}

/* 收缩状态的内容 */
.annotation-content-collapsed {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
}

.content-preview {
  margin-bottom: 8px;
}

.json-preview {
  margin: 0;
  padding: 8px;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #495057;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow: hidden;
  max-height: 60px;
}

/* 展开状态的内容 */
.annotation-content-expanded {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.content-type {
  font-size: 13px;
  font-weight: 600;
  color: #28a745;
  background-color: #d4edda;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #c3e6cb;
}

.json-content {
  margin: 0;
  padding: 12px;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: #495057;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
  overflow-x: auto;
}

/* 切换按钮 */
.toggle-btn {
  background-color: #007bff;
  border: none;
  color: white;
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.toggle-btn:hover {
  background-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

.toggle-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 123, 255, 0.2);
}

/* 无内容状态 */
.no-content {
  padding: 20px;
  text-align: center;
  color: #6c757d;
  font-style: italic;
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 4px;
}

/* 滚动条样式 */
.json-content::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.json-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.json-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.json-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .json-content,
  .json-preview {
    font-size: 11px;
    padding: 8px;
  }
  
  .json-content {
    max-height: 300px;
  }
  
  .toggle-btn {
    font-size: 11px;
    padding: 3px 10px;
  }
}

.annotation-item-footer {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6c757d;
  border-top: 1px solid #f1f3f4;
  padding-top: 8px;
}

.annotation-date {
  display: flex;
  align-items: center;
}
