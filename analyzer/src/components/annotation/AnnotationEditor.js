import React, { useState, useEffect } from 'react';
import logger from '../../utils/logger';
import './AnnotationEditor.css';

const AnnotationEditor = ({ annotation, selectedCase, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    annotator: '',
    structure: {
      rows: 0,
      cols: 0,
      merged_cells: [],
      headers: []
    },
    content: '',
    status: 'draft'
  });
  const [contentMode, setContentMode] = useState('markdown'); // markdown | html
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    if (annotation) {
      // 编辑现有标注
      try {
        const structure = typeof annotation.table_structure === 'string' 
          ? JSON.parse(annotation.table_structure)
          : annotation.table_structure;
        
        setFormData({
          annotator: annotation.annotator || '',
          structure: structure || { rows: 0, cols: 0, merged_cells: [], headers: [] },
          content: annotation.table_content || '',
          status: annotation.status || 'draft'
        });
      } catch (err) {
        logger.error('解析标注数据失败:', err);
      }
    } else {
      // 新建标注
      setFormData({
        annotator: localStorage.getItem('annotator_name') || '',
        structure: {
          rows: 0,
          cols: 0,
          merged_cells: [],
          headers: []
        },
        content: '',
        status: 'draft'
      });
    }
  }, [annotation]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // 保存标注员名称到本地存储
    if (field === 'annotator') {
      localStorage.setItem('annotator_name', value);
    }
  };

  const handleStructureChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      structure: {
        ...prev.structure,
        [field]: value
      }
    }));
  };

  const handleSave = () => {
    // 验证必填字段
    if (!formData.annotator.trim()) {
      alert('请输入标注员姓名');
      return;
    }
    
    if (!formData.content.trim()) {
      alert('请输入表格内容');
      return;
    }

    onSave(formData);
  };

  const generateMarkdownTable = () => {
    const { rows, cols } = formData.structure;
    if (rows <= 0 || cols <= 0) return '';

    let markdown = '';
    
    // 生成表头
    const headerRow = Array(cols).fill('列').map((col, i) => `${col}${i + 1}`).join(' | ');
    const separatorRow = Array(cols).fill('---').join(' | ');
    
    markdown += `| ${headerRow} |\n`;
    markdown += `| ${separatorRow} |\n`;
    
    // 生成数据行
    for (let i = 0; i < rows - 1; i++) {
      const dataRow = Array(cols).fill('数据').map((data, j) => `${data}${i + 1}-${j + 1}`).join(' | ');
      markdown += `| ${dataRow} |\n`;
    }
    
    return markdown;
  };

  const insertTemplate = () => {
    const template = generateMarkdownTable();
    setFormData(prev => ({
      ...prev,
      content: template
    }));
  };

  const renderPreview = () => {
    if (!formData.content) return <p>暂无内容</p>;
    
    if (contentMode === 'markdown') {
      // 简单的Markdown表格渲染
      const lines = formData.content.split('\n');
      const tableLines = lines.filter(line => line.trim().startsWith('|'));
      
      if (tableLines.length === 0) {
        return <pre>{formData.content}</pre>;
      }
      
      return (
        <table className="preview-table">
          <tbody>
            {tableLines.map((line, index) => {
              const cells = line.split('|').slice(1, -1).map(cell => cell.trim());
              const isHeader = index === 0;
              const isSeparator = line.includes('---');
              
              if (isSeparator) return null;
              
              return (
                <tr key={index} className={isHeader ? 'header-row' : ''}>
                  {cells.map((cell, cellIndex) => (
                    isHeader ? 
                      <th key={cellIndex}>{cell}</th> : 
                      <td key={cellIndex}>{cell}</td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      );
    } else {
      // HTML内容直接渲染
      return <div dangerouslySetInnerHTML={{ __html: formData.content }} />;
    }
  };

  return (
    <div className="annotation-editor">
      <div className="annotation-editor-header">
        <h4>{annotation ? '编辑标注' : '新建标注'}</h4>
        <div className="annotation-editor-actions">
          <button className="btn btn-primary" onClick={handleSave}>
            保存
          </button>
          <button className="btn btn-secondary" onClick={onCancel}>
            取消
          </button>
        </div>
      </div>

      <div className="annotation-editor-content">
        {/* 基本信息 */}
        <div className="form-section">
          <h5>基本信息</h5>
          <div className="form-row">
            <div className="form-group">
              <label>标注员:</label>
              <input
                type="text"
                value={formData.annotator}
                onChange={(e) => handleInputChange('annotator', e.target.value)}
                placeholder="请输入标注员姓名"
              />
            </div>
            <div className="form-group">
              <label>状态:</label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                <option value="draft">草稿</option>
                <option value="completed">完成</option>
                <option value="reviewed">已审核</option>
              </select>
            </div>
          </div>
        </div>

        {/* 表格结构 */}
        <div className="form-section">
          <h5>表格结构</h5>
          <div className="form-row">
            <div className="form-group">
              <label>行数:</label>
              <input
                type="number"
                min="0"
                value={formData.structure.rows}
                onChange={(e) => handleStructureChange('rows', parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="form-group">
              <label>列数:</label>
              <input
                type="number"
                min="0"
                value={formData.structure.cols}
                onChange={(e) => handleStructureChange('cols', parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="form-group">
              <button 
                type="button" 
                className="btn btn-small"
                onClick={insertTemplate}
              >
                生成模板
              </button>
            </div>
          </div>
        </div>

        {/* 表格内容 */}
        <div className="form-section">
          <div className="form-section-header">
            <h5>表格内容</h5>
            <div className="content-mode-toggle">
              <button
                className={`btn btn-small ${contentMode === 'markdown' ? 'active' : ''}`}
                onClick={() => setContentMode('markdown')}
              >
                Markdown
              </button>
              <button
                className={`btn btn-small ${contentMode === 'html' ? 'active' : ''}`}
                onClick={() => setContentMode('html')}
              >
                HTML
              </button>
              <button
                className={`btn btn-small ${previewMode ? 'active' : ''}`}
                onClick={() => setPreviewMode(!previewMode)}
              >
                预览
              </button>
            </div>
          </div>

          {previewMode ? (
            <div className="content-preview">
              {renderPreview()}
            </div>
          ) : (
            <textarea
              className="content-editor"
              value={formData.content}
              onChange={(e) => handleInputChange('content', e.target.value)}
              placeholder={contentMode === 'markdown' ? 
                '请输入Markdown格式的表格内容...\n例如:\n| 列1 | 列2 |\n| --- | --- |\n| 数据1 | 数据2 |' :
                '请输入HTML格式的表格内容...'
              }
              rows={15}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default AnnotationEditor;
