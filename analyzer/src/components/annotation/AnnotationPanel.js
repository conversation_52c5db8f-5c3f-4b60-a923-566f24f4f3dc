import React, { useState, useEffect } from 'react';
import logger from '../../utils/logger';
import { createAnnotation, updateAnnotation, deleteAnnotation } from '../../services/api';
import AnnotationEditor from './AnnotationEditor';
import AnnotationList from './AnnotationList';
import AnnotationManager from '../AnnotationManager';
import './AnnotationPanel.css';

const AnnotationPanel = ({
  selectedCase,
  selectedDataset,
  annotationData,
  annotationsLoading,
  onAnnotationChange
}) => {
  const [annotations, setAnnotations] = useState([]);
  const [selectedAnnotation, setSelectedAnnotation] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('case'); // case | dataset

  // 使用传入的标注数据
  useEffect(() => {
    if (selectedCase) {
      if (annotationData) {
        // 如果已经有传入的标注数据，直接使用
        logger.debug('AnnotationPanel: 使用传入的标注数据');

        // 将单个标注转换为数组格式
        const annotationsArray = [annotationData];
        setAnnotations(annotationsArray);
        setSelectedAnnotation(annotationData);
        setLoading(false);
        setError(null);
      } else {
        // 如果没有标注数据，显示空状态
        logger.debug('AnnotationPanel: 没有标注数据');
        setAnnotations([]);
        setSelectedAnnotation(null);
        setLoading(false);
        setError(null);
      }
    }
  }, [selectedCase, annotationData]);

  // 不再需要loadAnnotations函数，标注数据通过props传入

  const handleCreateAnnotation = async (annotationData) => {
    try {
      const newAnnotation = await createAnnotation({
        dataset_name: selectedDataset,           // 使用传递的数据集名称
        image_filename: selectedCase.fileName,   // 使用图片文件名
        annotator: annotationData.annotator || 'anonymous',
        table_structure: JSON.stringify(annotationData.structure),
        table_content: annotationData.content,
        annotation_type: 'manual',
        status: 'draft'
      });
      
      setAnnotations([...annotations, newAnnotation]);
      setIsEditing(false);
      setSelectedAnnotation(null);
      
      if (onAnnotationChange) {
        onAnnotationChange();
      }
    } catch (err) {
      logger.error('创建标注失败:', err);
      setError('创建标注失败');
    }
  };

  const handleUpdateAnnotation = async (annotationId, updateData) => {
    try {
      const updatedAnnotation = await updateAnnotation(annotationId, {
        table_structure: JSON.stringify(updateData.structure),
        table_content: updateData.content,
        status: updateData.status
      });
      
      setAnnotations(annotations.map(ann => 
        ann.id === annotationId ? updatedAnnotation : ann
      ));
      setIsEditing(false);
      setSelectedAnnotation(null);
      
      if (onAnnotationChange) {
        onAnnotationChange();
      }
    } catch (err) {
      logger.error('更新标注失败:', err);
      setError('更新标注失败');
    }
  };

  const handleDeleteAnnotation = async (annotationId) => {
    if (!window.confirm('确定要删除这个标注吗？')) {
      return;
    }
    
    try {
      await deleteAnnotation(annotationId);
      setAnnotations(annotations.filter(ann => ann.id !== annotationId));
      
      if (selectedAnnotation && selectedAnnotation.id === annotationId) {
        setSelectedAnnotation(null);
        setIsEditing(false);
      }
      
      if (onAnnotationChange) {
        onAnnotationChange();
      }
    } catch (err) {
      logger.error('删除标注失败:', err);
      setError('删除标注失败');
    }
  };

  const handleEditAnnotation = (annotation) => {
    setSelectedAnnotation(annotation);
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setSelectedAnnotation(null);
    setIsEditing(false);
  };

  const handleStartNewAnnotation = () => {
    setSelectedAnnotation(null);
    setIsEditing(true);
  };

  return (
    <div className="annotation-panel">
      <div className="annotation-panel-header">
        <h3>人工标注</h3>
        <div className="annotation-tab-navigation">
          <button
            className={`tab-button ${activeTab === 'case' ? 'active' : ''}`}
            onClick={() => setActiveTab('case')}
          >
            案例标注
          </button>
          <button
            className={`tab-button ${activeTab === 'dataset' ? 'active' : ''}`}
            onClick={() => setActiveTab('dataset')}
          >
            数据集管理
          </button>
        </div>
      </div>

      {error && (
        <div className="annotation-error">
          <p>{error}</p>
          <button onClick={() => setError(null)}>关闭</button>
        </div>
      )}

      <div className="annotation-panel-content">
        {activeTab === 'dataset' ? (
          <AnnotationManager
            selectedDataset={selectedDataset}
            onAnnotationChange={onAnnotationChange}
          />
        ) : (
          <>
            {!selectedCase ? (
              <div className="annotation-panel-empty">
                <p>请选择一个测试案例开始标注</p>
              </div>
            ) : isEditing ? (
              <AnnotationEditor
                annotation={selectedAnnotation}
                selectedCase={selectedCase}
                onSave={selectedAnnotation ?
                  (data) => handleUpdateAnnotation(selectedAnnotation.id, data) :
                  handleCreateAnnotation
                }
                onCancel={handleCancelEdit}
              />
            ) : (
              <>
                <div className="annotation-panel-info">
                  <span>图片: {selectedCase.fileName}</span>
                  <span>标注数量: {annotations.length}</span>
                </div>

                <div className="annotation-panel-actions">
                  <button
                    className="btn btn-primary"
                    onClick={handleStartNewAnnotation}
                  >
                    新建标注
                  </button>
                  {/* 不再需要刷新按钮，标注数据通过props传入 */}
                </div>

                <AnnotationList
                  annotations={annotations}
                  onEdit={handleEditAnnotation}
                  onDelete={handleDeleteAnnotation}
                  loading={loading}
                />
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default AnnotationPanel;
