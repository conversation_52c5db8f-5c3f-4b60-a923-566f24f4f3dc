import React, { useState, useEffect } from 'react';
import { PARSER_CONFIGS, PARSER_CATEGORIES, DEFAULT_ENABLED_PARSERS } from '../utils/parserConfig';
import './ParserSelector.css';

/**
 * 解析器选择器组件
 * 提供多路解析器的复选框开关控制
 */
const ParserSelector = ({ 
  enabledParsers = DEFAULT_ENABLED_PARSERS, 
  onParsersChange,
  disabled = false 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localEnabledParsers, setLocalEnabledParsers] = useState(enabledParsers);

  useEffect(() => {
    setLocalEnabledParsers(enabledParsers);
  }, [enabledParsers]);

  const handleParserToggle = (parserKey) => {
    if (disabled) return;

    const newEnabledParsers = localEnabledParsers.includes(parserKey)
      ? localEnabledParsers.filter(key => key !== parserKey)
      : [...localEnabledParsers, parserKey];
    
    setLocalEnabledParsers(newEnabledParsers);
    onParsersChange?.(newEnabledParsers);
  };

  const handleSelectAll = () => {
    if (disabled) return;
    
    const allParsers = PARSER_CONFIGS.map(config => config.key);
    setLocalEnabledParsers(allParsers);
    onParsersChange?.(allParsers);
  };

  const handleSelectNone = () => {
    if (disabled) return;
    
    setLocalEnabledParsers([]);
    onParsersChange?.([]);
  };

  const handleCategoryToggle = (category) => {
    if (disabled) return;

    const categoryParsers = PARSER_CONFIGS
      .filter(config => config.category === category)
      .map(config => config.key);
    
    const allCategoryEnabled = categoryParsers.every(key => 
      localEnabledParsers.includes(key)
    );

    let newEnabledParsers;
    if (allCategoryEnabled) {
      // 取消选择该分类的所有解析器
      newEnabledParsers = localEnabledParsers.filter(key => 
        !categoryParsers.includes(key)
      );
    } else {
      // 选择该分类的所有解析器
      newEnabledParsers = [
        ...localEnabledParsers.filter(key => !categoryParsers.includes(key)),
        ...categoryParsers
      ];
    }

    setLocalEnabledParsers(newEnabledParsers);
    onParsersChange?.(newEnabledParsers);
  };

  const enabledCount = localEnabledParsers.length;
  const totalCount = PARSER_CONFIGS.length;

  return (
    <div className="parser-selector">
      <div className="parser-selector-header">
        <label className="parser-selector-label">
          多路解析:
        </label>
        <div className="parser-selector-summary">
          <button
            className={`parser-selector-toggle ${isExpanded ? 'expanded' : ''}`}
            onClick={() => setIsExpanded(!isExpanded)}
            disabled={disabled}
            title={isExpanded ? "收起解析器选择" : "展开解析器选择"}
          >
            <span className="toggle-text">
              已选择 {enabledCount}/{totalCount} 个解析器
            </span>
            <span className={`toggle-arrow ${isExpanded ? 'expanded' : ''}`}>
              ▼
            </span>
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="parser-selector-panel">
          <div className="parser-selector-controls">
            <button
              className="parser-selector-btn parser-selector-btn-all"
              onClick={handleSelectAll}
              disabled={disabled || enabledCount === totalCount}
            >
              全选
            </button>
            <button
              className="parser-selector-btn parser-selector-btn-none"
              onClick={handleSelectNone}
              disabled={disabled || enabledCount === 0}
            >
              全不选
            </button>
          </div>

          <div className="parser-selector-categories">
            {Object.entries(PARSER_CATEGORIES).map(([categoryKey, categoryName]) => {
              const categoryParsers = PARSER_CONFIGS.filter(config => 
                config.category === categoryKey
              );
              const categoryEnabledCount = categoryParsers.filter(config => 
                localEnabledParsers.includes(config.key)
              ).length;
              const allCategoryEnabled = categoryEnabledCount === categoryParsers.length;
              const someCategoryEnabled = categoryEnabledCount > 0;

              return (
                <div key={categoryKey} className="parser-category">
                  <div className="parser-category-header">
                    <label className="parser-category-label">
                      <input
                        type="checkbox"
                        checked={allCategoryEnabled}
                        ref={input => {
                          if (input) input.indeterminate = someCategoryEnabled && !allCategoryEnabled;
                        }}
                        onChange={() => handleCategoryToggle(categoryKey)}
                        disabled={disabled}
                      />
                      <span className="parser-category-name">
                        {categoryName} ({categoryEnabledCount}/{categoryParsers.length})
                      </span>
                    </label>
                  </div>
                  <div className="parser-category-items">
                    {categoryParsers.map(config => (
                      <label key={config.key} className="parser-item">
                        <input
                          type="checkbox"
                          checked={localEnabledParsers.includes(config.key)}
                          onChange={() => handleParserToggle(config.key)}
                          disabled={disabled}
                        />
                        <span className="parser-item-name">
                          {config.displayName}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default ParserSelector;
