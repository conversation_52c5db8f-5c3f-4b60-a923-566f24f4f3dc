// 模拟 logger
const logger = {
  error: console.log,
  debug: console.log,
  warn: console.warn
};

// 模拟 JsonSchemaTableParser
const JsonSchemaTableParser = {
  parseJsonSchemaTable: (data) => data
};

// 测试数据
const testData = {
  metadata: {
    total_files: 1,
    status: "completed"
  },
  results: [
    {
      index: 1,
      fileName: "test.pdf",
      mineru_vlm: {
        success: true,
        result: {
          layout_data: [
            {
              type: "title",
              text: "测试标题",
              bbox: [100, 100, 400, 150]
            },
            {
              type: "table",
              text: "| 列1 | 列2 |\n| --- | --- |\n| 数据1 | 数据2 |",
              bbox: [100, 200, 400, 300]
            }
          ]
        }
      }
    }
  ]
};

// 测试 processParseResults
const processParseResults = (parseData, imageList = []) => {
  if (!parseData) return [];

  const results = parseData.results || [];
  return results.map((result, index) => ({
    id: `case_${index}`,
    index: index + 1,
    fileName: result.fileName,
    mineru_vlm: result.mineru_vlm
  }));
};

// 运行测试
const processedCases = processParseResults(testData, ['test.png']);

// 检查结果
console.log('处理结果:', {
  totalCases: processedCases.length,
  firstCase: processedCases[0],
  hasMineruVlm: !!processedCases[0]?.mineru_vlm,
  mineruVlmSuccess: processedCases[0]?.mineru_vlm?.success,
  hasResult: !!processedCases[0]?.mineru_vlm?.result,
  hasLayoutData: !!processedCases[0]?.mineru_vlm?.result?.layout_data,
  layoutDataType: typeof processedCases[0]?.mineru_vlm?.result?.layout_data,
  isLayoutDataArray: Array.isArray(processedCases[0]?.mineru_vlm?.result?.layout_data),
  layoutDataLength: processedCases[0]?.mineru_vlm?.result?.layout_data?.length,
  fullMineruVlmData: processedCases[0]?.mineru_vlm
});