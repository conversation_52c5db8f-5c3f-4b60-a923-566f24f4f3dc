/**
 * HTML报告生成器
 * 生成独立的HTML页面展示数据集的详细统计报告
 */

import { calculateParserStats, calculateMetricsForCase } from './dataProcessor';
import { PARSER_CONFIGS, getParserDisplayName } from './parserConfig';
import logger from './logger';

/**
 * 生成HTML格式的数据集报告
 * @param {Array} cases - 案例数据
 * @param {string} dataset - 数据集名称
 * @param {Array} annotationData - 标注数据
 * @param {Array} enabledParsers - 启用的解析器列表
 */
export const generateHTMLReport = (cases, dataset, annotationData, enabledParsers = null) => {
  try {
    logger.debug('开始生成HTML报告:', { casesLength: cases.length, dataset, enabledParsers });
    const reportData = generateReportData(cases, dataset, annotationData, enabledParsers);
    if (!reportData) {
      logger.error('generateReportData返回null');
      return '<html><body><h1>报告生成失败：无数据</h1></body></html>';
    }

    logger.debug('reportData生成成功，开始生成HTML');
    const html = generateReportHTML(reportData);
    logger.debug('HTML生成成功');
    return html;
  } catch (error) {
    logger.error('生成HTML报告时发生错误:', error);
    return `<html><body><h1>报告生成失败</h1><p>错误信息: ${error.message}</p></body></html>`;
  }
};



/**
 * 生成详细的调试信息
 */
function generateDebugInfo(cases, dataset, annotationData, enabledParsers = null) {
  try {
    logger.debug('开始生成调试信息...');

    const debugInfo = {
      dataset,
      totalCases: cases.length,
      casesWithAnnotation: 0,
      casesWithAccuracyResult: 0,
      casesWithTedsResult: 0,
      parserDebugInfo: {},
      caseDetails: []
    };

    // 初始化解析器调试信息 - 使用启用的解析器列表或默认配置
    const parserNames = enabledParsers || PARSER_CONFIGS.map(config => config.key);
    parserNames.forEach(parser => {
      debugInfo.parserDebugInfo[parser] = {
        name: getParserDisplayName(parser),
        totalCases: 0,
        successCases: 0,
        validAccuracyCases: 0,
        validTedsCount: 0,
        totalAccuracy: 0,
        totalTeds: 0,
        totalTedsStructure: 0,
        totalTedsContent: 0,
        avgAccuracy: null,
        avgTeds: null,
        avgTedsStructure: null,
        avgTedsContent: null,
        validCases: [],
        failedCases: []
      };
    });

    // 分析每个案例
    cases.forEach((caseData, index) => {
      try {
        // 修复：使用传入的annotationData参数查找匹配的annotation
        let caseAnnotation = null;
        if (annotationData && Array.isArray(annotationData)) {
          caseAnnotation = annotationData.find(ann => 
            ann.image_filename === caseData.fileName
          );
        }
        
        const hasAnnotation = !!caseAnnotation;
        
        if (hasAnnotation) {
          debugInfo.casesWithAnnotation++;
        }

        // 计算准确率和TEDS
        let metricsResult = null;
        let hasAccuracyResult = false;
        let hasTedsResult = false;

        try {
          metricsResult = calculateMetricsForCase(caseData, caseAnnotation);
          hasAccuracyResult = !!(metricsResult && metricsResult.accuracies);
          hasTedsResult = !!(metricsResult && metricsResult.tedsScores);
        } catch (error) {
          logger.warn(`案例 ${index + 1} 指标计算失败:`, error);
        }

        if (hasAccuracyResult) {
          debugInfo.casesWithAccuracyResult++;
        }
        if (hasTedsResult) {
          debugInfo.casesWithTedsResult++;
        }

        // 案例详情
        const caseDetail = {
          index: index + 1,
          fileName: caseData.fileName || `case_${index + 1}`,
          hasAnnotation,
          annotationType: hasAnnotation ? (caseAnnotation.elements ? 'JSON Schema' : 'Table Content') : 'None',
          hasAccuracyResult,
          hasTedsResult
        };

        debugInfo.caseDetails.push(caseDetail);

        // 更新解析器统计
        if (hasAccuracyResult && metricsResult && metricsResult.accuracies) {
          const { accuracies = {}, tedsScores = {}, tedsDetails = {} } = metricsResult;
          
          // 检查各解析器状态
          const parserStatus = {
            kdcMarkdown: {
              hasResult: !!(caseData.kdcMarkdown?.result?.data?.[0]?.markdown || caseData.kdcMarkdown?.data?.[0]?.markdown),
              accuracy: accuracies.kdcMarkdown,
              teds: tedsScores.kdcMarkdown,
              tedsStructure: tedsDetails.kdcMarkdown?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.kdcMarkdown?.contentAnalysis?.contentSimilarity
            },
            kdcPlain: {
              hasResult: !!(caseData.kdcPlain?.result?.data?.[0]?.plain || caseData.kdcPlain?.data?.[0]?.plain),
              accuracy: accuracies.kdcPlain,
              teds: tedsScores.kdcPlain,
              tedsStructure: tedsDetails.kdcPlain?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.kdcPlain?.contentAnalysis?.contentSimilarity
            },
            kdcKdc: {
              hasResult: !!(caseData.kdcKdc?.result?.data?.[0]?.doc || caseData.kdcKdc?.data?.[0]?.doc),
              accuracy: accuracies.kdcKdc,
              teds: tedsScores.kdcKdc,
              tedsStructure: tedsDetails.kdcKdc?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.kdcKdc?.contentAnalysis?.contentSimilarity
            },
            monkeyOCR: {
              hasResult: !!(caseData.monkeyOCR?.result?.html || caseData.monkeyOCR?.html),
              accuracy: accuracies.monkeyOCR,
              teds: tedsScores.monkeyOCR,
              tedsStructure: tedsDetails.monkeyOCR?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.monkeyOCR?.contentAnalysis?.contentSimilarity
            },
            monkeyOCRV2: {
              hasResult: !!(caseData.monkeyOCRV2?.result?.html || caseData.monkeyOCRV2?.html),
              accuracy: accuracies.monkeyOCRV2,
              teds: tedsScores.monkeyOCRV2,
              tedsStructure: tedsDetails.monkeyOCRV2?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.monkeyOCRV2?.contentAnalysis?.contentSimilarity
            },
            vlLLM: {
              hasResult: !!caseData.vlLLMResult?.result,
              accuracy: accuracies.vlLLM,
              teds: tedsScores.vlLLM,
              tedsStructure: tedsDetails.vlLLM?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.vlLLM?.contentAnalysis?.contentSimilarity
            },
            monkeyOCRLocal: {
              hasResult: !!(caseData.monkeyOCRLocal?.results?.[0]?.result || caseData.monkeyOCRLocal?.result),
              accuracy: accuracies.monkeyOCRLocal,
              teds: tedsScores.monkeyOCRLocal,
              tedsStructure: tedsDetails.monkeyOCRLocal?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.monkeyOCRLocal?.contentAnalysis?.contentSimilarity
            },
            monkeyOcrKas: {
              hasResult: !!(caseData.monkeyOcrKas?.result?.content || caseData.monkeyOcrKas?.content),
              accuracy: accuracies.monkeyOcrKas,
              teds: tedsScores.monkeyOcrKas,
              tedsStructure: tedsDetails.monkeyOcrKas?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.monkeyOcrKas?.contentAnalysis?.contentSimilarity
            },
            ocrflux: {
              hasResult: !!(caseData.ocrflux?.result?.content || caseData.ocrflux?.content),
              accuracy: accuracies.ocrflux,
              teds: tedsScores.ocrflux,
              tedsStructure: tedsDetails.ocrflux?.structureAnalysis?.structureSimilarity,
              tedsContent: tedsDetails.ocrflux?.contentAnalysis?.contentSimilarity
            }
          };

          Object.entries(parserStatus).forEach(([parserKey, status]) => {
            const parser = debugInfo.parserDebugInfo[parserKey];
            parser.totalCases++;

            if (status.hasResult) {
              parser.successCases++;
            }

            if (typeof status.accuracy === 'number' && !isNaN(status.accuracy)) {
              parser.validAccuracyCases++;
              parser.totalAccuracy += status.accuracy;
              parser.validCases.push({
                index: index + 1,
                fileName: caseData.fileName || `case_${index + 1}`,
                accuracy: status.accuracy,
                teds: status.teds
              });
            } else {
              parser.failedCases.push({
                index: index + 1,
                fileName: caseData.fileName || `case_${index + 1}`,
                hasResult: status.hasResult,
                reason: !status.hasResult ? '无解析结果' : '准确率计算失败'
              });
            }

            if (typeof status.teds === 'number' && !isNaN(status.teds)) {
              parser.validTedsCount++;
              parser.totalTeds += status.teds;

              // 添加TEDS结构和内容得分统计
              if (typeof status.tedsStructure === 'number' && !isNaN(status.tedsStructure)) {
                parser.totalTedsStructure += status.tedsStructure;
              }
              if (typeof status.tedsContent === 'number' && !isNaN(status.tedsContent)) {
                parser.totalTedsContent += status.tedsContent;
              }
            }
          });
        }
      } catch (error) {
        logger.warn(`处理案例 ${index + 1} 时发生错误:`, error);
      }
    });

    // 计算平均准确率和TEDS
    Object.values(debugInfo.parserDebugInfo).forEach(parser => {
      parser.avgAccuracy = parser.validAccuracyCases > 0
        ? (parser.totalAccuracy / parser.validAccuracyCases)
        : null;
      parser.avgTeds = parser.validTedsCount > 0
        ? (parser.totalTeds / parser.validTedsCount)
        : null;
      parser.avgTedsStructure = parser.validTedsCount > 0
        ? (parser.totalTedsStructure / parser.validTedsCount)
        : null;
      parser.avgTedsContent = parser.validTedsCount > 0
        ? (parser.totalTedsContent / parser.validTedsCount)
        : null;
    });

    logger.debug('调试信息生成完成:', debugInfo);
    return debugInfo;
  } catch (error) {
    logger.error('生成调试信息时发生错误:', error);
    return {
      dataset,
      totalCases: cases.length,
      casesWithAnnotation: 0,
      casesWithAccuracyResult: 0,
      casesWithTedsResult: 0,
      parserDebugInfo: {},
      caseDetails: [],
      error: error.message
    };
  }
}

function generateReportData(cases, dataset, annotationData, enabledParsers = null) {
  if (!cases.length) return null;

  // 使用统一的解析器统计计算函数，传递启用的解析器列表
  const report = calculateParserStats(cases, annotationData, enabledParsers);
  if (!report) return null;

  // 调试：检查计算结果
  logger.debug('HTML报告数据生成完成:', {
    totalCases: report.totalCases,
    parsersCount: Object.keys(report.parsers).length,
    ocrfluxStats: report.parsers.ocrflux ? {
      totalCases: report.parsers.ocrflux.totalCases,
      successCases: report.parsers.ocrflux.successCases,
      validAccuracyCases: report.parsers.ocrflux.validAccuracyCases,
      avgAccuracy: report.parsers.ocrflux.avgAccuracy,
      avgTeds: report.parsers.ocrflux.avgTeds
    } : 'ocrflux not found'
  });

  // 添加HTML报告特有的字段
  report.dataset = dataset;
  report.generatedAt = new Date().toISOString();
  report.debugInfo = generateDebugInfo(cases, dataset, annotationData, enabledParsers);

  return report;
}

function generateReportHTML(reportData) {
  const { debugInfo } = reportData;
  
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数据集报告 - ${reportData.dataset}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 1400px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px 40px;
      text-align: center;
    }
    .content {
      padding: 30px 40px;
    }
    .section {
      margin-bottom: 40px;
    }
    .section h2 {
      color: #333;
      border-bottom: 2px solid #667eea;
      padding-bottom: 10px;
      margin-bottom: 20px;
    }
    .section h3 {
      color: #555;
      margin-top: 30px;
      margin-bottom: 15px;
    }
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    .stat-card {
      background: #f8f9fa;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #667eea;
      text-align: center;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #667eea;
      display: block;
    }
    .stat-label {
      font-size: 14px;
      color: #666;
      margin-top: 5px;
    }
    .table-container {
      overflow-x: auto;
      margin: 20px 0;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      background: white;
    }
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #e9ecef;
    }
    th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #495057;
    }
    tr:hover {
      background-color: #f8f9fa;
    }
    .accuracy-value {
      font-weight: bold;
    }
    .accuracy-high { color: #28a745; }
    .accuracy-medium { color: #ffc107; }
    .accuracy-low { color: #dc3545; }
    .accuracy-none { color: #6c757d; }
    .debug-section {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 25px;
      margin: 20px 0;
    }
    .debug-overview {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 15px;
      margin-bottom: 30px;
    }
    .debug-card {
      background: white;
      padding: 20px;
      border-radius: 6px;
      border: 1px solid #e9ecef;
    }
    .debug-card h4 {
      margin: 0 0 10px 0;
      color: #495057;
      font-size: 16px;
    }
    .debug-value {
      font-size: 24px;
      font-weight: bold;
      color: #667eea;
    }
    .debug-percentage {
      font-size: 14px;
      color: #6c757d;
      margin-top: 5px;
    }
    .parser-debug-table {
      margin: 20px 0;
    }
    .parser-debug-table th {
      background-color: #667eea;
      color: white;
    }
    .calculation-formula {
      background: #e7f3ff;
      border: 1px solid #b3d9ff;
      border-radius: 4px;
      padding: 10px;
      margin: 10px 0;
      font-family: monospace;
      font-size: 14px;
    }
    .case-list {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 10px;
      background: white;
    }
    .case-item {
      padding: 8px;
      border-bottom: 1px solid #f8f9fa;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .case-item:last-child {
      border-bottom: none;
    }
    .case-name {
      flex: 1;
      font-size: 13px;
    }
    .case-accuracy {
      font-weight: bold;
      font-size: 14px;
    }
    .failed-reason {
      color: #dc3545;
      font-size: 12px;
      font-style: italic;
    }
    .summary-highlight {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px;
      border-radius: 8px;
      margin: 20px 0;
    }
    .summary-highlight h3 {
      margin: 0 0 15px 0;
      color: white;
    }
    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
    }
    .summary-item {
      text-align: center;
    }
    .summary-value {
      font-size: 28px;
      font-weight: bold;
      display: block;
    }
    .summary-label {
      font-size: 14px;
      opacity: 0.9;
      margin-top: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📊 数据集分析报告</h1>
      <p>生成时间: ${new Date(reportData.generatedAt).toLocaleString('zh-CN')}</p>
    </div>

    <div class="content">
      <!-- 数据集概览 -->
      <div class="section">
        <h2>📋 数据集概览</h2>
        <div class="summary-highlight">
          <h3>关键指标</h3>
          <div class="summary-grid">
            <div class="summary-item">
              <span class="summary-value">${debugInfo.totalCases}</span>
              <div class="summary-label">总案例数</div>
            </div>
            <div class="summary-item">
              <span class="summary-value">${debugInfo.casesWithAnnotation}</span>
              <div class="summary-label">有标注数据</div>
            </div>
            <div class="summary-item">
              <span class="summary-value">${debugInfo.casesWithAccuracyResult}</span>
              <div class="summary-label">准确率计算成功</div>
            </div>
          </div>
        </div>

        <div class="debug-overview">
          <div class="debug-card">
            <h4>标注数据覆盖率</h4>
            <div class="debug-value">${((debugInfo.casesWithAnnotation / debugInfo.totalCases) * 100).toFixed(1)}%</div>
            <div class="debug-percentage">${debugInfo.casesWithAnnotation} / ${debugInfo.totalCases} 案例</div>
          </div>
          <div class="debug-card">
            <h4>准确率计算成功率</h4>
            <div class="debug-value">${((debugInfo.casesWithAccuracyResult / debugInfo.totalCases) * 100).toFixed(1)}%</div>
            <div class="debug-percentage">${debugInfo.casesWithAccuracyResult} / ${debugInfo.totalCases} 案例</div>
          </div>
        </div>
      </div>

      <!-- 解析器性能汇总 -->
      <div class="section">
        <h2>🔧 解析器性能汇总</h2>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>解析器</th>
                <th>解析成功率</th>
                <th>表格解析率</th>
                <th>平均准确率</th>
                <th>平均TEDS</th>
                <th>TEDS结构得分</th>
                <th>TEDS内容得分</th>
                <th>有效案例数</th>
              </tr>
            </thead>
            <tbody>
              ${Object.values(reportData.parsers).map(parser => {
                const accuracy = parser.avgAccuracy;
                const teds = parser.avgTeds;
                const tedsStructure = parser.avgTedsStructure;
                const tedsContent = parser.avgTedsContent;

                // 调试日志
                logger.debug(`HTML报告生成 - ${parser.name}:`, {
                  tedsStructure: tedsStructure,
                  tedsContent: tedsContent,
                  tedsStructureType: typeof tedsStructure,
                  tedsContentType: typeof tedsContent
                });
                const accuracyClass = accuracy === null ? 'accuracy-none' :
                                    accuracy >= 80 ? 'accuracy-high' :
                                    accuracy >= 60 ? 'accuracy-medium' : 'accuracy-low';
                const tedsClass = teds === null ? 'accuracy-none' :
                                teds >= 80 ? 'accuracy-high' :
                                teds >= 60 ? 'accuracy-medium' : 'accuracy-low';
                const tedsStructureClass = tedsStructure === null ? 'accuracy-none' :
                                        tedsStructure >= 80 ? 'accuracy-high' :
                                        tedsStructure >= 60 ? 'accuracy-medium' : 'accuracy-low';
                const tedsContentClass = tedsContent === null ? 'accuracy-none' :
                                      tedsContent >= 80 ? 'accuracy-high' :
                                      tedsContent >= 60 ? 'accuracy-medium' : 'accuracy-low';
                return `
                <tr>
                  <td><strong>${parser.name}</strong></td>
                  <td>${parser.successRate}% (${parser.successCases}/${parser.totalCases})</td>
                  <td>${parser.tableParsingRate}% (${parser.tableParsingCases}/${parser.totalCases})</td>
                  <td class="accuracy-value ${accuracyClass}">
                    ${accuracy !== null ? `${accuracy}%` : '-'}
                    ${accuracy === null && parser.validAccuracyCases === 0 ? '<br><small>(无标注数据)</small>' : ''}
                  </td>
                  <td class="accuracy-value ${tedsClass}">
                    ${teds !== null ? `${teds}%` : '-'}
                    ${teds === null && parser.validTedsCount === 0 ? '<br><small>(无标注数据)</small>' : ''}
                  </td>
                  <td class="accuracy-value ${tedsStructureClass}">
                    ${tedsStructure !== null && tedsStructure !== undefined ? `${Number(tedsStructure).toFixed(1)}%` : '-'}
                    ${(tedsStructure === null || tedsStructure === undefined) && parser.validTedsCount === 0 ? '<br><small>(无标注数据)</small>' : ''}
                  </td>
                  <td class="accuracy-value ${tedsContentClass}">
                    ${tedsContent !== null && tedsContent !== undefined ? `${Number(tedsContent).toFixed(1)}%` : '-'}
                    ${(tedsContent === null || tedsContent === undefined) && parser.validTedsCount === 0 ? '<br><small>(无标注数据)</small>' : ''}
                  </td>
                  <td>准确率: ${parser.validAccuracyCases}/${parser.totalCases}<br>TEDS: ${parser.validTedsCount}/${parser.totalCases}</td>
                </tr>
                `;
              }).join('')}
            </tbody>
          </table>
        </div>
      </div>

      <!-- 详细计算过程 -->
      <div class="section">
        <h2>🧮 指标计算详情</h2>
        <div class="debug-section">
          <h3>计算公式说明</h3>
          <div class="calculation-formula">
            <strong>指标计算说明：</strong><br>
            • 平均准确率 = Σ(有效案例准确率) ÷ 有效案例数量<br>
            • 平均TEDS = Σ(有效案例TEDS) ÷ 有效案例数量<br>
            • TEDS结构得分 = Σ(有效案例TEDS结构得分) ÷ 有效案例数量<br>
            • TEDS内容得分 = Σ(有效案例TEDS内容得分) ÷ 有效案例数量<br>
            • 有效案例定义：解析器有产生结果 AND 有人工标注数据 AND 指标计算成功（不为null）<br>
            • 如果没有有效案例，则显示 "-" 表示无法计算<br>
            • 当前数据集共 ${reportData.totalCases} 个案例，其中 ${reportData.debugInfo.casesWithAnnotation} 个有人工标注数据
          </div>

          ${Object.entries(debugInfo.parserDebugInfo).map(([parserKey, parser]) => `
            <h3>${parser.name} 详细计算过程</h3>
            <div style="background: white; border-radius: 6px; padding: 20px; margin: 15px 0;">
              
              <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                <div class="debug-card">
                  <h4>总案例数</h4>
                  <div class="debug-value">${parser.totalCases}</div>
                </div>
                <div class="debug-card">
                  <h4>解析成功</h4>
                  <div class="debug-value">${parser.successCases}</div>
                  <div class="debug-percentage">成功率: ${((parser.successCases / parser.totalCases) * 100).toFixed(1)}%</div>
                </div>
                <div class="debug-card">
                  <h4>有效准确率案例</h4>
                  <div class="debug-value">${parser.validAccuracyCases}</div>
                  <div class="debug-percentage">分母</div>
                </div>
                <div class="debug-card">
                  <h4>准确率总和</h4>
                  <div class="debug-value">${(parser.totalAccuracy || 0).toFixed(1)}</div>
                  <div class="debug-percentage">分子</div>
                </div>
                <div class="debug-card">
                  <h4>有效TEDS案例</h4>
                  <div class="debug-value">${parser.validTedsCount}</div>
                  <div class="debug-percentage">分母</div>
                </div>
                <div class="debug-card">
                  <h4>TEDS总和</h4>
                  <div class="debug-value">${(parser.totalTeds || 0).toFixed(1)}</div>
                  <div class="debug-percentage">分子</div>
                </div>
                <div class="debug-card">
                  <h4>TEDS结构得分总和</h4>
                  <div class="debug-value">${Number(parser.totalTedsStructure || 0).toFixed(1)}</div>
                  <div class="debug-percentage">分子</div>
                </div>
                <div class="debug-card">
                  <h4>TEDS内容得分总和</h4>
                  <div class="debug-value">${Number(parser.totalTedsContent || 0).toFixed(1)}</div>
                  <div class="debug-percentage">分子</div>
                </div>
              </div>

              <div class="calculation-formula">
                <strong>准确率计算过程:</strong><br>
                ${parser.validAccuracyCases > 0 
                  ? `${(parser.totalAccuracy || 0).toFixed(1)} ÷ ${parser.validAccuracyCases} = ${(parser.avgAccuracy || 0).toFixed(1)}%`
                  : '无有效准确率数据，结果为 "-"'
                }
                <br><br>
                <strong>TEDS计算过程:</strong><br>
                ${parser.validTedsCount > 0
                  ? `${(parser.totalTeds || 0).toFixed(1)} ÷ ${parser.validTedsCount} = ${(parser.avgTeds || 0).toFixed(1)}%`
                  : '无有效TEDS数据，结果为 "-"'
                }
                <br><br>
                <strong>TEDS结构得分计算过程:</strong><br>
                ${parser.validTedsCount > 0
                  ? `${Number(parser.totalTedsStructure || 0).toFixed(1)} ÷ ${parser.validTedsCount} = ${Number(parser.avgTedsStructure || 0).toFixed(1)}%`
                  : '无有效TEDS数据，结果为 "-"'
                }
                <br><br>
                <strong>TEDS内容得分计算过程:</strong><br>
                ${parser.validTedsCount > 0
                  ? `${Number(parser.totalTedsContent || 0).toFixed(1)} ÷ ${parser.validTedsCount} = ${Number(parser.avgTedsContent || 0).toFixed(1)}%`
                  : '无有效TEDS数据，结果为 "-"'
                }
              </div>

              ${parser.validCases.length > 0 ? `
                <h4>参与计算的案例 (${parser.validCases.length}个):</h4>
                <div class="case-list">
                  ${parser.validCases.map(caseInfo => `
                    <div class="case-item">
                      <span class="case-name">案例#${caseInfo.index}: ${caseInfo.fileName}</span>
                      <span class="case-metrics">
                        <span class="case-accuracy accuracy-${caseInfo.accuracy >= 80 ? 'high' : caseInfo.accuracy >= 60 ? 'medium' : 'low'}">
                          准确率: ${(caseInfo.accuracy || 0).toFixed(1)}%
                        </span>
                        ${caseInfo.teds !== undefined ? `
                          <span class="case-teds accuracy-${caseInfo.teds >= 80 ? 'high' : caseInfo.teds >= 60 ? 'medium' : 'low'}">
                            TEDS: ${(caseInfo.teds || 0).toFixed(1)}%
                          </span>
                        ` : ''}
                      </span>
                    </div>
                  `).join('')}
                </div>
              ` : ''}

              ${parser.failedCases.length > 0 ? `
                <h4>未参与计算的案例 (${parser.failedCases.length}个):</h4>
                <div class="case-list">
                  ${parser.failedCases.map(caseInfo => `
                    <div class="case-item">
                      <span class="case-name">案例#${caseInfo.index}: ${caseInfo.fileName}</span>
                      <span class="failed-reason">${caseInfo.reason}</span>
                    </div>
                  `).join('')}
                </div>
              ` : ''}
            </div>
          `).join('')}
        </div>
      </div>

      <!-- 案例明细分析 -->
      <div class="section">
        <h2>📝 案例明细分析</h2>
        <div class="table-container" style="max-height: 600px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px;">
          <table>
            <thead style="position: sticky; top: 0; background-color: #f8f9fa; z-index: 10; border-bottom: 2px solid #dee2e6;">
              <tr>
                <th>案例#</th>
                <th>文件名</th>
                <th>标注数据</th>
                <th>标注类型</th>
                <th>准确率计算</th>
                <th>TEDS计算</th>
              </tr>
            </thead>
            <tbody>
              ${debugInfo.caseDetails.map(caseDetail => `
                <tr>
                  <td>${caseDetail.index}</td>
                  <td>${caseDetail.fileName}</td>
                  <td>${caseDetail.hasAnnotation ? '✅' : '❌'}</td>
                  <td>${caseDetail.annotationType}</td>
                  <td>${caseDetail.hasAccuracyResult ? '✅' : '❌'}</td>
                  <td>${caseDetail.hasTedsResult ? '✅' : '❌'}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        <div style="text-align: center; margin-top: 10px; color: #6c757d; font-size: 14px;">
          📊 显示全部 ${debugInfo.caseDetails.length} 个案例
        </div>
      </div>

      <!-- 生成信息 -->
      <div class="section">
        <p style="text-align: center; color: #6c757d; font-size: 14px;">
          📄 报告生成于 ${new Date().toLocaleString('zh-CN')} | 数据集: ${reportData.dataset} | 总案例数: ${reportData.totalCases}
        </p>
      </div>
    </div>
  </div>
</body>
</html>`;
} 
