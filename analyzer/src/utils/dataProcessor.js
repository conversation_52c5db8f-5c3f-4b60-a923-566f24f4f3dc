/**
 * 数据处理工具函数
 * 支持JSON Schema格式的表格数据处理
 */

import logger from './logger';

/**
 * JSON Schema表格数据解析器
 */
export class JsonSchemaTableParser {
  /**
   * 解析JSON Schema格式的表格数据
   * @param {Object} jsonData - JSON Schema格式的数据
   * @returns {Object} 解析后的表格数据
   */
  static parseJsonSchemaTable(jsonData) {
    if (!jsonData || !jsonData.elements || !Array.isArray(jsonData.elements)) {
      return null;
    }

    // 处理所有elements，包括title和多个table
    const processedElements = [];
    let hasTable = false;

    jsonData.elements.forEach((element, index) => {
      if (element.type === 'title') {
        // 处理标题元素
        processedElements.push({
          type: 'title',
          content: element.content,
          level: element.level || 1
        });
      } else if (element.type === 'table' && element.rows) {
        // 处理表格元素
        processedElements.push({
          type: 'table',
          rows: element.rows,
          style: element.style || 'default'
        });
        hasTable = true;
      }
    });

    if (!hasTable) {
      return null;
    }

    return {
      type: 'json_schema',
      elements: processedElements,
      structure: this.analyzeCompleteStructure(processedElements),
      content: this.extractCompleteContent(processedElements)
    };
  }

  /**
   * 分析完整结构（包括所有elements）
   * @param {Array} elements - 所有元素
   * @returns {Object} 完整结构信息
   */
  static analyzeCompleteStructure(elements) {
    const structure = {
      totalElements: elements.length,
      titles: [],
      tables: [],
      totalRows: 0,
      maxCols: 0,
      mergedCells: [],
      complexCells: []
    };

    elements.forEach((element, elementIndex) => {
      if (element.type === 'title') {
        structure.titles.push({
          index: elementIndex,
          content: element.content,
          level: element.level
        });
      } else if (element.type === 'table') {
        const tableStructure = this.analyzeTableStructure(element.rows);
        structure.tables.push({
          index: elementIndex,
          ...tableStructure
        });
        structure.totalRows += tableStructure.rows;
        structure.maxCols = Math.max(structure.maxCols, tableStructure.cols);
        
        // 调整合并单元格和复杂单元格的位置索引
        tableStructure.mergedCells.forEach(cell => {
          structure.mergedCells.push({
            ...cell,
            tableIndex: elementIndex
          });
        });
        
        tableStructure.complexCells.forEach(cell => {
          structure.complexCells.push({
            ...cell,
            tableIndex: elementIndex
          });
        });
      }
    });

    return structure;
  }

  /**
   * 提取完整内容（所有elements的内容）
   * @param {Array} elements - 所有元素
   * @returns {Array} 完整内容数组
   */
  static extractCompleteContent(elements) {
    const content = [];

    elements.forEach(element => {
      if (element.type === 'title') {
        content.push({
          type: 'title',
          content: element.content,
          level: element.level
        });
      } else if (element.type === 'table') {
        content.push({
          type: 'table',
          content: this.extractTableContent(element.rows)
        });
      }
    });

    return content;
  }

  /**
   * 分析表格结构
   * @param {Array} rows - 表格行数据
   * @returns {Object} 表格结构信息
   */
  static analyzeTableStructure(rows) {
    if (!rows || rows.length === 0) {
      return { rows: 0, cols: 0, mergedCells: [], complexCells: [] };
    }

    let maxCols = 0;
    const mergedCells = [];
    const complexCells = [];

    rows.forEach((row, rowIndex) => {
      if (Array.isArray(row)) {
        let colCount = 0;
        row.forEach((cell, colIndex) => {
          const colspan = cell.colspan || 1;
          const rowspan = cell.rowspan || 1;
          colCount += colspan;

          // 记录合并单元格
          if (colspan > 1 || rowspan > 1) {
            mergedCells.push({
              row: rowIndex,
              col: colIndex,
              colspan,
              rowspan
            });
          }

          // 记录复杂内容单元格
          if (typeof cell.content === 'object') {
            complexCells.push({
              row: rowIndex,
              col: colIndex,
              type: cell.content.type || 'unknown',
              content: cell.content
            });
          }
        });
        maxCols = Math.max(maxCols, colCount);
      }
    });

    return {
      rows: rows.length,
      cols: maxCols,
      mergedCells,
      complexCells
    };
  }

  /**
   * 提取表格内容为二维数组
   * @param {Array} rows - 表格行数据
   * @returns {Array} 二维数组格式的表格内容
   */
  static extractTableContent(rows) {
    if (!rows || rows.length === 0) {
      return [];
    }

    return rows.map(row => {
      if (!Array.isArray(row)) return [];

      return row.map(cell => {
        return this.extractCellContent(cell.content);
      });
    });
  }

  /**
   * 提取单元格内容
   * @param {*} content - 单元格内容
   * @returns {string} 提取的文本内容
   */
  static extractCellContent(content) {
    if (typeof content === 'string') {
      return content;
    }

    if (typeof content === 'object' && content !== null) {
      switch (content.type) {
        case 'checkbox':
          return this.extractCheckboxContent(content);
        case 'list':
          return this.extractListContent(content);
        default:
          return JSON.stringify(content);
      }
    }

    return String(content || '');
  }

  /**
   * 提取复选框内容
   * @param {Object} checkboxData - 复选框数据
   * @returns {string} 格式化的复选框内容
   */
  static extractCheckboxContent(checkboxData) {
    if (!checkboxData.options || !Array.isArray(checkboxData.options)) {
      return '';
    }

    const checkedItems = checkboxData.options
      .filter(option => option.checked)
      .map(option => option.option);

    const uncheckedItems = checkboxData.options
      .filter(option => !option.checked)
      .map(option => option.option);

    // 返回格式化的字符串，便于比较
    return `${checkboxData.key || ''}:checked[${checkedItems.join(',')}];unchecked[${uncheckedItems.join(',')}]`;
  }

  /**
   * 提取列表内容
   * @param {Object} listData - 列表数据
   * @returns {string} 格式化的列表内容
   */
  static extractListContent(listData) {
    if (!listData.items || !Array.isArray(listData.items)) {
      return '';
    }

    return listData.items.join(';');
  }
}

/**
 * 将Markdown表格转换为HTML
 */
export const convertMarkdownTableToHTML = (markdownText) => {
  if (!markdownText || !markdownText.trim()) {
    return "<div style='color:gray;'>无表格内容</div>";
  }

  try {
    const lines = markdownText.trim().split('\n');
    const tableLines = [];
    let inTable = false;

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.startsWith('|') && trimmedLine.endsWith('|')) {
        // 跳过分隔行（如 | --- | --- |）
        if (!trimmedLine.replace(/\|/g, '').replace(/-/g, '').replace(/ /g, '')) {
          continue;
        }
        tableLines.push(trimmedLine);
        inTable = true;
      } else if (inTable) {
        // 如果已经在表格中但当前行不是表格行，表格结束
        break;
      }
    }

    if (!tableLines.length) {
      return "<div style='color:gray;'>无有效表格内容</div>";
    }

    // 解析所有行的单元格，找出最大列数
    const allRows = [];
    let maxCols = 0;

    for (const line of tableLines) {
      // 移除首尾的|，然后按|分割
      const cells = line.slice(1, -1).split('|').map(cell => cell.trim());
      allRows.push(cells);
      maxCols = Math.max(maxCols, cells.length);
    }

    // 补齐所有行到相同列数
    allRows.forEach(row => {
      while (row.length < maxCols) {
        row.push('');
      }
    });

    // 构建HTML表格
    const html = ["<table border='1' style='border-collapse: collapse; width: 100%;'>"];

    allRows.forEach((cells, i) => {
      if (i === 0) {
        // 第一行作为表头
        html.push("<thead><tr>");
        cells.forEach(cell => {
          html.push(`<th style='border: 1px solid #ddd; padding: 8px; background-color: #f2f2f2;'>${cell}</th>`);
        });
        html.push("</tr></thead><tbody>");
      } else {
        // 其他行作为数据行
        html.push("<tr>");
        cells.forEach(cell => {
          html.push(`<td style='border: 1px solid #ddd; padding: 8px;'>${cell}</td>`);
        });
        html.push("</tr>");
      }
    });

    html.push("</tbody></table>");
    return html.join('');
  } catch (error) {
    return `<div style='color:red;'>表格解析失败: ${error.message}</div>`;
  }
};

/**
 * 提取文件名称（不含扩展名）- 支持图片和PDF文件
 */
export const getImageBaseName = (fileName) => {
  return fileName.replace(/\.(png|jpg|jpeg|gif|webp|pdf)$/i, '');
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化时间戳
 */
export const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  
  // 如果是字符串格式的时间戳（如 "2025_06_20_16_54_56"）
  if (typeof timestamp === 'string' && timestamp.includes('_')) {
    const parts = timestamp.split('_');
    if (parts.length >= 6) {
      const [year, month, day, hour, minute, second] = parts;
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
  }
  
  // 如果是数字时间戳
  if (typeof timestamp === 'number') {
    return new Date(timestamp * 1000).toLocaleString();
  }
  
  return timestamp;
};

/**
 * 计算准确率（支持JSON Schema数据）
 */
export const calculateAccuracy = (expected, actual) => {
  if (!expected || !actual) return 0;

  // 检查是否为解析失败的结果
  const actualStr = String(actual);
  if (actualStr.includes('MonkeyOCR处理失败') ||
      actualStr.includes('MonkeyOCR文件上传失败') ||
      actualStr.includes('MonkeyOCR处理超时') ||
      actualStr.includes('MonkeyOCR获取结果失败') ||
      actualStr.includes('MonkeyOCR处理请求提交失败') ||
      actualStr.includes('VL LLM处理失败') ||
      actualStr.includes('KDC处理失败')) {
    return 0;
  }

  // 检查是否为JSON Schema格式的数据
  if (typeof expected === 'object' && expected.type === 'json_schema') {
    return calculateJsonSchemaAccuracy(expected, actual);
  }

  try {
    // 预处理：检测并处理重复内容
    const cleanedActual = preprocessContent(actualStr);

    // 尝试解析表格内容进行比较
    const expectedTable = parseTableContent(String(expected));
    const actualTable = parseTableContent(cleanedActual);

    logger.debug('准确率计算调试:', {
      hasExpectedTable: !!expectedTable,
      hasActualTable: !!actualTable,
      expectedRows: expectedTable ? expectedTable.length : 0,
      actualRows: actualTable ? actualTable.length : 0,
      originalContentLength: actualStr.length,
      cleanedContentLength: cleanedActual.length,
      hasMultipleTables: actualStr.split('<table').length > 2,
      wasContentCleaned: actualStr !== cleanedActual,
      expectedSample: expectedTable ? expectedTable.slice(0, 3) : null,
      actualSample: actualTable ? actualTable.slice(0, 3) : null
    });

    if (expectedTable && actualTable) {
      const accuracy = compareTableData(expectedTable, actualTable);
      logger.debug('表格比较准确率:', accuracy);
      return accuracy;
    } else {
      // 如果无法解析为表格，使用简单字符串比较
      const accuracy = calculateAccuracySimple(expected, cleanedActual);
      logger.debug('简单字符串比较准确率:', accuracy);
      return accuracy;
    }
  } catch (error) {
    logger.warn('表格解析失败，使用简单算法:', error);
    return calculateAccuracySimple(expected, actual);
  }
};

/**
 * 预处理内容，移除重复的表格或文本
 */
const preprocessContent = (content) => {
  if (!content || typeof content !== 'string') return content;

  // 处理HTML表格重复
  if (content.includes('<table')) {
    const tableMatches = content.match(/<table[^>]*>.*?<\/table>/gs);
    if (tableMatches && tableMatches.length > 1) {
      const firstTable = tableMatches[0];
      const firstTableNormalized = normalizeTableContent(firstTable);

      // 检查是否所有表格都是重复的
      const allDuplicate = tableMatches.slice(1).every(table =>
        normalizeTableContent(table) === firstTableNormalized
      );

      if (allDuplicate) {
        // 如果所有表格都是重复的，只保留第一个
        return firstTable;
      }
    }
  }

  // 处理其他类型的重复内容（如重复的文本块）
  // 这里可以根据需要添加更多的重复检测逻辑

  return content;
};

/**
 * 统一的准确率计算工具函数
 * 确保所有组件使用相同的准确率计算逻辑
 * 只使用人工标注数据作为基准进行准确率计算
 */
export const calculateAccuracyForCase = (caseData, annotationData) => {
  logger.info('[calculateAccuracyForCase] 开始计算准确率', {
    hasCaseData: !!caseData,
    hasAnnotationData: !!annotationData,
    annotationDataType: typeof annotationData,
    annotationDataKeys: annotationData ? Object.keys(annotationData) : 'null'
  });

  // 获取人工标注数据作为准确率计算基准
  const getBaselineData = () => {
    // 只使用人工标注数据作为基准
    if (!annotationData) {
      logger.warn('没有人工标注数据，无法计算准确率');
      return null;
    }

    // 检查是否直接是JSON Schema格式（complex数据集的情况）
    if (annotationData.elements) {
      logger.info('发现直接的JSON Schema格式标注数据');
      const parsedJsonSchema = JsonSchemaTableParser.parseJsonSchemaTable(annotationData);
      if (parsedJsonSchema) {
        logger.info('成功解析直接JSON Schema人工标注数据');
        return parsedJsonSchema;
      }
    }

    // 检查是否有table_content字段
    if (annotationData.table_content) {
      logger.info('发现table_content字段，尝试解析', {
        contentLength: annotationData.table_content.length,
        contentSample: annotationData.table_content.substring(0, 200) + '...'
      });

      // 尝试解析为JSON Schema格式
      try {
        const jsonData = JSON.parse(annotationData.table_content);
        logger.info('table_content JSON解析成功', {
          hasElements: !!jsonData.elements,
          elementsCount: jsonData.elements ? jsonData.elements.length : 0
        });

        // 检查是否为JSON Schema格式的数据
        if (jsonData && typeof jsonData === 'object' && jsonData.elements) {
          const parsedJsonSchema = JsonSchemaTableParser.parseJsonSchemaTable(jsonData);
          if (parsedJsonSchema) {
            logger.info('成功解析JSON Schema人工标注数据（从table_content）', {
              hasContent: !!parsedJsonSchema.content,
              contentType: typeof parsedJsonSchema.content,
              contentLength: Array.isArray(parsedJsonSchema.content) ? parsedJsonSchema.content.length : 'not array'
            });
            return parsedJsonSchema;
          } else {
            logger.warn('JsonSchemaTableParser.parseJsonSchemaTable返回null');
          }
        } else {
          logger.warn('解析的JSON数据不是有效的JSON Schema格式', {
            hasElements: !!jsonData.elements,
            jsonDataType: typeof jsonData,
            jsonDataKeys: jsonData ? Object.keys(jsonData) : 'null'
          });
        }
      } catch (error) {
        logger.warn('标注数据不是JSON格式，使用原有逻辑:', error.message);
      }

      // 如果不是JSON Schema格式，直接使用标注内容
      logger.info('使用原始table_content作为基准数据');
      return annotationData.table_content;
    }

    logger.warn('标注数据格式无法识别', {
      hasElements: !!annotationData.elements,
      hasTableContent: !!annotationData.table_content,
      annotationDataKeys: Object.keys(annotationData)
    });
    return null;
  };

  const baselineData = getBaselineData();

  logger.info('[calculateAccuracyForCase] 基准数据获取结果', {
    hasBaselineData: !!baselineData,
    baselineDataType: typeof baselineData,
    baselineDataLength: typeof baselineData === 'string' ? baselineData.length :
                       Array.isArray(baselineData) ? baselineData.length : 'not string/array',
    baselineDataSample: typeof baselineData === 'string' ? baselineData.substring(0, 200) + '...' :
                       Array.isArray(baselineData) ? baselineData.slice(0, 2) : baselineData
  });

  // 兼容性处理：如果baselineData是字符串，保持原有逻辑
  const baselineText = !baselineData ? '' :
    (typeof baselineData === 'string' ? baselineData :
    (baselineData.type === 'json_schema' ? 'JSON Schema Data' : String(baselineData)));

  // 提取各解析器的文本
  const kdcMarkdownText = caseData.kdcMarkdown?.result?.data?.[0]?.markdown || '';
  const kdcPlainText = caseData.kdcPlain?.result?.data?.[0]?.plain || '';
  const kdcKdcText = caseData.kdcKdc?.result?.data?.[0]?.kdc || '';

  // MonkeyOCR (table)
  let monkeyOCRText = '';
  if (caseData.monkeyOCR?.result?.html) {
    monkeyOCRText = caseData.monkeyOCR.result.html;
  } else if (caseData.monkeyOCR?.html) {
    monkeyOCRText = caseData.monkeyOCR.html;
  }

  // MonkeyOCR (parse) - 支持多种数据结构
  let monkeyOCRV2Text = '';
  if (caseData.monkeyOCRV2?.result?.html) {
    monkeyOCRV2Text = caseData.monkeyOCRV2.result.html;
  } else if (caseData.monkeyOCRV2?.html) {
    monkeyOCRV2Text = caseData.monkeyOCRV2.html;
  } else if (caseData.monkey_ocr?.result?.html) {
    // complex数据集中MonkeyOCR(parse)在monkey_ocr键下
    monkeyOCRV2Text = caseData.monkey_ocr.result.html;
  } else if (caseData.monkey_ocr?.html) {
    monkeyOCRV2Text = caseData.monkey_ocr.html;
  }

  // VL LLM - 彻底修复文本提取逻辑
  let vlLLMText = '';

  // 如果没有vlLLMResult，尝试从其他字段获取
  if (!caseData.vlLLMResult && caseData.vlLLM) {
    caseData.vlLLMResult = caseData.vlLLM;
  }

  if (caseData.vlLLMResult) {
    // 尝试所有可能的路径
    const possiblePaths = [
      () => caseData.vlLLMResult.result?.content?.choices?.[0]?.message?.content,
      () => caseData.vlLLMResult.result?.choices?.[0]?.message?.content,
      () => caseData.vlLLMResult.content?.choices?.[0]?.message?.content,
      () => caseData.vlLLMResult.choices?.[0]?.message?.content,
      () => caseData.vlLLMResult.result?.content,
      () => caseData.vlLLMResult.content,
      () => caseData.vlLLMResult.text,
      () => caseData.vlLLMResult.markdown,
      () => typeof caseData.vlLLMResult === 'string' ? caseData.vlLLMResult : null
    ];

    for (const getPath of possiblePaths) {
      try {
        const text = getPath();
        if (text && typeof text === 'string' && text.trim()) {
          vlLLMText = text;
          break;
        }
      } catch (e) {
        // 忽略错误，继续尝试下一个路径
      }
    }

    logger.error('[calculateMetricsForCase] VL LLM最终提取结果:', {
      textLength: vlLLMText.length,
      textSample: vlLLMText.substring(0, 100) + '...',
      hasText: !!vlLLMText
    });
  } else {
    logger.error('[calculateMetricsForCase] VL LLM数据完全不存在');
  }

  // MonkeyOCR Local
  let monkeyOCRLocalText = '';
  if (caseData.monkeyOCRLocal?.results?.[0]?.result) {
    const result = caseData.monkeyOCRLocal.results[0].result;
    monkeyOCRLocalText = result.html || result.markdown || '';
  } else if (caseData.monkeyOCRLocal?.result) {
    monkeyOCRLocalText = caseData.monkeyOCRLocal.result.html || caseData.monkeyOCRLocal.result.markdown || '';
  }

  // MonkeyOCR（kas）
  let monkeyOcrKasText = '';
  if (caseData.monkeyOcrKas?.result?.content) {
    monkeyOcrKasText = caseData.monkeyOcrKas.result.content;
  } else if (caseData.monkeyOcrKas?.content) {
    monkeyOcrKasText = caseData.monkeyOcrKas.content;
  }

  // OCR Flux
  let ocrfluxText = '';
  if (caseData.ocrflux?.result?.content) {
    ocrfluxText = caseData.ocrflux.result.content;
  } else if (caseData.ocrflux?.content) {
    ocrfluxText = caseData.ocrflux.content;
  }

  // 使用统一的准确率计算方法 - 基于单元格位置比对
  const calculateAccuracyWithTableComparison = (actualText) => {
    logger.debug('[calculateAccuracyWithTableComparison] Starting accuracy calculation');
    logger.debug('[calculateAccuracyWithTableComparison] actualText length:', actualText ? actualText.length : 0);

    if (!actualText) {
      logger.debug('[calculateAccuracyWithTableComparison] No actual text, returning null');
      return null; // 没有解析结果，返回null
    }

    if (!baselineData) {
      // 没有人工标注数据，无法计算准确率
      logger.debug('[calculateAccuracyWithTableComparison] No baseline data, returning null');
      return null;
    }

    logger.debug('[calculateAccuracyWithTableComparison] baselineData type:', typeof baselineData);
    
    try {
      // 解析基准表格
      let baselineTable = null;
      if (typeof baselineData === 'object' && baselineData !== null) {
        logger.debug('[calculateAccuracyWithTableComparison] ==== Parsing baseline as object');
        baselineTable = parseTableContent(baselineData);
      } else {
        logger.debug('[calculateAccuracyWithTableComparison] ==== Parsing baseline as string');
        baselineTable = parseTableContent(String(baselineData));
      }
      
      logger.debug('[calculateAccuracyWithTableComparison] ==== baselineTable result:', baselineTable);
      logger.debug('[calculateAccuracyWithTableComparison] ==== baselineTable type:', typeof baselineTable);
      logger.debug('[calculateAccuracyWithTableComparison] ==== baselineTable length:', baselineTable ? baselineTable.length : 'null');
      
      // 解析实际表格
      logger.debug('[calculateAccuracyWithTableComparison] ==== Parsing actual text as table');
      const actualTable = parseTableContent(String(actualText));
      
      logger.debug('[calculateAccuracyWithTableComparison] ==== actualTable result:', actualTable);
      logger.debug('[calculateAccuracyWithTableComparison] ==== actualTable type:', typeof actualTable);
      logger.debug('[calculateAccuracyWithTableComparison] ==== actualTable length:', actualTable ? actualTable.length : 'null');
      
      if (baselineTable && actualTable) {
        logger.debug('[calculateAccuracyWithTableComparison] ==== Both tables exist, comparing...');
        const comparisonResult = getCompleteTableComparison(baselineTable, actualTable);
        logger.debug('[calculateAccuracyWithTableComparison] ==== Comparison result:', comparisonResult);
        return comparisonResult.accuracy;
      } else {
        logger.debug('[calculateAccuracyWithTableComparison] ==== Missing tables, falling back to legacy algorithm');
        logger.debug('[calculateAccuracyWithTableComparison] ==== baselineTable exists:', !!baselineTable);
        logger.debug('[calculateAccuracyWithTableComparison] ==== actualTable exists:', !!actualTable);
      }
    } catch (error) {
      logger.warn('[calculateAccuracyWithTableComparison] 表格比对准确率计算失败:', error);
    }
    
    // 回退到简单的字符串比较
    logger.debug('[calculateAccuracyWithTableComparison] ==== Falling back to simple string comparison');
    const simpleResult = calculateAccuracySimple(baselineData, actualText);
    logger.debug('[calculateAccuracyWithTableComparison] ==== Simple result:', simpleResult);
    return simpleResult;
  };

  // 计算各解析器的准确率
  const accuracies = {
    kdcMarkdown: calculateAccuracyWithTableComparison(kdcMarkdownText),
    kdcPlain: calculateAccuracyWithTableComparison(kdcPlainText),
    kdcKdc: calculateAccuracyWithTableComparison(kdcKdcText),
    monkeyOCR: calculateAccuracyWithTableComparison(monkeyOCRText),
    monkeyOCRV2: calculateAccuracyWithTableComparison(monkeyOCRV2Text),
    vlLLM: calculateAccuracyWithTableComparison(vlLLMText),
    monkeyOCRLocal: calculateAccuracyWithTableComparison(monkeyOCRLocalText),
    monkeyOcrKas: calculateAccuracyWithTableComparison(monkeyOcrKasText),
    ocrflux: calculateAccuracyWithTableComparison(ocrfluxText)
  };

  const result = {
    baselineData,
    baselineText,
    accuracies: accuracies || {},
    texts: {
      kdcMarkdownText: kdcMarkdownText || '',
      kdcPlainText: kdcPlainText || '',
      kdcKdcText: kdcKdcText || '',
      monkeyOCRText: monkeyOCRText || '',
      monkeyOCRV2Text: monkeyOCRV2Text || '',
      vlLLMText: vlLLMText || '',
      monkeyOCRLocalText: monkeyOCRLocalText || '',
      monkeyOcrKasText: monkeyOcrKasText || '',
      ocrfluxText: ocrfluxText || ''
    }
  };

  logger.error('[calculateAccuracyForCase] VL LLM文本提取调试', {
    vlLLMTextLength: vlLLMText.length,
    vlLLMTextSample: vlLLMText.substring(0, 100),
    hasVlLLMResult: !!caseData.vlLLMResult,
    vlLLMResultStructure: caseData.vlLLMResult ? Object.keys(caseData.vlLLMResult) : 'null'
  });

  logger.info('[calculateAccuracyForCase] 最终返回结果', {
    hasBaselineData: !!result.baselineData,
    hasBaselineText: !!result.baselineText,
    accuraciesKeys: Object.keys(result.accuracies),
    textsKeys: Object.keys(result.texts),
    accuraciesValues: result.accuracies,
    textsLengths: Object.fromEntries(
      Object.entries(result.texts).map(([key, value]) => [key, value ? value.length : 0])
    )
  });

  return result;
};

/**
 * 解析表格内容为二维数组
 * 支持HTML、Markdown和JSON Schema格式
 */
export const parseTableContent = (content) => {
  try {
    logger.error('[parseTableContent] 开始解析，输入类型:', typeof content);
    logger.error('[parseTableContent] 输入内容示例:', content ? String(content).substring(0, 200) + '...' : 'null');
    
    // 检查是否为JSON Schema格式的对象
    if (typeof content === 'object' && content !== null) {
      if (content.type === 'json_schema' && content.content) {
        // 已经是解析后的JSON Schema格式，需要进一步处理content数组
        logger.debug('[parseTableContent] Processing parsed JSON Schema:', content);

        // content是一个数组，包含表格对象
        if (Array.isArray(content.content)) {
          for (const item of content.content) {
            logger.debug('[parseTableContent] Processing content item:', item);
            if (item.type === 'table') {
              // 检查是否有content属性（这是extractCompleteContent生成的结构）
              if (item.content && Array.isArray(item.content)) {
                logger.debug('[parseTableContent] Found table content array:', item.content);
                return item.content;
              }
              // 检查是否有rows属性（原始JSON Schema结构）
              else if (item.rows) {
                logger.debug('[parseTableContent] Found table rows:', item.rows);
                return extractTableContentFromRows(item.rows);
              }
            }
          }
        }
        
        // 如果没有找到table类型的content，检查是否整个content就是表格数据
        if (Array.isArray(content.content) && content.content.length > 0) {
          // 检查第一个元素是否是数组（表格行）
          const firstItem = content.content[0];
          if (Array.isArray(firstItem)) {
            logger.debug('[parseTableContent] Content appears to be table rows directly');
            return content.content;
          }
        }
        
        logger.debug('[parseTableContent] No table found in JSON Schema content');
        return null;
      } else if (content.elements) {
        // 原始JSON Schema格式，需要解析
        logger.debug('[parseTableContent] Parsing raw JSON Schema with elements');
        const parsed = JsonSchemaTableParser.parseJsonSchemaTable(content);
        if (parsed && parsed.content) {
          logger.debug('[parseTableContent] Parsed JSON Schema result:', parsed);
          // 递归调用以处理解析后的结果
          return parseTableContent(parsed);
        }
        return null;
      } else if (content.rows) {
        // 直接的表格行数据
        logger.debug('[parseTableContent] Direct table rows data');
        return extractTableContentFromRows(content.rows);
      }
    }

    // 如果是字符串，尝试解析为JSON Schema
    if (typeof content === 'string') {
      try {
        const jsonData = JSON.parse(content);
        if (jsonData && typeof jsonData === 'object') {
          logger.debug('[parseTableContent] Parsed JSON from string:', jsonData);
          if (jsonData.elements) {
            const parsed = JsonSchemaTableParser.parseJsonSchemaTable(jsonData);
            if (parsed && parsed.content) {
              logger.debug('[parseTableContent] Parsed JSON Schema from string:', parsed);
              return parseTableContent(parsed);
            }
            return null;
          } else if (jsonData.rows) {
            return extractTableContentFromRows(jsonData.rows);
          }
        }
      } catch (e) {
        // 不是JSON格式，继续尝试其他格式
        logger.debug('[parseTableContent] String is not JSON, trying other formats');
      }
    }

    // 对于包含重复表格的内容，只取第一个表格
    let cleanContent = String(content);
    logger.debug('[parseTableContent] 检查HTML表格，内容长度:', cleanContent.length);
    logger.debug('[parseTableContent] 内容是否包含<table>:', cleanContent.includes('<table'));

    if (cleanContent.includes('<table')) {
      // 如果包含多个表格，检查是否是重复的
      const tableMatches = cleanContent.match(/<table[^>]*>.*?<\/table>/gs);
      logger.debug('[parseTableContent] 找到表格数量:', tableMatches ? tableMatches.length : 0);

      if (tableMatches && tableMatches.length > 1) {
        logger.debug(`发现 ${tableMatches.length} 个表格，检查是否重复...`);

        // 检查是否是重复的表格（更严格的比较）
        const firstTable = tableMatches[0];
        const firstTableNormalized = normalizeTableContent(firstTable);

        let duplicateCount = 0;
        const isDuplicate = tableMatches.slice(1).every(table => {
          const tableNormalized = normalizeTableContent(table);
          const isMatch = tableNormalized === firstTableNormalized;
          if (isMatch) duplicateCount++;
          return isMatch;
        });

        if (isDuplicate && duplicateCount > 0) {
          logger.debug(`检测到 ${duplicateCount + 1} 个重复表格，使用第一个表格进行解析`);
          cleanContent = firstTable;
        } else {
          logger.debug('表格内容不同，保留所有表格');
          // 如果表格内容不同，可能需要合并或选择最完整的表格
          // 这里选择最长的表格作为主要内容
          const longestTable = tableMatches.reduce((longest, current) =>
            current.length > longest.length ? current : longest
          );
          cleanContent = longestTable;
        }
      } else if (tableMatches && tableMatches.length === 1) {
        logger.debug('[parseTableContent] 找到单个表格，直接使用');
        cleanContent = tableMatches[0];
      }

      logger.debug('[parseTableContent] 准备解析的HTML表格内容:', cleanContent.substring(0, 300) + '...');
      const result = parseHTMLTable(cleanContent);
      logger.debug('[parseTableContent] HTML表格解析结果:', result);
      return result;
    }

    // 尝试解析Markdown表格
    if (cleanContent.includes('|')) {
      logger.error('[parseTableContent] 检测到Markdown表格，开始解析');
      const result = parseMarkdownTable(cleanContent);
      logger.error('[parseTableContent] Markdown表格解析结果:', result);
      return result;
    }

    logger.error('[parseTableContent] 未识别的表格格式:', {
      contentLength: cleanContent.length,
      contentSample: cleanContent.substring(0, 300),
      hasTable: cleanContent.includes('<table'),
      hasPipe: cleanContent.includes('|'),
      hasElements: cleanContent.includes('elements')
    });
    return null;
  } catch (error) {
    logger.warn('解析表格内容失败:', error);
    return null;
  }
};

/**
 * 从JSON Schema行数据中提取表格内容
 */
const extractTableContentFromRows = (rows) => {
  if (!rows || !Array.isArray(rows)) return null;

  return rows.map(row => {
    if (!Array.isArray(row)) return [];

    return row.map(cell => {
      if (typeof cell === 'string') {
        return cell;
      } else if (typeof cell === 'object' && cell !== null) {
        if (cell.content) {
          if (typeof cell.content === 'string') {
            return cell.content;
          } else if (typeof cell.content === 'object') {
            // 处理复杂内容（如checkbox、list等）
            return extractComplexCellContent(cell.content);
          }
        }
        return String(cell);
      }
      return String(cell || '');
    });
  });
};

/**
 * 提取复杂单元格内容
 */
const extractComplexCellContent = (content) => {
  if (!content || typeof content !== 'object') return '';

  switch (content.type) {
    case 'checkbox':
      if (content.options && Array.isArray(content.options)) {
        return content.options
          .filter(option => option.checked)
          .map(option => option.option)
          .join(', ');
      }
      return '';
    case 'list':
      if (content.items && Array.isArray(content.items)) {
        return content.items.join(', ');
      }
      return '';
    default:
      return String(content);
  }
};

/**
 * 标准化表格内容用于比较
 */
const normalizeTableContent = (tableHtml) => {
  return tableHtml
    .replace(/\s+/g, ' ')  // 将多个空白字符替换为单个空格
    .replace(/>\s+</g, '><')  // 移除标签间的空白
    .replace(/\s+>/g, '>')  // 移除标签前的空白
    .replace(/<\s+/g, '<')  // 移除标签后的空白
    .toLowerCase()
    .trim();
};

/**
 * 解析HTML表格
 */
const parseHTMLTable = (htmlContent) => {
  logger.debug('[parseHTMLTable] 开始解析HTML表格');
  logger.debug('[parseHTMLTable] HTML内容长度:', htmlContent ? htmlContent.length : 0);
  logger.debug('[parseHTMLTable] HTML内容示例:', htmlContent ? String(htmlContent).substring(0, 200) + '...' : 'null');

  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlContent, 'text/html');
  const table = doc.querySelector('table');

  if (!table) {
    logger.debug('[parseHTMLTable] 未找到table元素');
    return null;
  }

  const rows = [];
  const trs = table.querySelectorAll('tr');
  logger.debug('[parseHTMLTable] 找到', trs.length, '行');

  trs.forEach((tr, rowIndex) => {
    const cells = [];
    const tds = tr.querySelectorAll('th, td');
    logger.debug('[parseHTMLTable] 第', rowIndex, '行有', tds.length, '个单元格');

    tds.forEach((td, cellIndex) => {
      const cellContent = td.textContent.trim();
      cells.push(cellContent);
      // 只在DEBUG级别记录单元格内容，避免过多日志
      if (rowIndex < 3 && cellIndex < 3) { // 只记录前3行3列
        logger.debug('[parseHTMLTable] 单元格[', rowIndex, ',', cellIndex, ']:', cellContent);
      }
    });

    if (cells.length > 0) {
      rows.push(cells);
    }
  });

  logger.debug('[parseHTMLTable] 解析完成，共', rows.length, '行数据');
  logger.debug('[parseHTMLTable] 解析结果:', rows);
  return rows;
};

/**
 * 解析Markdown表格
 */
const parseMarkdownTable = (markdownContent) => {
  const lines = markdownContent.split('\n').map(line => line.trim()).filter(line => line);
  const tableLines = lines.filter(line => line.startsWith('|') && line.endsWith('|'));

  if (tableLines.length < 2) return null;

  // 移除分隔符行
  const dataLines = tableLines.filter(line => !line.match(/^\|[\s\-|]+\|$/));

  const rows = [];
  dataLines.forEach(line => {
    const cells = line.split('|').slice(1, -1).map(cell => cell.trim());
    if (cells.length > 0) {
      rows.push(cells);
    }
  });

  return rows;
};

/**
 * 移除表格尾部的空列
 */
const removeTrailingEmptyColumns = (table) => {
  if (!table || table.length === 0) return table;

  // 找到最大列数
  const maxCols = Math.max(...table.map(row => row.length));

  // 从右到左检查每一列是否为空
  let lastNonEmptyCol = -1;
  for (let col = 0; col < maxCols; col++) {
    let hasContent = false;
    for (let row = 0; row < table.length; row++) {
      const cell = (table[row][col] || '').trim();
      if (cell !== '') {
        hasContent = true;
        break;
      }
    }
    if (hasContent) {
      lastNonEmptyCol = col;
    }
  }

  // 如果所有列都为空，返回原表格
  if (lastNonEmptyCol === -1) return table;

  // 移除尾部空列
  return table.map(row => row.slice(0, lastNonEmptyCol + 1));
};

/**
 * 比较两个表格数据 - 按单元格位置进行精确比较
 */
const compareTableData = (table1, table2) => {
  if (!table1 || !table2) return 0;
  if (table1.length === 0 && table2.length === 0) return 100;
  if (table1.length === 0 || table2.length === 0) return 0;

  logger.debug('表格结构比较调试:', {
    table1Rows: table1.length,
    table2Rows: table2.length,
    table1Cols: table1[0] ? table1[0].length : 0,
    table2Cols: table2[0] ? table2[0].length : 0,
    table1Sample: table1.slice(0, 3).map(row => row.slice(0, 3)),
    table2Sample: table2.slice(0, 3).map(row => row.slice(0, 3))
  });

  // 获取两个表格的最大行数和列数
  const maxRows = Math.max(table1.length, table2.length);
  const maxCols = Math.max(
    Math.max(...table1.map(row => row.length)),
    Math.max(...table2.map(row => row.length))
  );

  let totalCells = 0;
  let matchedCells = 0;

  // 按位置比较每个单元格
  for (let i = 0; i < maxRows; i++) {
    for (let j = 0; j < maxCols; j++) {
      // 获取对应位置的单元格内容
      const cell1 = (table1[i] && table1[i][j] !== undefined) ?
        String(table1[i][j]).trim().toLowerCase() : '';
      const cell2 = (table2[i] && table2[i][j] !== undefined) ?
        String(table2[i][j]).trim().toLowerCase() : '';

      totalCells++;

      // 比较单元格内容
      if (cell1 === cell2) {
        matchedCells++;
      }

      // 调试前几个单元格的比较
      if (i < 3 && j < 3) {
        logger.debug(`单元格[${i}][${j}]比较:`, {
          cell1: cell1 || '(空)',
          cell2: cell2 || '(空)',
          match: cell1 === cell2
        });
      }
    }
  }

  const accuracy = totalCells > 0 ? (matchedCells / totalCells) * 100 : 0;

  logger.debug('表格单元格比较结果:', {
    totalCells,
    matchedCells,
    accuracy: accuracy.toFixed(2),
    maxRows,
    maxCols
  });

  return accuracy;
};

/**
 * 计算未命中的单元格详情
 * 返回包含预期内容和实际内容的详细信息
 */
export const getDiffCells = (gt_table, pred_table) => {
  if (!gt_table || !pred_table) return [];

  // 创建位置映射，保存原始内容
  const gt_map = {};
  const gt_positions = {}; // 位置到内容的映射

  for (let i = 0; i < gt_table.length; i++) {
    for (let j = 0; j < gt_table[i].length; j++) {
      const originalCell = String(gt_table[i][j] || '');
      const normalizedCell = originalCell.toLowerCase().trim();
      const posKey = `${i}-${j}`;

      gt_positions[posKey] = originalCell;

      if (!gt_map[normalizedCell]) gt_map[normalizedCell] = [];
      gt_map[normalizedCell].push({ row: i, col: j, original: originalCell });
    }
  }

  // 创建预测表格的位置映射
  const pred_positions = {};
  for (let i = 0; i < pred_table.length; i++) {
    for (let j = 0; j < pred_table[i].length; j++) {
      const posKey = `${i}-${j}`;
      pred_positions[posKey] = String(pred_table[i][j] || '');
    }
  }

  // 计算计数器
  const gt_flat = gt_table.flat().map(cell => String(cell || '').toLowerCase().trim());
  const pred_flat = pred_table.flat().map(cell => String(cell || '').toLowerCase().trim());

  const gt_counter = {};
  const pred_counter = {};

  gt_flat.forEach(cell => {
    gt_counter[cell] = (gt_counter[cell] || 0) + 1;
  });

  pred_flat.forEach(cell => {
    pred_counter[cell] = (pred_counter[cell] || 0) + 1;
  });

  // 计算命中计数器
  const hit_counter = {};
  for (const cell in gt_counter) {
    if (pred_counter[cell]) {
      hit_counter[cell] = Math.min(gt_counter[cell], pred_counter[cell]);
    }
  }

  // 计算未命中的单元格
  const missed = [];
  for (const cell in gt_counter) {
    const missed_count = gt_counter[cell] - (hit_counter[cell] || 0);
    for (let i = 0; i < missed_count; i++) {
      if (gt_map[cell] && gt_map[cell].length > 0) {
        const pos = gt_map[cell].shift();
        const posKey = `${pos.row}-${pos.col}`;
        const actualContent = pred_positions[posKey] || '';

        missed.push({
          expectedContent: pos.original, // 预期内容（原始）
          actualContent: actualContent,  // 实际内容
          row: pos.row + 1, // 1-based
          col: pos.col + 1  // 1-based
        });
      }
    }
  }

  return missed;
};

/**
 * 获取完整的单元格比对结果
 * @param {Array} gt_table - 基准表格数据
 * @param {Array} pred_table - 预测表格数据
 * @returns {Object} 完整的比对结果，包括所有单元格的比对状态
 */
export const getCompleteTableComparison = (gt_table, pred_table) => {
  logger.error('[getCompleteTableComparison] 🔥 开始表格比对', {
    hasGtTable: !!gt_table,
    hasPredTable: !!pred_table,
    gtTableType: typeof gt_table,
    predTableType: typeof pred_table,
    gtTableRows: Array.isArray(gt_table) ? gt_table.length : 'not array',
    predTableRows: Array.isArray(pred_table) ? pred_table.length : 'not array'
  });

  logger.debug('[getCompleteTableComparison] ==== Input validation:');
  logger.debug('[getCompleteTableComparison] ==== gt_table type:', typeof gt_table);
  logger.debug('[getCompleteTableComparison] ==== gt_table:', gt_table);
  logger.debug('[getCompleteTableComparison] ==== pred_table type:', typeof pred_table);
  logger.debug('[getCompleteTableComparison] ==== pred_table:', pred_table);
  
  if (!gt_table || !pred_table) {
    logger.debug('[getCompleteTableComparison] ==== Early return: missing tables');
    return {
      cells: [],
      totalCells: 0,
      correctCells: 0,
      incorrectCells: 0,
      accuracy: 0
    };
  }

  if (!Array.isArray(gt_table) || !Array.isArray(pred_table)) {
    logger.debug('[getCompleteTableComparison] ==== Early return: not arrays');
    return {
      cells: [],
      totalCells: 0,
      correctCells: 0,
      incorrectCells: 0,
      accuracy: 0
    };
  }

  if (gt_table.length === 0 || pred_table.length === 0) {
    logger.debug('[getCompleteTableComparison] ==== Early return: empty arrays');
    return {
      cells: [],
      totalCells: 0,
      correctCells: 0,
      incorrectCells: 0,
      accuracy: 0
    };
  }

  const maxRows = Math.max(gt_table.length, pred_table.length);
  const maxCols = Math.max(
    Math.max(...gt_table.map(row => Array.isArray(row) ? row.length : 0)),
    Math.max(...pred_table.map(row => Array.isArray(row) ? row.length : 0))
  );

  logger.debug('[getCompleteTableComparison] ==== Dimensions:', {
    maxRows,
    maxCols,
    gt_table_length: gt_table.length,
    pred_table_length: pred_table.length,
    gt_table_sample: gt_table.slice(0, 2),
    pred_table_sample: pred_table.slice(0, 2)
  });

  const cells = [];
  let correctCount = 0;
  let totalCount = 0;

  for (let row = 0; row < maxRows; row++) {
    for (let col = 0; col < maxCols; col++) {
      const expectedContent = gt_table[row] && gt_table[row][col] !== undefined 
        ? String(gt_table[row][col] || '') 
        : '';
      const actualContent = pred_table[row] && pred_table[row][col] !== undefined 
        ? String(pred_table[row][col] || '') 
        : '';

      // 标准化内容进行比较
      const expectedNormalized = expectedContent.toLowerCase().trim();
      const actualNormalized = actualContent.toLowerCase().trim();
      
      const isCorrect = expectedNormalized === actualNormalized;
      
      if (expectedContent || actualContent) { // 只统计有内容的单元格
        totalCount++;
        if (isCorrect) {
          correctCount++;
        }
      }

      cells.push({
        row: row + 1, // 1-based
        col: col + 1, // 1-based
        expectedContent,
        actualContent,
        isCorrect,
        isEmpty: !expectedContent && !actualContent
      });
    }
  }

  const accuracy = totalCount > 0 ? (correctCount / totalCount) * 100 : 0;

  const result = {
    cells,
    totalCells: totalCount,
    correctCells: correctCount,
    incorrectCells: totalCount - correctCount,
    accuracy: Math.round(accuracy * 10) / 10, // 保留1位小数
    maxRows,
    maxCols
  };

  logger.debug('[getCompleteTableComparison] ==== Result:', result);
  return result;
};

/**
 * 计算JSON Schema格式数据的准确率
 * @param {Object} expectedJsonSchema - 期望的JSON Schema数据
 * @param {*} actual - 实际解析结果
 * @returns {number} 准确率百分比
 */
export const calculateJsonSchemaAccuracy = (expectedJsonSchema, actual) => {
  try {
    // 提取期望的表格内容
    const expectedContent = expectedJsonSchema.content;
    if (!expectedContent || expectedContent.length === 0) {
      return 0;
    }

    // 解析实际结果
    let actualTable = null;
    if (typeof actual === 'string') {
      // 尝试解析为表格
      actualTable = parseTableContent(actual);
    } else if (typeof actual === 'object' && actual.type === 'json_schema') {
      actualTable = actual.content;
    }

    if (!actualTable) {
      return 0;
    }

    // 使用改进的表格比较算法
    return compareJsonSchemaTableData(expectedContent, actualTable, expectedJsonSchema.structure);

  } catch (error) {
    logger.warn('JSON Schema准确率计算失败:', error);
    return 0;
  }
};

/**
 * 比较JSON Schema表格数据
 * @param {Array} expectedTable - 期望的表格数据
 * @param {Array} actualTable - 实际的表格数据
 * @param {Object} expectedStructure - 期望的表格结构
 * @returns {number} 准确率百分比
 */
const compareJsonSchemaTableData = (expectedTable, actualTable, expectedStructure) => {
  if (!expectedTable || !actualTable) return 0;
  if (expectedTable.length === 0 && actualTable.length === 0) return 100;
  if (expectedTable.length === 0 || actualTable.length === 0) return 0;

  // 结构准确率权重
  const structureWeight = 0.3;
  const contentWeight = 0.7;

  // 计算结构准确率
  const structureAccuracy = calculateStructureAccuracy(expectedStructure, actualTable);

  // 计算内容准确率
  const contentAccuracy = calculateContentAccuracy(expectedTable, actualTable);

  // 综合准确率
  const overallAccuracy = structureAccuracy * structureWeight + contentAccuracy * contentWeight;

  logger.debug('JSON Schema准确率计算:', {
    structureAccuracy: structureAccuracy.toFixed(2),
    contentAccuracy: contentAccuracy.toFixed(2),
    overallAccuracy: overallAccuracy.toFixed(2)
  });

  return Math.round(overallAccuracy);
};

/**
 * 计算表格结构准确率
 * @param {Object} expectedStructure - 期望的表格结构
 * @param {Array} actualTable - 实际的表格数据
 * @returns {number} 结构准确率百分比
 */
const calculateStructureAccuracy = (expectedStructure, actualTable) => {
  if (!expectedStructure || !actualTable) return 0;

  let score = 0;
  let totalChecks = 0;

  // 检查行数
  totalChecks++;
  if (expectedStructure.rows === actualTable.length) {
    score++;
  }

  // 检查列数
  totalChecks++;
  const actualMaxCols = Math.max(...actualTable.map(row => row.length));
  if (expectedStructure.cols === actualMaxCols) {
    score++;
  }

  // 检查合并单元格（如果有的话）
  if (expectedStructure.mergedCells && expectedStructure.mergedCells.length > 0) {
    totalChecks++;
    // 这里可以添加更复杂的合并单元格检查逻辑
    // 暂时给予部分分数
    score += 0.5;
  }

  return totalChecks > 0 ? (score / totalChecks) * 100 : 0;
};

/**
 * 计算表格内容准确率
 * @param {Array} expectedTable - 期望的表格内容
 * @param {Array} actualTable - 实际的表格内容
 * @returns {number} 内容准确率百分比
 */
const calculateContentAccuracy = (expectedTable, actualTable) => {
  // 将表格数据扁平化为单元格数组
  const expectedCells = expectedTable.flat().map(cell =>
    String(cell || '').toLowerCase().trim()
  ).filter(cell => cell.length > 0);

  const actualCells = actualTable.flat().map(cell =>
    String(cell || '').toLowerCase().trim()
  ).filter(cell => cell.length > 0);

  if (expectedCells.length === 0 && actualCells.length === 0) return 100;
  if (expectedCells.length === 0 || actualCells.length === 0) return 0;

  // 使用改进的匹配算法
  return calculateCellMatchingAccuracy(expectedCells, actualCells);
};

/**
 * 计算单元格匹配准确率
 * @param {Array} expectedCells - 期望的单元格数组
 * @param {Array} actualCells - 实际的单元格数组
 * @returns {number} 匹配准确率百分比
 */
const calculateCellMatchingAccuracy = (expectedCells, actualCells) => {
  let totalScore = 0;
  let maxPossibleScore = expectedCells.length;

  expectedCells.forEach(expectedCell => {
    let bestMatch = 0;

    actualCells.forEach(actualCell => {
      // 计算单元格相似度
      const similarity = calculateCellSimilarity(expectedCell, actualCell);
      bestMatch = Math.max(bestMatch, similarity);
    });

    totalScore += bestMatch;
  });

  return maxPossibleScore > 0 ? (totalScore / maxPossibleScore) * 100 : 0;
};

/**
 * 计算单元格相似度
 * @param {string} cell1 - 单元格1
 * @param {string} cell2 - 单元格2
 * @returns {number} 相似度分数 (0-1)
 */
const calculateCellSimilarity = (cell1, cell2) => {
  if (cell1 === cell2) return 1.0;

  // 处理数字的相似性
  const num1 = parseFloat(cell1);
  const num2 = parseFloat(cell2);
  if (!isNaN(num1) && !isNaN(num2)) {
    if (num1 === num2) return 1.0;
    // 数字相近度计算
    const diff = Math.abs(num1 - num2);
    const avg = (Math.abs(num1) + Math.abs(num2)) / 2;
    if (avg === 0) return diff === 0 ? 1.0 : 0.0;
    return Math.max(0, 1 - (diff / avg));
  }

  // 文本相似度计算
  return calculateTextSimilarity(cell1, cell2);
};

/**
 * 计算文本相似度
 * @param {string} text1 - 文本1
 * @param {string} text2 - 文本2
 * @returns {number} 相似度分数 (0-1)
 */
const calculateTextSimilarity = (text1, text2) => {
  if (!text1 || !text2) return 0;

  // 完全匹配
  if (text1 === text2) return 1.0;

  // 包含关系
  if (text1.includes(text2) || text2.includes(text1)) {
    const shorter = text1.length < text2.length ? text1 : text2;
    const longer = text1.length >= text2.length ? text1 : text2;
    return shorter.length / longer.length;
  }

  // 字符级相似度
  const commonChars = countCommonCharacters(text1, text2);
  const maxLength = Math.max(text1.length, text2.length);

  return maxLength > 0 ? commonChars / maxLength : 0;
};

/**
 * 计算两个字符串的共同字符数
 * @param {string} str1 - 字符串1
 * @param {string} str2 - 字符串2
 * @returns {number} 共同字符数
 */
const countCommonCharacters = (str1, str2) => {
  const chars1 = [...str1];
  const chars2 = [...str2];
  let commonCount = 0;

  chars1.forEach(char => {
    const index = chars2.indexOf(char);
    if (index !== -1) {
      chars2.splice(index, 1);
      commonCount++;
    }
  });

  return commonCount;
};

/**
 * 简单的准确率计算（备用方案）
 */
export const calculateAccuracySimple = (expected, actual) => {
  if (!expected || !actual) return 0;

  // 检查是否为解析失败的结果
  const actualStr = String(actual);
  if (actualStr.includes('MonkeyOCR处理失败') ||
      actualStr.includes('MonkeyOCR文件上传失败') ||
      actualStr.includes('MonkeyOCR处理超时') ||
      actualStr.includes('MonkeyOCR获取结果失败') ||
      actualStr.includes('MonkeyOCR处理请求提交失败') ||
      actualStr.includes('VL LLM处理失败') ||
      actualStr.includes('KDC处理失败')) {
    return 0;
  }

  // 简单的字符串相似度计算
  const expectedStr = String(expected).toLowerCase().trim();
  const actualStrLower = actualStr.toLowerCase().trim();

  if (expectedStr === actualStrLower) return 100;

  // 使用编辑距离计算相似度
  const distance = levenshteinDistance(expectedStr, actualStrLower);
  const maxLength = Math.max(expectedStr.length, actualStrLower.length);

  if (maxLength === 0) return 100;

  return Math.max(0, ((maxLength - distance) / maxLength) * 100);
};

/**
 * 计算编辑距离
 */
const levenshteinDistance = (str1, str2) => {
  const matrix = [];
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }
  
  return matrix[str2.length][str1.length];
};

/**
 * 处理解析结果数据，整合6路解析结果
 * 基于images目录中的图片文件，使用文件名匹配而不是索引匹配
 * 因为不同解析器的处理顺序可能不一致
 * 只显示当前解析任务中启用的解析器结果
 */
export const processParseResults = (parseData, imageList = []) => {
  if (!parseData) return [];

  // 获取当前启用的解析器列表
  const enabledParsers = parseData.metadata?.parser_config?.enabled_parsers || [];

  // 从parse_results对象中提取实际的解析结果，使用正确的字段名
  const parseResults = parseData.parse_results || {};
  const {
    kdc_markdown: kdc_results = [],           // KDC Markdown结果
    kdc_plain: kdc_plain_results = [],        // KDC Plain结果
    kdc_kdc: kdc_kdc_results = [],           // KDC KDC结果
    monkey_ocr: monkey_ocr_results = [],      // MonkeyOCR结果
    monkey_ocr_latex: monkey_ocr_results_v2 = [], // MonkeyOCR Latex结果
    vl_llm: vl_llm_results = [],             // VL LLM结果
    monkey_ocr_local: monkey_ocr_local_results = [], // MonkeyOCR Local结果
    monkey_ocr_kas: monkey_ocr_kas_results = [],      // MonkeyOCR（kas）解析器结果
    ocrflux: ocrflux_results = [],           // OCR Flux结果
    mineru: mineru_results = [],             // Mineru结果
    mineru_vlm: mineru_vlm_results = []      // Mineru VLM结果
  } = parseResults;

  // 同时保持对旧格式的兼容性（直接从parseData提取）
  const {
    uploaded_files = [],
    kdc_results: legacy_kdc_results = [],
    kdc_plain_results: legacy_kdc_plain_results = [],
    kdc_kdc_results: legacy_kdc_kdc_results = [],
    monkey_ocr_results: legacy_monkey_ocr_results = [],
    monkey_ocr_results_v2: legacy_monkey_ocr_results_v2 = [],
    vl_llm_results: legacy_vl_llm_results = [],
    monkey_ocr_local_results: legacy_monkey_ocr_local_results = [],
    monkey_ocr_kas_results: legacy_monkey_ocr_kas_results = [],
    ocrflux: legacy_ocrflux_results = []
  } = parseData;

  // 使用新格式数据，如果为空则回退到旧格式
  const finalKdcResults = kdc_results.length > 0 ? kdc_results : legacy_kdc_results;
  const finalKdcPlainResults = kdc_plain_results.length > 0 ? kdc_plain_results : legacy_kdc_plain_results;
  const finalKdcKdcResults = kdc_kdc_results.length > 0 ? kdc_kdc_results : legacy_kdc_kdc_results;
  const finalMonkeyOcrResults = monkey_ocr_results.length > 0 ? monkey_ocr_results : legacy_monkey_ocr_results;
  const finalMonkeyOcrResultsV2 = monkey_ocr_results_v2.length > 0 ? monkey_ocr_results_v2 : legacy_monkey_ocr_results_v2;
  const finalVlLlmResults = vl_llm_results.length > 0 ? vl_llm_results : legacy_vl_llm_results;
  const finalMonkeyOcrLocalResults = monkey_ocr_local_results.length > 0 ? monkey_ocr_local_results : legacy_monkey_ocr_local_results;
  const finalMonkeyOcrKasResults = monkey_ocr_kas_results.length > 0 ? monkey_ocr_kas_results : legacy_monkey_ocr_kas_results;
  const finalOcrfluxResults = ocrflux_results.length > 0 ? ocrflux_results : legacy_ocrflux_results;
  const finalMineruResults = mineru_results;
  const finalMineruVlmResults = mineru_vlm_results;

  // 强制使用后端的processed_files顺序，确保前后端一致
  const processed_files = parseData.processed_files || [];
  let sourceList = [];

  if (processed_files.length > 0) {
    // 使用后端的处理顺序，提取原始图片文件名
    sourceList = processed_files.map(file => file.original_image || file.filename || file.name);
  } else {
    // 如果没有processed_files，使用imageList但按字母顺序排序以匹配后端
    sourceList = imageList.length > 0 ? imageList.slice().sort() : uploaded_files;
  }

  const sortedSourceList = sourceList;

  logger.debug('Processing parse data:', {
    imageList: imageList.length,
    uploaded_files: uploaded_files.length,
    sortedSourceList: sortedSourceList.length,
    kdc_results: finalKdcResults.length,
    kdc_plain_results: finalKdcPlainResults.length,
    kdc_kdc_results: finalKdcKdcResults.length,
    monkey_ocr_results: finalMonkeyOcrResults.length,
    monkey_ocr_results_v2: finalMonkeyOcrResultsV2.length,
    vl_llm_results: finalVlLlmResults.length,
    monkey_ocr_local_results: finalMonkeyOcrLocalResults.length,
    monkey_ocr_kas_results: finalMonkeyOcrKasResults.length,
    ocrflux_results: finalOcrfluxResults.length
  });

  // 创建文件名到解析结果的映射，以便快速查找
  // 使用多种可能的文件名格式进行匹配
  const createFileNameMap = (results, getFileNameFn, getOriginalImageFn) => {
    const map = new Map();
    results.forEach(result => {
      // 尝试多种文件名提取方式
      const fileName = getFileNameFn(result);
      const originalImage = getOriginalImageFn ? getOriginalImageFn(result) : null;
      
      if (fileName) {
        const baseName = getImageBaseName(fileName);
        map.set(baseName, result);
        
        // 也尝试完整文件名
        map.set(fileName, result);
        map.set(fileName.replace(/\.(pdf|png|jpg|jpeg)$/i, ''), result);
      }
      
      if (originalImage) {
        const baseName = getImageBaseName(originalImage);
        map.set(baseName, result);
        map.set(originalImage, result);
        map.set(originalImage.replace(/\.(pdf|png|jpg|jpeg)$/i, ''), result);
      }
    });
    return map;
  };

  // 创建各路解析结果的文件名映射
  const kdcMap = createFileNameMap(finalKdcResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const kdcPlainMap = createFileNameMap(finalKdcPlainResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const kdcKdcMap = createFileNameMap(finalKdcKdcResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const monkeyOcrMap = createFileNameMap(finalMonkeyOcrResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const monkeyOcrV2Map = createFileNameMap(finalMonkeyOcrResultsV2, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const vlLlmMap = createFileNameMap(finalVlLlmResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const monkeyOcrLocalMap = createFileNameMap(finalMonkeyOcrLocalResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const monkeyOcrKasMap = createFileNameMap(finalMonkeyOcrKasResults,
    r => r.filename || r.fname,
    r => r.original_image);
  
  const ocrfluxMap = createFileNameMap(finalOcrfluxResults, 
    r => r.filename || r.fname, 
    r => r.original_image);
  
  const mineruMap = createFileNameMap(finalMineruResults, 
    r => r.filename || r.fname, 
    r => r.original_image);

  // 调试mineru_vlm结果
  logger.error('[processParseResults] Mineru VLM结果:', {
    finalMineruVlmResults,
    hasResults: Array.isArray(finalMineruVlmResults) && finalMineruVlmResults.length > 0
  });

  const mineruVlmMap = createFileNameMap(finalMineruVlmResults, 
    r => r.filename || r.fname, 
    r => r.original_image);



  return sortedSourceList.map((item, index) => {
    // 如果是图片文件名，直接使用；如果是uploaded_files对象，提取文件名
    const imageName = typeof item === 'string' ? item : (item.original_image || item.fname || item.filename || item.name || `file_${index}`);
    const fileName = imageName;
    const baseName = getImageBaseName(fileName);
    const pdfBaseName = baseName; // PDF通常使用相同的基础名称



    // 使用文件名匹配查找各路解析结果
    // 尝试多种可能的匹配方式：baseName, fileName, pdfBaseName等
    const findResult = (map, targetBaseName, targetFileName) => {
      // 优先使用baseName匹配
      let result = map.get(targetBaseName);
      if (result) return result;
      
      // 尝试完整文件名
      result = map.get(targetFileName);
      if (result) return result;
      
      // 尝试PDF文件名
      result = map.get(targetBaseName + '.pdf');
      if (result) return result;
      
      // 尝试原始图片名
      result = map.get(targetFileName.replace(/\.(pdf)$/i, '.png'));
      if (result) return result;
      
      return null;
    };

    const kdcResult = findResult(kdcMap, baseName, fileName);
    const kdcPlainResult = findResult(kdcPlainMap, baseName, fileName);
    const kdcKdcResult = findResult(kdcKdcMap, baseName, fileName);
    const monkeyResult = findResult(monkeyOcrMap, baseName, fileName);
    const monkeyResultV2 = findResult(monkeyOcrV2Map, baseName, fileName);
    const vlLLMResult = findResult(vlLlmMap, baseName, fileName);

    // 调试VL LLM文件名匹配
    if (index === 0) {
      logger.error('[processParseResults] 第一个案例VL LLM匹配调试:', {
        imageName,
        fileName,
        baseName,
        hasVlLLMResult: !!vlLLMResult,
        vlLlmMapSize: vlLlmMap.size,
        vlLlmMapKeys: Array.from(vlLlmMap.keys()).slice(0, 10)
      });
    }
    const monkeyOCRLocalResult = findResult(monkeyOcrLocalMap, baseName, fileName);
    const monkeyOcrKasResult = findResult(monkeyOcrKasMap, baseName, fileName);
    const ocrfluxResult = findResult(ocrfluxMap, baseName, fileName);
    const mineruResult = findResult(mineruMap, baseName, fileName);
    const mineruVlmResult = findResult(mineruVlmMap, baseName, fileName);
    
    // 调试mineru_vlm结果匹配
    if (index === 0) {
      logger.error('[processParseResults] 第一个案例Mineru VLM匹配调试:', {
        imageName,
        fileName,
        baseName,
        hasMineruVlmResult: !!mineruVlmResult,
        mineruVlmMapSize: mineruVlmMap.size,
        mineruVlmMapKeys: Array.from(mineruVlmMap.keys()).slice(0, 10)
      });
    }



    // 提取KDC KDC的特征信息
    const kdcFeatures = kdcKdcResult && kdcKdcResult.features ? kdcKdcResult.features : null;

    return {
      id: `case_${index}`,
      index: index + 1, // 显示用的1基索引
      fileName: imageName, // 使用原始图片文件名
      baseName,
      imageName, // 原始图片文件名
      pdfFileName: baseName + '.pdf', // 对应的PDF文件名
      file: typeof item === 'string' ? { name: item } : item, // 兼容原有结构
      // KDC Markdown (原kdc_results)
      kdcMarkdown: kdcResult || null,
      // KDC Plain
      kdcPlain: kdcPlainResult || null,
      // KDC KDC
      kdcKdc: kdcKdcResult || null,
      // MonkeyOCR (table prompt)
      monkeyOCR: monkeyResult || null,
      // MonkeyOCR (parse)
      monkeyOCRV2: monkeyResultV2 || null,
      // VL LLM
      vlLLMResult: vlLLMResult || null,
      // MonkeyOCR (local)
      monkeyOCRLocal: monkeyOCRLocalResult || null,
      // MonkeyOCR（kas）
      monkeyOcrKas: monkeyOcrKasResult || null,
      // OCR Flux
      ocrflux: ocrfluxResult || null,
      // Mineru - 使用对象格式（不需要数组包装）
      mineru: mineruResult || null,
      // Mineru VLM - 使用对象格式（不需要数组包装）
      mineru_vlm: mineruVlmResult || null,
      // 特征信息
      features: {
        kdc: kdcFeatures
      }
    };
  });
};

/**
 * 提取文本内容用于比较
 */
export const extractTextContent = (data) => {
  if (!data) return '';

  if (typeof data === 'string') return data;

  if (data.markdown) return data.markdown;
  if (data.plain) return data.plain;
  if (data.text) return data.text;
  if (data.content) return data.content;

  return JSON.stringify(data);
};

/**
 * 按照原始HTML报告的逻辑提取各路解析结果的文本内容
 * 用于在原始文本列显示
 */
export const extractParseResultText = (data, type = 'markdown') => {
  if (!data) return '';

  switch (type) {
    case 'markdown':
      // KDC Markdown结果提取 和 本地MonkeyOCR结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // 本地MonkeyOCR结果格式：{ results: [{ filename: "xxx", result: { html: "xxx", markdown: "xxx", type: "html" } }] }
          if (data.results && Array.isArray(data.results) && data.results.length > 0) {
            const firstResult = data.results[0];
            if (firstResult.result && typeof firstResult.result === 'object') {
              // 检查success字段
              if (firstResult.result.success === false) {
                return `MonkeyOCR Local处理失败: ${firstResult.result.html || firstResult.result.markdown || '未知错误'}`;
              }
              
              // 优先使用 html 内容，因为 type 通常是 "html"
              if (firstResult.result.html) {
                return firstResult.result.html;
              }
              if (firstResult.result.markdown) {
                return firstResult.result.markdown;
              }
            }
          }
          // 兼容旧格式：单个result字段
          if (data.result && typeof data.result === 'object') {
            // 优先使用 html 内容，因为 type 通常是 "html"
            if (data.result.html) {
              return data.result.html;
            }
            if (data.result.markdown) {
              return data.result.markdown;
            }
            // 新格式：KDC类型的数据在result.data中
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              return resultData[0].markdown || '';
            }
          }
          // 兼容旧格式
          if (data.result && typeof data.result === 'string') {
            return data.result;
          }
          // 兼容旧格式：KDC Markdown结果格式
          const dataArray = data.data || [];
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            return dataArray[0].markdown || '';
          } else if (typeof dataArray === 'object') {
            return dataArray.markdown || '';
          }
        } catch (e) {
          logger.error('提取markdown内容失败:', e);
        }
      }
      return '';

    case 'plain':
      // KDC Plain结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // 新格式：数据在result.data中
          if (data.result && data.result.data) {
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              return resultData[0].plain || '';
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            return dataArray[0].plain || '';
          } else if (typeof dataArray === 'object') {
            return dataArray.plain || '';
          }
        } catch (e) {
          logger.error('提取plain内容失败:', e);
        }
      }
      return '';

    case 'kdc':
      // KDC KDC结果提取 - 按照HTML报告的逻辑，原始文本显示完整的JSON数据
      if (typeof data === 'object') {
        try {
          // 新格式：数据在result.data中
          if (data.result && data.result.data) {
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              const docData = resultData[0].doc || {};
              if (docData && typeof docData === 'object') {
                return JSON.stringify(docData, null, 2);
              }
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          let docData = null;
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            docData = dataArray[0].doc || {};
          } else if (typeof dataArray === 'object') {
            docData = dataArray.doc || {};
          }

          if (docData) {
            // 返回格式化的JSON字符串，用于原始文本显示
            return JSON.stringify(docData, null, 2);
          }
        } catch (e) {
          logger.error('提取kdc内容失败:', e);
        }
      }
      return '';

    case 'html':
      // MonkeyOCR结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        // 检查新格式的success字段
        if (data.result && data.result.success === false) {
          return `MonkeyOCR处理失败: ${data.result.html || '未知错误'}`;
        }
        
        // 检查是否超时
        if (data.result && data.result.is_timeout) {
          const processing_time = data.result.processing_time || 0;
          return `MonkeyOCR处理超时 (${processing_time.toFixed(1)}秒)`;
        }

        // MonkeyOCR结果格式：{ filename: "xxx", result: { html: "xxx", text_content: [...] } }
        if (data.result) {
          // MonkeyOCR（kas）结果格式支持：{ result: { content: "HTML内容", format: "markdown" } }
          if (data.result.content) {
            return data.result.content;
          }
          // 优先使用result.html字段
          if (data.result.html) {
            return data.result.html;
          }
          // 如果没有html字段，尝试使用result.text_content的第一个元素
          if (data.result.text_content && Array.isArray(data.result.text_content) && data.result.text_content.length > 0) {
            return data.result.text_content[0];
          }
        }
        // 兼容旧格式：直接在data层级
        if (data.html) {
          return data.html;
        }
        if (data.text_content && Array.isArray(data.text_content) && data.text_content.length > 0) {
          return data.text_content[0];
        }
      }
      return '';

    case 'vl_llm':
      // VL LLM结果提取 - 处理两种不同的数据结构
      if (typeof data === 'object') {
        try {
          const result = data.result || {};

          // 尝试两种不同的数据结构
          let choices = [];

          // 结构1: result.content.choices (tables数据集)
          if (result.content && result.content.choices) {
            choices = result.content.choices;
            logger.debug('VL LLM using structure 1 (result.content.choices)');
          }
          // 结构2: result.choices (kingsoft数据集)
          else if (result.choices) {
            choices = result.choices;
            logger.debug('VL LLM using structure 2 (result.choices)');
          }

          if (choices.length > 0) {
            const message = choices[0].message || {};
            let content = message.content || '';

            // 清理markdown代码块标记
            if (content.startsWith('```markdown')) {
              content = content.replace('```markdown', '').replace(/```$/, '').trim();
            } else if (content.startsWith('```')) {
              content = content.replace(/^```/, '').replace(/```$/, '').trim();
            }

            logger.debug('VL LLM content extracted successfully, length:', content.length);
            return content;
          }
        } catch (e) {
          logger.error('提取VL LLM内容失败:', e);
        }
      }
      return '';

    case 'monkey_ocr_kas':
      // MonkeyOCR（kas）解析器结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        if (data.result && data.result.content) {
          return data.result.content;
        }
        if (data.content) {
          return data.content;
        }
      }
      return '';

    case 'ocrflux':
      // OCR Flux结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // OCR Flux结果格式：{ result: { content: "文本内容", format: "html" } }
          if (data.result && data.result.content) {
            return data.result.content;
          }
          // 兼容其他可能的格式
          if (data.content) {
            return data.content;
          }
          // 检查API响应中的内容
          if (data.result && data.result.api_response) {
            const choices = data.result.api_response.choices || [];
            if (choices.length > 0 && choices[0].message) {
              return choices[0].message.content || '';
            }
          }
        } catch (e) {
          logger.error('提取OCR Flux渲染内容失败:', e);
        }
      }
      return '';

    default:
      return '';
  }
};

/**
 * 提取用于渲染的数据内容
 * 与extractParseResultText不同，这个函数返回适合TableRenderer渲染的数据
 */
export const extractParseResultForRender = (data, type = 'markdown') => {
  if (!data) return '';

  // 规范化类型
  const normalizedType = (() => {
    switch (type) {
      case 'monkeyocr':
      case 'monkeyocrv2':
      case 'html':
        return 'html';
      case 'markdown':
      case 'monkeyocrlocal':
      case 'vl_llm':
        return 'markdown';
      default:
        return type;
    }
  })();

      switch (normalizedType) {
      case 'markdown':
        // KDC Markdown结果提取 和 本地MonkeyOCR结果提取
        if (typeof data === 'string') return data;
        if (typeof data === 'object') {
          try {
            // Mineru VLM结果提取
            if (data.result?.layout_data) {
              // 将layout_data转换为markdown格式
              const layoutData = data.result.layout_data;
              let markdown = '';
              for (const item of layoutData) {
                if (item.type === 'title') {
                  markdown += `# ${item.text}\n\n`;
                } else if (item.type === 'table') {
                  markdown += item.text + '\n\n';
                } else if (item.type === 'text') {
                  markdown += item.text + '\n\n';
                }
              }
              return markdown.trim();
            }
            // VL LLM结果提取
            if (data.result) {
            // 尝试所有可能的路径
            const possiblePaths = [
              () => data.result.content?.choices?.[0]?.message?.content,
              () => data.result.choices?.[0]?.message?.content,
              () => data.content?.choices?.[0]?.message?.content,
              () => data.choices?.[0]?.message?.content,
              () => data.result.content,
              () => data.content,
              () => data.text,
              () => data.markdown,
              () => typeof data === 'string' ? data : null
            ];

            for (const getPath of possiblePaths) {
              try {
                const text = getPath();
                if (text && typeof text === 'string' && text.trim()) {
                  // 清理markdown代码块标记
                  if (text.startsWith('```markdown')) {
                    return text.replace('```markdown', '').replace(/```$/, '').trim();
                  } else if (text.startsWith('```')) {
                    return text.replace(/^```/, '').replace(/```$/, '').trim();
                  }
                  return text;
                }
              } catch (e) {
                // 忽略错误，继续尝试下一个路径
              }
            }
          }

          // 本地MonkeyOCR结果格式：{ results: [{ filename: "xxx", result: { html: "xxx", markdown: "xxx", type: "html" } }] }
          if (data.results && Array.isArray(data.results) && data.results.length > 0) {
            const firstResult = data.results[0];
            if (firstResult.result && typeof firstResult.result === 'object') {
              // 检查success字段
              if (firstResult.result.success === false) {
                return `MonkeyOCR Local处理失败: ${firstResult.result.html || firstResult.result.markdown || '未知错误'}`;
              }
              
              if (firstResult.result.type === 'latex') {
                return firstResult.result.latex || firstResult.result.html;  // 对于LaTeX格式，优先返回latex内容
              }
              if (firstResult.result.type === 'html') {
                return firstResult.result.html;  // 对于HTML格式，返回html内容
              }
              if (firstResult.result.markdown) {
                return firstResult.result.markdown;
              }
              if (firstResult.result.html) {
                return firstResult.result.html;
              }
            }
          }
          // 兼容旧格式和KDC类型
          if (data.result && typeof data.result === 'object') {
            // 优先使用 html 内容，因为 type 通常是 "html"
            if (data.result.html) {
              return data.result.html;
            }
            if (data.result.markdown) {
              return data.result.markdown;
            }
            // 新格式：KDC类型的数据在result.data中
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              return resultData[0].markdown || '';
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            return dataArray[0].markdown || '';
          }
        } catch (e) {
          logger.error('提取markdown内容失败:', e);
        }
      }
      return '';

    case 'latex':
      // LaTeX格式提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        // MonkeyOCR Local 结果中的 LaTeX
        if (data.results && Array.isArray(data.results) && data.results.length > 0) {
          const firstResult = data.results[0];
          if (firstResult.result && firstResult.result.latex) {
            return firstResult.result.latex;
          }
          if (firstResult.result && firstResult.result.html) {
            return firstResult.result.html;
          }
        }
        // 旧格式
        if (data.result && data.result.html) {
          return data.result.html;
        }
      }
      return '';

    case 'plain':
      // KDC Plain结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // 新格式：数据在result.data中
          if (data.result && data.result.data) {
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              return resultData[0].plain || '';
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            return dataArray[0].plain || '';
          } else if (typeof dataArray === 'object') {
            return dataArray.plain || '';
          }
        } catch (e) {
          logger.error('提取plain内容失败:', e);
        }
      }
      return '';

    case 'html':
      // MonkeyOCR结果提取
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        // 检查新格式的success字段
        if (data.result && data.result.success === false) {
          return `MonkeyOCR处理失败: ${data.result.html || '未知错误'}`;
        }
        
        // 检查是否超时
        if (data.result && data.result.is_timeout) {
          const processing_time = data.result.processing_time || 0;
          return `MonkeyOCR处理超时 (${processing_time.toFixed(1)}秒)`;
        }

        if (data.result) {
          // MonkeyOCR（kas）结果格式支持：{ result: { content: "HTML内容", format: "markdown" } }
          if (data.result.content) {
            return data.result.content;
          }
          if (data.result.html) {
            return data.result.html;
          }
          if (data.result.text_content && Array.isArray(data.result.text_content) && data.result.text_content.length > 0) {
            return data.result.text_content[0];
          }
        }
        if (data.html) {
          return data.html;
        }
        if (data.text_content && Array.isArray(data.text_content) && data.text_content.length > 0) {
          return data.text_content[0];
        }
      }
      return '';

    case 'kdc':
      // KDC KDC结果提取 - 返回Canvas渲染器期望的数据格式
      if (typeof data === 'object') {
        try {
          // 新格式：数据在result.data中
          if (data.result && data.result.data) {
            const resultData = data.result.data || [];
            if (Array.isArray(resultData) && resultData.length > 0) {
              const docData = resultData[0].doc;
              // 检查docData是否有实际的表格内容
              if (docData && typeof docData === 'object') {
                // 递归检查是否包含表格数据
                const hasTableContent = (obj) => {
                  if (!obj || typeof obj !== 'object') return false;
                  
                  // 检查是否有 table 字段且包含 rows
                  if (obj.table && obj.table.rows && Array.isArray(obj.table.rows) && obj.table.rows.length > 0) {
                    return true;
                  }
                  
                  // 递归检查 children
                  if (obj.children && Array.isArray(obj.children)) {
                    for (const child of obj.children) {
                      if (hasTableContent(child)) return true;
                      if (child.blocks && Array.isArray(child.blocks)) {
                        for (const block of child.blocks) {
                          if (hasTableContent(block)) return true;
                        }
                      }
                    }
                  }
                  
                  // 检查 tree 结构
                  if (obj.tree && hasTableContent(obj.tree)) {
                    return true;
                  }
                  
                  return false;
                };
                
                if (hasTableContent(docData)) {
                  // 返回Canvas渲染器期望的格式: { data: [{ doc: ... }] }
                  return { data: [{ doc: docData }] };
                }
              }
            }
          }
          // 兼容旧格式
          const dataArray = data.data || [];
          let docData = null;
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            docData = dataArray[0].doc;
          } else if (typeof dataArray === 'object') {
            docData = dataArray.doc;
          }

          // 检查docData是否有实际的表格内容
          if (docData && typeof docData === 'object') {
            // 递归检查是否包含表格数据
            const hasTableContent = (obj) => {
              if (!obj || typeof obj !== 'object') return false;
              
              // 检查是否有 table 字段且包含 rows
              if (obj.table && obj.table.rows && Array.isArray(obj.table.rows) && obj.table.rows.length > 0) {
                return true;
              }
              
              // 递归检查 children
              if (obj.children && Array.isArray(obj.children)) {
                for (const child of obj.children) {
                  if (hasTableContent(child)) return true;
                  if (child.blocks && Array.isArray(child.blocks)) {
                    for (const block of child.blocks) {
                      if (hasTableContent(block)) return true;
                    }
                  }
                }
              }
              
              // 检查 tree 结构
              if (obj.tree && hasTableContent(obj.tree)) {
                return true;
              }
              
              return false;
            };
            
            if (hasTableContent(docData)) {
              // 返回Canvas渲染器期望的格式: { data: [{ doc: ... }] }
              return { data: [{ doc: docData }] };
            }
          }
        } catch (e) {
          logger.error('提取kdc内容失败:', e);
        }
      }
      return '';

    case 'ocrflux':
      // OCR Flux结果提取（用于渲染）
      if (typeof data === 'string') return data;
      if (typeof data === 'object') {
        try {
          // OCR Flux结果格式：{ result: { content: "文本内容", format: "html" } }
          if (data.result && data.result.content) {
            return data.result.content;
          }
          // 兼容其他可能的格式
          if (data.content) {
            return data.content;
          }
          // 检查API响应中的内容
          if (data.result && data.result.api_response) {
            const choices = data.result.api_response.choices || [];
            if (choices.length > 0 && choices[0].message) {
              return choices[0].message.content || '';
            }
          }
        } catch (e) {
          logger.error('提取OCR Flux渲染内容失败:', e);
        }
      }
      return '';

    default:
      return '';
  }
};

/**
 * 从KDC文档结构中提取文本内容的辅助函数
 */
export const extractTextFromKdcDoc = (docData) => {
  if (!docData || typeof docData !== 'object') return '';

  try {
    // 递归提取所有文本内容
    const extractText = (obj) => {
      if (typeof obj === 'string') return obj;
      if (Array.isArray(obj)) {
        return obj.map(extractText).join(' ');
      }
      if (typeof obj === 'object' && obj !== null) {
        if (obj.text) return obj.text;
        if (obj.content) return extractText(obj.content);
        if (obj.children) return extractText(obj.children);
        // 递归处理所有属性
        return Object.values(obj).map(extractText).filter(Boolean).join(' ');
      }
      return '';
    };

    return extractText(docData);
  } catch (e) {
    logger.error('从KDC文档提取文本失败:', e);
    return JSON.stringify(docData, null, 2);
  }
};

/**
 * 改进的表格数据比较算法
 * 更好地处理表格结构差异和内容匹配
 */
const compareTableDataImproved = (table1, table2) => {
  if (!table1 || !table2) return 0;
  if (table1.length === 0 && table2.length === 0) return 100;
  if (table1.length === 0 || table2.length === 0) return 0;

  // 使用与现有compareTableData相同的算法，但添加更多调试信息
  const gt_flat = table1.flat().map(cell => String(cell || '').toLowerCase().trim()).filter(cell => cell.length > 0);
  const pred_flat = table2.flat().map(cell => String(cell || '').toLowerCase().trim()).filter(cell => cell.length > 0);

  logger.debug('改进表格比较调试:', {
    table1Rows: table1.length,
    table2Rows: table2.length,
    gt_flatCells: gt_flat.length,
    pred_flatCells: pred_flat.length,
    gt_sample: gt_flat.slice(0, 5),
    pred_sample: pred_flat.slice(0, 5)
  });

  if (!gt_flat.length && !pred_flat.length) return 100;
  if (!gt_flat.length || !pred_flat.length) return 0;

  // 计算每个单元格的出现次数
  const gt_counter = {};
  const pred_counter = {};

  gt_flat.forEach(cell => {
    gt_counter[cell] = (gt_counter[cell] || 0) + 1;
  });

  pred_flat.forEach(cell => {
    pred_counter[cell] = (pred_counter[cell] || 0) + 1;
  });

  // 计算命中数量（取最小值）
  let hit = 0;
  for (const cell in gt_counter) {
    if (pred_counter[cell]) {
      hit += Math.min(gt_counter[cell], pred_counter[cell]);
    }
  }

  const total = gt_flat.length;
  const accuracy = total > 0 ? (hit / total) * 100 : 0;

  logger.debug('改进表格匹配结果:', {
    hit,
    total,
    accuracy: accuracy.toFixed(2),
    matchedCells: Object.keys(gt_counter).filter(cell => pred_counter[cell]).length,
    totalUniqueCells: Object.keys(gt_counter).length
  });

  return accuracy;
};

/**
 * 改进的文本准确率计算
 * 更好地处理HTML和Markdown格式的差异
 */
const calculateAccuracyImproved = (expected, actual) => {
  if (!expected || !actual) return 0;

  // 提取纯文本内容
  const extractTextContent = (content) => {
    let text = String(content);

    // 移除HTML标签
    text = text.replace(/<[^>]*>/g, ' ');

    // 移除Markdown标记
    text = text.replace(/\|/g, ' ');
    text = text.replace(/[-:]+/g, ' ');
    text = text.replace(/\*\*/g, '');
    text = text.replace(/\*/g, '');
    text = text.replace(/#/g, '');

    // 标准化空白字符
    text = text.replace(/\s+/g, ' ').trim();

    return text.toLowerCase();
  };

  const expectedText = extractTextContent(expected);
  const actualText = extractTextContent(actual);

  logger.debug('改进文本内容比较:', {
    expectedLength: expectedText.length,
    actualLength: actualText.length,
    expectedSample: expectedText.substring(0, 100),
    actualSample: actualText.substring(0, 100)
  });

  if (expectedText === actualText) return 100;
  if (!expectedText || !actualText) return 0;

  // 使用词汇级别的比较
  const expectedWords = expectedText.split(/\s+/).filter(w => w.length > 0);
  const actualWords = actualText.split(/\s+/).filter(w => w.length > 0);

  if (expectedWords.length === 0 && actualWords.length === 0) return 100;
  if (expectedWords.length === 0 || actualWords.length === 0) return 0;

  // 计算词汇匹配度
  const totalWords = expectedWords.length; // 以期望文本为基准
  let matchedWords = 0;

  const actualWordsSet = new Set(actualWords);

  for (const word of expectedWords) {
    if (actualWordsSet.has(word)) {
      matchedWords++;
    }
  }

  const accuracy = (matchedWords / totalWords) * 100;
  logger.debug('改进文本匹配结果:', {
    expectedWords: expectedWords.length,
    actualWords: actualWords.length,
    matchedWords,
    totalWords,
    accuracy: accuracy.toFixed(2)
  });

  return accuracy;
};

/**
 * TEDS (Tree Edit Distance based Similarity) 指标计算
 * 用于评估表格结构的相似性
 */
export const calculateTEDS = (expectedTable, actualTable) => {
  if (!expectedTable || !actualTable) return 0;
  
  try {
    // 如果两个表格都为空
    if (expectedTable.length === 0 && actualTable.length === 0) return 100;
    if (expectedTable.length === 0 || actualTable.length === 0) return 0;
    
    // 基本结构相似性
    const expectedRows = expectedTable.length;
    const actualRows = actualTable.length;
    const expectedCols = expectedTable[0] ? expectedTable[0].length : 0;
    const actualCols = actualTable[0] ? actualTable[0].length : 0;
    
    // 结构差异惩罚
    const rowDiff = Math.abs(expectedRows - actualRows);
    const colDiff = Math.abs(expectedCols - actualCols);
    const structurePenalty = (rowDiff + colDiff) / (expectedRows + expectedCols + 1);
    
    // 内容相似性（基于编辑距离）
    let totalEditDistance = 0;
    let totalLength = 0;
    const maxRows = Math.max(expectedRows, actualRows);
    const maxCols = Math.max(expectedCols, actualCols);
    
    for (let i = 0; i < maxRows; i++) {
      for (let j = 0; j < maxCols; j++) {
        const expectedCell = (expectedTable[i] && expectedTable[i][j]) ? String(expectedTable[i][j]).trim() : '';
        const actualCell = (actualTable[i] && actualTable[i][j]) ? String(actualTable[i][j]).trim() : '';
        
        if (expectedCell || actualCell) {
          const editDist = levenshteinDistance(expectedCell, actualCell);
          const maxLen = Math.max(expectedCell.length, actualCell.length);
          totalEditDistance += editDist;
          totalLength += maxLen;
        }
      }
    }
    
    // 计算内容相似性
    const contentSimilarity = totalLength > 0 ? 1 - (totalEditDistance / totalLength) : 1;
    
    // 综合TEDS得分
    const structureWeight = 0.3;
    const contentWeight = 0.7;
    const structureSimilarity = Math.max(0, 1 - structurePenalty);
    
    const teds = (structureWeight * structureSimilarity + contentWeight * contentSimilarity) * 100;
    
    return Math.max(0, Math.min(100, teds));
  } catch (error) {
    logger.error('TEDS计算失败:', error);
    return 0;
  }
};

/**
 * 计算详细的TEDS分析数据
 * 返回结构分析和内容分析的详细信息
 */
export const calculateDetailedTEDS = (expectedTable, actualTable) => {
  try {
    if (!expectedTable || !actualTable) {
      return {
        score: 0,
        structureAnalysis: null,
        contentAnalysis: null,
        hasData: false
      };
    }

    // 确保输入是二维数组
    const expected = Array.isArray(expectedTable) ? expectedTable : [];
    const actual = Array.isArray(actualTable) ? actualTable : [];

    if (expected.length === 0 && actual.length === 0) {
      return {
        score: 100,
        structureAnalysis: { perfect: true },
        contentAnalysis: { perfect: true },
        hasData: true
      };
    }

    if (expected.length === 0 || actual.length === 0) {
      return {
        score: 0,
        structureAnalysis: { empty: true },
        contentAnalysis: { empty: true },
        hasData: true
      };
    }

    // 结构分析
    const expectedRows = expected.length;
    const actualRows = actual.length;
    const expectedCols = expected[0] ? expected[0].length : 0;
    const actualCols = actual[0] ? actual[0].length : 0;

    const rowDiff = Math.abs(expectedRows - actualRows);
    const colDiff = Math.abs(expectedCols - actualCols);
    const structurePenalty = (rowDiff + colDiff) / (expectedRows + expectedCols + 1);
    const structureSimilarity = Math.max(0, 1 - structurePenalty);

    const structureAnalysis = {
      expectedRows,
      actualRows,
      expectedCols,
      actualCols,
      rowDiff,
      colDiff,
      structureSimilarity: structureSimilarity * 100,
      isStructureMatch: rowDiff === 0 && colDiff === 0
    };

    // 内容分析
    let totalEditDistance = 0;
    let totalLength = 0;
    let cellComparisons = [];

    const maxRows = Math.max(expectedRows, actualRows);
    const maxCols = Math.max(expectedCols, actualCols);

    for (let i = 0; i < maxRows; i++) {
      for (let j = 0; j < maxCols; j++) {
        const expectedCell = (expected[i] && expected[i][j]) ? String(expected[i][j]).trim() : '';
        const actualCell = (actual[i] && actual[i][j]) ? String(actual[i][j]).trim() : '';

        if (expectedCell || actualCell) {
          const editDistance = levenshteinDistance(expectedCell, actualCell);
          const maxLength = Math.max(expectedCell.length, actualCell.length);
          const cellSimilarity = maxLength > 0 ? 1 - (editDistance / maxLength) : 1;

          cellComparisons.push({
            row: i,
            col: j,
            expectedContent: expectedCell,
            actualContent: actualCell,
            editDistance,
            similarity: cellSimilarity * 100,
            isMatch: expectedCell === actualCell
          });

          totalEditDistance += editDistance;
          totalLength += maxLength;
        }
      }
    }

    const contentSimilarity = totalLength > 0 ? 1 - (totalEditDistance / totalLength) : 1;

    const contentAnalysis = {
      totalCells: cellComparisons.length,
      matchedCells: cellComparisons.filter(c => c.isMatch).length,
      contentSimilarity: contentSimilarity * 100,
      cellComparisons: cellComparisons.slice(0, 20) // 只返回前20个单元格的详细比对
    };

    // 综合TEDS得分
    const structureWeight = 0.3;
    const contentWeight = 0.7;
    const teds = (structureWeight * structureSimilarity + contentWeight * contentSimilarity) * 100;

    return {
      score: Math.max(0, Math.min(100, teds)),
      structureAnalysis,
      contentAnalysis,
      hasData: true,
      weights: { structure: structureWeight, content: contentWeight }
    };
  } catch (error) {
    logger.error('详细TEDS计算失败:', error);
    return {
      score: 0,
      structureAnalysis: null,
      contentAnalysis: null,
      hasData: false,
      error: error.message
    };
  }
};

/**
 * 获取解析器结果状态的统一函数
 */
export const getParserResultStatus = (caseData, parserKey) => {
  const parserStatusMap = {
    kdcMarkdown: {
      hasResult: caseData.kdcMarkdown?.result?.data?.[0]?.markdown || caseData.kdcMarkdown?.data?.[0]?.markdown,
      hasTable: !!(caseData.kdcMarkdown?.result?.data?.[0]?.markdown || caseData.kdcMarkdown?.data?.[0]?.markdown)
    },
    kdcPlain: {
      hasResult: caseData.kdcPlain?.result?.data?.[0]?.plain || caseData.kdcPlain?.data?.[0]?.plain,
      hasTable: !!(caseData.kdcPlain?.result?.data?.[0]?.plain || caseData.kdcPlain?.data?.[0]?.plain)
    },
    kdcKdc: {
      hasResult: caseData.kdcKdc?.result?.data?.[0]?.doc || caseData.kdcKdc?.data?.[0]?.doc,
      hasTable: !!(caseData.kdcKdc?.result?.data?.[0]?.doc || caseData.kdcKdc?.data?.[0]?.doc)
    },
    monkeyOCR: {
      hasResult: caseData.monkeyOCR?.result?.html || caseData.monkeyOCR?.html,
      hasTable: !!(caseData.monkeyOCR?.result?.html || caseData.monkeyOCR?.html) &&
               caseData.monkeyOCR?.result?.success !== false &&
               !caseData.monkeyOCR?.result?.is_timeout &&
               !(caseData.monkeyOCR?.result?.html || caseData.monkeyOCR?.html || '').includes('MonkeyOCR文件上传失败')
    },
    monkeyOCRV2: {
      hasResult: caseData.monkeyOCRV2?.result?.html || caseData.monkeyOCRV2?.html,
      hasTable: !!(caseData.monkeyOCRV2?.result?.html || caseData.monkeyOCRV2?.html || caseData.monkey_ocr?.result?.html || caseData.monkey_ocr?.html) &&
               (caseData.monkeyOCRV2?.result?.success !== false && caseData.monkey_ocr?.result?.success !== false) &&
               !(caseData.monkeyOCRV2?.result?.is_timeout || caseData.monkey_ocr?.result?.is_timeout) &&
               !((caseData.monkeyOCRV2?.result?.html || caseData.monkeyOCRV2?.html || caseData.monkey_ocr?.result?.html || caseData.monkey_ocr?.html) || '').includes('MonkeyOCR文件上传失败')
    },
    vlLLM: {
      hasResult: !!(caseData.vlLLMResult?.result?.success && caseData.vlLLMResult?.result?.content),
      hasTable: (() => {
        // 检查VL-LLM返回的markdown文本是否包含表格
        const vlLLMContent = caseData.vlLLMResult?.result?.content?.choices?.[0]?.message?.content;
        console.log('🔍 VL-LLM表格检测:', {
          hasVlLLMResult: !!caseData.vlLLMResult,
          hasResult: !!caseData.vlLLMResult?.result,
          hasContent: !!caseData.vlLLMResult?.result?.content,
          hasChoices: !!caseData.vlLLMResult?.result?.content?.choices,
          contentSample: vlLLMContent?.substring(0, 100),
          contentType: typeof vlLLMContent
        });

        if (!vlLLMContent || typeof vlLLMContent !== 'string') return false;

        // 检查是否包含markdown表格标记
        const hasMarkdownTable = vlLLMContent.includes('|') && vlLLMContent.includes('---');
        console.log('🔍 VL-LLM表格检测结果:', hasMarkdownTable);
        return hasMarkdownTable;
      })()
    },
    monkeyOCRLocal: {
      hasResult: !!(caseData.monkeyOCRLocal?.results?.[0]?.result || caseData.monkeyOCRLocal?.result),
      hasTable: !!(caseData.monkeyOCRLocal?.results?.[0]?.result || caseData.monkeyOCRLocal?.result)
    },
    monkeyOcrKas: {
      hasResult: !!(caseData.monkeyOcrKas?.result?.content || caseData.monkeyOcrKas?.content),
      hasTable: !!(caseData.monkeyOcrKas?.result?.content || caseData.monkeyOcrKas?.content)
    },
    ocrflux: {
      hasResult: !!(caseData.ocrflux?.result?.content || caseData.ocrflux?.content),
      hasTable: !!(caseData.ocrflux?.result?.content || caseData.ocrflux?.content)
    }
  };

  return parserStatusMap[parserKey] || { hasResult: false, hasTable: false };
};

/**
 * 统一的解析器统计计算函数
 * @param {Array} cases - 案例数据
 * @param {Array} annotationData - 标注数据
 * @param {Array} enabledParsers - 启用的解析器列表，如果不提供则使用默认列表
 */
export const calculateParserStats = (cases, annotationData, enabledParsers = null) => {
  if (!cases.length) return null;

  const total = cases.length;
  const stats = {
    dataset: '',
    totalCases: total,
    hasAnnotationData: !!annotationData,
    parsers: {}
  };

  // 使用传入的启用解析器列表，或者使用默认列表
  const parserNames = enabledParsers || [
    'ocrflux',
    'vlLLM',
    'monkeyOCR',
    'monkeyOCRV2',
    'monkeyOCRLocal',
    'monkeyOcrKas',
    'kdcMarkdown',
    'kdcPlain',
    'kdcKdc'
  ];

  parserNames.forEach(parser => {
    stats.parsers[parser] = {
      name: getParserDisplayName(parser),
      totalCases: 0,
      successCases: 0,
      tableParsingCases: 0,
      validAccuracyCases: 0,
      totalAccuracy: 0,
      validTedsCount: 0,
      totalTeds: 0,
      totalTedsStructure: 0,
      totalTedsContent: 0,
      successRate: 0,
      tableParsingRate: 0,
      avgAccuracy: null,
      avgTeds: null,
      avgTedsStructure: null,
      avgTedsContent: null,
      details: []
    };
  });

  // 分析每个案例
  cases.forEach((caseData, index) => {
    // 修复：使用传入的annotationData参数查找匹配的annotation
    let caseAnnotation = caseData.annotation; // 优先使用案例自带的annotation
    if (!caseAnnotation && annotationData && Array.isArray(annotationData)) {
      caseAnnotation = annotationData.find(ann =>
        ann.image_filename === caseData.fileName
      );
    }

    try {
      const metricsResult = calculateMetricsForCase(caseData, caseAnnotation);
      if (!metricsResult) {
        logger.debug(`案例 ${index + 1} 指标计算失败，跳过`);
        return;
      }

      const { accuracies = {}, tedsScores = {}, tedsDetails = {} } = metricsResult;

      // 调试：检查指标计算结果
      logger.debug(`案例 ${index + 1} 指标计算结果:`, {
        hasAnnotation: !!caseAnnotation,
        accuraciesKeys: Object.keys(accuracies),
        tedsScoresKeys: Object.keys(tedsScores),
        ocrfluxAccuracy: accuracies.ocrflux,
        ocrfluxTeds: tedsScores.ocrflux
      });

      // 分析每个解析器在这个案例上的表现
      parserNames.forEach(parserKey => {
        const parser = stats.parsers[parserKey];
        const analysis = getParserResultStatus(caseData, parserKey);
        const accuracy = accuracies[parserKey];
        const teds = tedsScores[parserKey];
        const tedsDetail = tedsDetails[parserKey];
        const tedsStructure = tedsDetail?.structureAnalysis?.structureSimilarity;
        const tedsContent = tedsDetail?.contentAnalysis?.contentSimilarity;

        parser.totalCases++;

        // 记录详细信息
        const caseDetail = {
          caseIndex: index + 1,
          fileName: caseData.fileName,
          hasResult: analysis.hasResult,
          hasTable: analysis.hasTable,
          accuracy: accuracy,
          teds: teds,
          hasValidAccuracy: accuracy !== null && accuracy !== undefined && !isNaN(accuracy),
          hasValidTeds: teds !== null && teds !== undefined && !isNaN(teds),
          hasAnnotation: !!(caseAnnotation && (caseAnnotation.table_content || caseAnnotation.elements))
        };

        parser.details.push(caseDetail);

        // 更新统计
        if (analysis.hasResult) {
          parser.successCases++;
        }

        if (analysis.hasTable) {
          parser.tableParsingCases++;
        }

        // 只有当准确率是有效数字时才计入统计
        if (typeof accuracy === 'number' && !isNaN(accuracy)) {
          parser.validAccuracyCases++;
          parser.totalAccuracy += accuracy;
        }

        // 只有当TEDS是有效数字时才计入统计
        if (typeof teds === 'number' && !isNaN(teds)) {
          parser.validTedsCount++;
          parser.totalTeds += teds;

          // 添加TEDS结构和内容得分统计
          if (typeof tedsStructure === 'number' && !isNaN(tedsStructure)) {
            parser.totalTedsStructure += tedsStructure;
          }
          if (typeof tedsContent === 'number' && !isNaN(tedsContent)) {
            parser.totalTedsContent += tedsContent;
          }
        }
      });
    } catch (error) {
      logger.error(`案例 ${index + 1} 分析失败:`, error);
    }
  });

  // 计算最终比率
  Object.values(stats.parsers).forEach(parser => {
    parser.successRate = ((parser.successCases / parser.totalCases) * 100).toFixed(1);
    parser.tableParsingRate = ((parser.tableParsingCases / parser.totalCases) * 100).toFixed(1);
    parser.avgAccuracy = parser.validAccuracyCases > 0
      ? (parser.totalAccuracy / parser.validAccuracyCases).toFixed(1)
      : null;
    parser.avgTeds = parser.validTedsCount > 0
      ? (parser.totalTeds / parser.validTedsCount).toFixed(1)
      : null;
    parser.avgTedsStructure = parser.validTedsCount > 0 && typeof parser.totalTedsStructure === 'number'
      ? Number(parser.totalTedsStructure / parser.validTedsCount).toFixed(1)
      : null;
    parser.avgTedsContent = parser.validTedsCount > 0 && typeof parser.totalTedsContent === 'number'
      ? Number(parser.totalTedsContent / parser.validTedsCount).toFixed(1)
      : null;
  });

  // 调试：检查ocrflux的计算结果
  if (stats.parsers.ocrflux) {
    logger.debug('[calculateParserStats] OCR Flux统计结果:', {
      totalCases: stats.parsers.ocrflux.totalCases,
      successCases: stats.parsers.ocrflux.successCases,
      validAccuracyCases: stats.parsers.ocrflux.validAccuracyCases,
      totalAccuracy: stats.parsers.ocrflux.totalAccuracy,
      avgAccuracy: stats.parsers.ocrflux.avgAccuracy,
      validTedsCount: stats.parsers.ocrflux.validTedsCount,
      totalTeds: stats.parsers.ocrflux.totalTeds,
      avgTeds: stats.parsers.ocrflux.avgTeds
    });
  }

  return stats;
};

/**
 * 统一的指标计算函数
 * 计算准确率、TEDS等多种指标
 */
export const calculateMetricsForCase = (caseData, annotationData) => {
  // 重用现有的准确率计算逻辑
  const accuracyResult = calculateAccuracyForCase(caseData, annotationData);

  if (!accuracyResult) {
    return null;
  }

  const { baselineData, baselineText, accuracies = {}, texts = {} } = accuracyResult;

  logger.debug('[calculateMetricsForCase] 开始计算指标', {
    hasBaselineData: !!baselineData,
    hasBaselineText: !!baselineText,
    textsKeys: Object.keys(texts),
    textsCount: Object.keys(texts).length,
    accuraciesKeys: Object.keys(accuracies)
  });

  // 计算TEDS指标、详细分析和比对数据
  const tedsScores = {};
  const tedsDetails = {};
  const comparisonData = {};

  // 解析baselineData为表格格式
  let baselineTable = null;
  if (baselineData) {
    try {
      baselineTable = parseTableContent(baselineData);
      logger.error('[calculateMetricsForCase] 🔥 基准数据解析结果:', {
        hasBaselineTable: !!baselineTable,
        baselineTableType: typeof baselineTable,
        baselineTableLength: Array.isArray(baselineTable) ? baselineTable.length : 'not array',
        baselineDataType: typeof baselineData,
        baselineDataSample: baselineData ? JSON.stringify(baselineData).substring(0, 200) + '...' : 'null'
      });
    } catch (error) {
      logger.error('[calculateMetricsForCase] 🔥 基准数据解析失败:', error);
    }
  } else {
    logger.error('[calculateMetricsForCase] 🔥 没有基准数据，跳过处理');
  }

  if (baselineTable) {
    logger.error('[calculateMetricsForCase] 🔥 开始为每个解析器计算TEDS');
    // 为每个解析器计算TEDS、详细分析和比对数据
    Object.entries({
      kdcMarkdown: texts.kdcMarkdownText,
      kdcPlain: texts.kdcPlainText,
      kdcKdc: texts.kdcKdcText,
      monkeyOCR: texts.monkeyOCRText,
      monkeyOCRV2: texts.monkeyOCRV2Text,
      vlLLM: texts.vlLLMText,
      monkeyOCRLocal: texts.monkeyOCRLocalText,
      monkeyOcrKas: texts.monkeyOcrKasText,
      ocrflux: texts.ocrfluxText
    }).forEach(([parserKey, actualText]) => {
      logger.error(`[calculateMetricsForCase] 🔥 处理解析器 ${parserKey}`, {
        hasActualText: !!actualText,
        actualTextLength: actualText ? actualText.length : 0,
        actualTextSample: actualText ? actualText.substring(0, 100) : 'null'
      });

      if (actualText) {
        try {
          const actualTable = parseTableContent(actualText);
          logger.error(`[calculateMetricsForCase] 🔥 ${parserKey} 表格解析结果`, {
            hasActualTable: !!actualTable,
            actualTableType: typeof actualTable,
            actualTableLength: Array.isArray(actualTable) ? actualTable.length : 'not array'
          });

          // 计算TEDS指标
          tedsScores[parserKey] = calculateTEDS(baselineTable, actualTable);
          tedsDetails[parserKey] = calculateDetailedTEDS(baselineTable, actualTable);

          logger.error(`[calculateMetricsForCase] 🔥 ${parserKey} TEDS计算完成`, {
            tedsScore: tedsScores[parserKey],
            hasTedsDetails: !!tedsDetails[parserKey]
          });

          // 计算详细比对数据
          logger.error(`[calculateMetricsForCase] 🔥 准备计算比对数据 ${parserKey}`, {
            hasBaselineTable: !!baselineTable,
            hasActualTable: !!actualTable,
            baselineTableRows: Array.isArray(baselineTable) ? baselineTable.length : 'not array',
            actualTableRows: Array.isArray(actualTable) ? actualTable.length : 'not array'
          });

          if (baselineTable && actualTable) {
            comparisonData[parserKey] = getCompleteTableComparison(baselineTable, actualTable);
            logger.error(`[calculateMetricsForCase] 🔥 比对数据计算完成 ${parserKey}`, {
              hasComparisonData: !!comparisonData[parserKey]
            });
          } else {
            logger.error(`[calculateMetricsForCase] 🔥 跳过比对数据计算 ${parserKey}`, {
              reason: !baselineTable ? 'no baseline table' : 'no actual table'
            });
          }
        } catch (error) {
          logger.error(`[calculateMetricsForCase] 🔥 处理解析器 ${parserKey} 失败:`, error);
          tedsScores[parserKey] = 0;
          tedsDetails[parserKey] = { score: 0, hasData: false, error: error.message };
        }
      } else {
        logger.error(`[calculateMetricsForCase] 🔥 ${parserKey} 没有文本内容，跳过TEDS计算`);
      }
    });

    logger.error('[calculateMetricsForCase] 🔥 所有解析器TEDS计算完成', {
      tedsScoresKeys: Object.keys(tedsScores),
      tedsScoresValues: tedsScores,
      totalTedsCount: Object.keys(tedsScores).length
    });
  } else {
    logger.error('[calculateMetricsForCase] 🔥 没有基准表格数据，跳过所有TEDS计算');
  }

  // 检查是否解析出表格 - 复用统一的状态判断逻辑
  const hasTableResults = {};
  const parserKeys = ['kdcMarkdown', 'kdcPlain', 'kdcKdc', 'monkeyOCR', 'monkeyOCRV2', 'vlLLM', 'monkeyOCRLocal', 'monkeyOcrKas', 'ocrflux'];

  parserKeys.forEach(parserKey => {
    const status = getParserResultStatus(caseData, parserKey);
    hasTableResults[parserKey] = status.hasTable;
  });

  // 🔥 调试返回数据
  logger.error('[calculateMetricsForCase] 🔥 最终返回数据', {
    hasComparisonData: !!comparisonData,
    comparisonDataKeys: Object.keys(comparisonData || {}),
    comparisonDataSample: comparisonData,
    tedsScoresKeys: Object.keys(tedsScores || {}),
    tedsDetailsKeys: Object.keys(tedsDetails || {})
  });

  return {
    ...accuracyResult,
    tedsScores,
    tedsDetails,
    comparisonData,
    hasTableResults,
    hasAnnotationData: !!annotationData,
    baselineData: baselineTable // 返回解析后的表格数据用于比对
  };
};

/**
 * 获取解析器的显示名称
 * @deprecated 请使用 parserConfig.js 中的 getParserDisplayName
 */
export const getParserDisplayName = (parserKey) => {
  const nameMap = {
    ocrflux: 'OCR Flux',
    vlLLM: 'VL-LLM',
    monkeyOCR: 'MonkeyOCR(table)',
    monkeyOCRV2: 'MonkeyOCR(parse)',
    monkeyOCRLocal: 'MonkeyOCR(local)',
    monkeyOcrKas: 'MonkeyOCR（kas）',
    kdcMarkdown: 'KDC Markdown',
    kdcPlain: 'KDC Plain',
    kdcKdc: 'KDC KDC'
  };
  return nameMap[parserKey] || parserKey;
};

/**
 * 格式化指标值
 */
export const formatMetricValue = (value, type = 'percentage') => {
  if (value === null || value === undefined || isNaN(value)) return '-';

  switch (type) {
    case 'percentage':
      return `${(value || 0).toFixed(1)}%`;
    case 'boolean':
      return value ? '✓' : '✗';
    case 'number':
      return (value || 0).toFixed(2);
    default:
      return String(value);
  }
};

export const getParserKey = (parserName) => {
  const parserMap = {
    'OCR Flux': 'ocrflux',
    'VL LLM': 'vlLLM',
    'MonkeyOCR(table)': 'monkeyOCR',
    'MonkeyOCR(parse)': 'monkeyOCRV2',
    'MonkeyOCR(local)': 'monkeyOCRLocal',
    'MonkeyOCR（kas）': 'monkeyOcrKas',
    'KDC Markdown': 'kdcMarkdown',
    'KDC Plain': 'kdcPlain',
    'KDC KDC': 'kdcKdc'
  };
  return parserMap[parserName];
};

// 在开发环境下提供全局访问
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.calculateMetricsForCase = calculateMetricsForCase;
  window.parseTableContent = parseTableContent;
  window.calculateTEDS = calculateTEDS;
  window.calculateDetailedTEDS = calculateDetailedTEDS;

  console.log('🔧 TEDS调试函数已加载到全局作用域');
}
