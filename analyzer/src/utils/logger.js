/**
 * 日志管理工具
 * 用于控制前端日志输出，避免console窗口崩溃
 */

// 日志级别定义
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// 从环境变量或localStorage获取日志级别
const getLogLevel = () => {
  // 默认为WARN级别，减少日志输出避免console崩溃
  const defaultLevel = 'WARN';
  
  // 优先使用localStorage中的设置
  const storedLevel = localStorage.getItem('tablerag_log_level');
  if (storedLevel && LOG_LEVELS.hasOwnProperty(storedLevel)) {
    return storedLevel;
  }
  
  // 其次使用环境变量
  const envLevel = process.env.REACT_APP_LOG_LEVEL;
  if (envLevel && LOG_LEVELS.hasOwnProperty(envLevel)) {
    return envLevel;
  }
  
  return defaultLevel;
};

// 当前日志级别
let currentLogLevel = getLogLevel();

// 日志计数器，用于限制频繁日志
const logCounters = new Map();
const LOG_THROTTLE_LIMIT = 10; // 每个日志最多输出10次

/**
 * 检查是否应该输出日志
 */
const shouldLog = (level, message) => {
  // 检查日志级别
  if (LOG_LEVELS[level] > LOG_LEVELS[currentLogLevel]) {
    return false;
  }
  
  // 检查日志频率限制
  const key = `${level}:${message}`;
  const count = logCounters.get(key) || 0;
  
  if (count >= LOG_THROTTLE_LIMIT) {
    return false;
  }
  
  logCounters.set(key, count + 1);
  return true;
};

/**
 * 格式化日志消息
 */
const formatMessage = (prefix, message, ...args) => {
  const timestamp = new Date().toLocaleTimeString();
  return [`[${timestamp}] ${prefix}:`, message, ...args];
};

/**
 * 日志输出函数
 */
const logger = {
  /**
   * 错误日志 - 总是输出
   */
  error: (message, ...args) => {
    if (shouldLog('ERROR', message)) {
      console.error(...formatMessage('ERROR', message, ...args));
    }
  },

  /**
   * 警告日志
   */
  warn: (message, ...args) => {
    if (shouldLog('WARN', message)) {
      console.warn(...formatMessage('WARN', message, ...args));
    }
  },

  /**
   * 信息日志
   */
  info: (message, ...args) => {
    if (shouldLog('INFO', message)) {
      console.info(...formatMessage('INFO', message, ...args));
    }
  },

  /**
   * 调试日志 - 只在DEBUG级别输出
   */
  debug: (message, ...args) => {
    if (shouldLog('DEBUG', message)) {
      console.debug(...formatMessage('DEBUG', message, ...args));
    }
  },

  /**
   * 设置日志级别
   */
  setLevel: (level) => {
    if (LOG_LEVELS.hasOwnProperty(level)) {
      currentLogLevel = level;
      localStorage.setItem('tablerag_log_level', level);
      logger.debug(`日志级别已设置为: ${level}`);
    } else {
      logger.error(`无效的日志级别: ${level}. 可用级别: ${Object.keys(LOG_LEVELS).join(', ')}`);
    }
  },

  /**
   * 获取当前日志级别
   */
  getLevel: () => currentLogLevel,

  /**
   * 清除日志计数器
   */
  clearCounters: () => {
    logCounters.clear();
    logger.debug('日志计数器已清除');
  },

  /**
   * 获取日志统计信息
   */
  getStats: () => {
    const stats = {};
    for (const [key, count] of logCounters.entries()) {
      const [level] = key.split(':');
      stats[level] = (stats[level] || 0) + count;
    }
    return {
      totalMessages: logCounters.size,
      byLevel: stats,
      throttledMessages: Array.from(logCounters.entries())
        .filter(([, count]) => count >= LOG_THROTTLE_LIMIT)
        .length
    };
  }
};

// 在开发环境下提供全局访问
if (process.env.NODE_ENV === 'development') {
  window.tableragLogger = logger;
  
  // 提供快捷命令
  window.setLogLevel = logger.setLevel;
  window.getLogStats = logger.getStats;
  window.clearLogCounters = logger.clearCounters;
  
  logger.debug('🔧 TableRAG Logger 已加载');
  logger.debug('💡 使用 setLogLevel("DEBUG") 启用详细日志');
  logger.debug('💡 使用 setLogLevel("WARN") 减少日志输出');
  logger.debug('💡 使用 getLogStats() 查看日志统计');
  logger.debug('💡 使用 clearLogCounters() 重置日志计数');
}

export default logger;
