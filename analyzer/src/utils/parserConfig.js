/**
 * 解析器配置管理
 * 统一管理解析器的展示顺序、选择状态和相关配置
 */

// 解析器配置定义
export const PARSER_CONFIGS = [
  {
    key: 'mineru',
    displayName: 'Mineru Layout',
    dataPath: 'mineru',
    hasResultCheck: (caseData) => !!(caseData.mineru?.result?.layout_data),
    enabled: true,
    category: 'mineru'
  },
  {
    key: 'mineru_vlm',
    displayName: 'Mineru（VLM）',
    dataPath: 'mineru_vlm',
    hasResultCheck: (caseData) => !!(caseData.mineru_vlm?.result?.layout_data),
    enabled: true,
    category: 'mineru'
  },
  {
    key: 'ocrflux',
    displayName: 'OCR Flux',
    dataPath: 'ocrflux',
    hasResultCheck: (caseData) => !!(caseData.ocrflux?.result?.content || caseData.ocrflux?.content),
    enabled: true,
    category: 'ocr_flux'
  },
  {
    key: 'vlLLM',
    displayName: 'VL-LLM',
    dataPath: 'vlLLMResult',
    hasResultCheck: (caseData) => !!(caseData.vlLLMResult?.result),
    enabled: true,
    category: 'vl_llm'
  },
  {
    key: 'monkeyOCR',
    displayName: 'MonkeyOCR(table)',
    dataPath: 'monkeyOCR',
    hasResultCheck: (caseData) => !!(caseData.monkeyOCR?.result?.html || caseData.monkeyOCR?.html),
    enabled: true,
    category: 'monkey_ocr'
  },
  {
    key: 'monkeyOCRV2',
    displayName: 'MonkeyOCR(parse)',
    dataPath: 'monkeyOCRV2',
    hasResultCheck: (caseData) => !!(caseData.monkeyOCRV2?.result?.html || caseData.monkeyOCRV2?.html),
    enabled: true,
    category: 'monkey_ocr'
  },
  {
    key: 'monkeyOCRLocal',
    displayName: 'MonkeyOCR(local)',
    dataPath: 'monkeyOCRLocal',
    hasResultCheck: (caseData) => !!(caseData.monkeyOCRLocal?.results?.[0]?.result || caseData.monkeyOCRLocal?.result),
    enabled: true,
    category: 'monkey_ocr'
  },
  {
    key: 'monkeyOcrKas',
    displayName: 'MonkeyOCR（kas）',
    dataPath: 'monkeyOcrKas',
    hasResultCheck: (caseData) => !!(caseData.monkeyOcrKas?.result?.content || caseData.monkeyOcrKas?.content),
    enabled: true,
    category: 'monkey_ocr'
  },
  {
    key: 'kdcMarkdown',
    displayName: 'KDC Markdown',
    dataPath: 'kdcMarkdown',
    hasResultCheck: (caseData) => !!(caseData.kdcMarkdown?.result?.data?.[0]?.markdown || caseData.kdcMarkdown?.data?.[0]?.markdown),
    enabled: true,
    category: 'kdc'
  },
  {
    key: 'kdcPlain',
    displayName: 'KDC Plain',
    dataPath: 'kdcPlain',
    hasResultCheck: (caseData) => !!(caseData.kdcPlain?.result?.data?.[0]?.plain || caseData.kdcPlain?.data?.[0]?.plain),
    enabled: true,
    category: 'kdc'
  },
  {
    key: 'kdcKdc',
    displayName: 'KDC KDC',
    dataPath: 'kdcKdc',
    hasResultCheck: (caseData) => !!(caseData.kdcKdc?.result?.data?.[0]?.doc || caseData.kdcKdc?.data?.[0]?.doc),
    enabled: true,
    category: 'kdc'
  }
];

// 默认启用的解析器
export const DEFAULT_ENABLED_PARSERS = PARSER_CONFIGS.map(config => config.key);

// 解析器分类
export const PARSER_CATEGORIES = {
  monkey_ocr: 'MonkeyOCR系列',
  kdc: 'KDC系列',
  vl_llm: 'VL-LLM',
  ocr_flux: 'OCR Flux',
  mineru: 'Mineru Layout'
};

/**
 * 获取启用的解析器配置列表
 * @param {Array} enabledParsers - 启用的解析器key列表
 * @returns {Array} 启用的解析器配置列表
 */
export const getEnabledParserConfigs = (enabledParsers = DEFAULT_ENABLED_PARSERS) => {
  return PARSER_CONFIGS.filter(config => enabledParsers.includes(config.key));
};

/**
 * 获取有结果的解析器配置列表
 * @param {Object} caseData - 案例数据
 * @param {Array} enabledParsers - 启用的解析器key列表
 * @returns {Array} 有结果的解析器配置列表
 */
export const getAvailableParserConfigs = (caseData, enabledParsers = DEFAULT_ENABLED_PARSERS) => {
  if (!caseData) return [];
  
  return getEnabledParserConfigs(enabledParsers).filter(config => 
    config.hasResultCheck(caseData)
  );
};

/**
 * 获取解析器的显示名称
 * @param {string} parserKey - 解析器key
 * @returns {string} 显示名称
 */
export const getParserDisplayName = (parserKey) => {
  const config = PARSER_CONFIGS.find(c => c.key === parserKey);
  return config ? config.displayName : parserKey;
};

/**
 * 获取解析器的数据路径
 * @param {string} parserKey - 解析器key
 * @returns {string} 数据路径
 */
export const getParserDataPath = (parserKey) => {
  const config = PARSER_CONFIGS.find(c => c.key === parserKey);
  return config ? config.dataPath : parserKey;
};

/**
 * 检查解析器是否有结果
 * @param {Object} caseData - 案例数据
 * @param {string} parserKey - 解析器key
 * @returns {boolean} 是否有结果
 */
export const hasParserResult = (caseData, parserKey) => {
  const config = PARSER_CONFIGS.find(c => c.key === parserKey);
  return config ? config.hasResultCheck(caseData) : false;
};

/**
 * 根据解析器key获取解析器名称映射（用于兼容旧代码）
 * @param {string} parserName - 解析器显示名称
 * @returns {string} 解析器key
 */
export const getParserKey = (parserName) => {
  const nameToKeyMap = {
    'OCR Flux': 'ocrflux',
    'VL-LLM': 'vlLLM',
    'VL LLM': 'vlLLM',
    'MonkeyOCR(table)': 'monkeyOCR',
    'MonkeyOCR(parse)': 'monkeyOCRV2',
    'MonkeyOCR(local)': 'monkeyOCRLocal',
    'MonkeyOCR（kas）': 'monkeyOcrKas',
    'KDC Markdown': 'kdcMarkdown',
    'KDC Plain': 'kdcPlain',
    'KDC KDC': 'kdcKdc',
    'Mineru Layout': 'mineru',
    'Mineru（VLM）': 'mineru_vlm'
  };

  return nameToKeyMap[parserName] || parserName;
};
