const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // 代理静态文件请求到backend服务
  app.use(
    ['/dataset', '/parse_results'],
    createProxyMiddleware({
      target: 'http://localhost:8000',
      changeOrigin: true,
      logLevel: 'debug',
      pathRewrite: {
        '^/dataset': '/static/dataset',
        '^/parse_results': '/static/parse_results'
      }
    })
  );
};
