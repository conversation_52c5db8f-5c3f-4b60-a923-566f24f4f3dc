<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试解析报告问题</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .case-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .case-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .table-preview {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>调试解析报告问题</h1>
        <p>检查 complex 数据集第2个和第3个案例的详细比对表格问题</p>
        
        <button onclick="debugCase2()">调试第2个案例</button>
        <button onclick="debugCase3()">调试第3个案例</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        async function fetchWithErrorHandling(url) {
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    mode: 'cors'
                });
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('Fetch error:', error);
                throw error;
            }
        }
        
        async function debugCase2() {
            const resultsDiv = document.getElementById('results');
            const caseDiv = document.createElement('div');
            caseDiv.className = 'case-section';
            caseDiv.innerHTML = '<div class="case-title">第2个案例: 0088aad035efeb23181f497b987d26c2.png</div>';
            
            try {
                // 获取解析结果
                addDebugInfo(caseDiv, '正在获取解析结果...', 'warning');
                const parseResults = await fetchWithErrorHandling(`${API_BASE}/api/datasets/complex/parse_results`);
                
                if (!parseResults || parseResults.length < 2) {
                    addDebugInfo(caseDiv, '错误: 解析结果不足2个案例', 'error');
                    resultsDiv.appendChild(caseDiv);
                    return;
                }
                
                const case2 = parseResults[1]; // 第2个案例 (索引1)
                const filename = case2.fileName;
                addDebugInfo(caseDiv, `案例文件名: ${filename}`, 'success');
                
                // 获取标注数据
                addDebugInfo(caseDiv, '正在获取标注数据...', 'warning');
                const annotations = await fetchWithErrorHandling(`${API_BASE}/api/annotations?dataset_name=complex&limit=200`);
                
                const matchingAnnotation = annotations.find(ann => ann.image_filename === filename);
                if (matchingAnnotation) {
                    addDebugInfo(caseDiv, `找到标注数据: ID=${matchingAnnotation.id}, 类型=${matchingAnnotation.annotation_type}`, 'success');
                    
                    // 显示标注内容预览
                    const tableContent = JSON.parse(matchingAnnotation.table_content);
                    addDebugInfo(caseDiv, `标注内容类型: ${typeof tableContent}`, 'success');
                    addDebugInfo(caseDiv, `标注内容预览: ${JSON.stringify(tableContent).substring(0, 200)}...`, 'success');
                    
                    // 测试数据处理逻辑
                    await testDataProcessing(caseDiv, case2, matchingAnnotation);
                } else {
                    addDebugInfo(caseDiv, '错误: 未找到匹配的标注数据', 'error');
                }
                
            } catch (error) {
                addDebugInfo(caseDiv, `错误: ${error.message}`, 'error');
            }
            
            resultsDiv.appendChild(caseDiv);
        }
        
        async function debugCase3() {
            const resultsDiv = document.getElementById('results');
            const caseDiv = document.createElement('div');
            caseDiv.className = 'case-section';
            caseDiv.innerHTML = '<div class="case-title">第3个案例: 00cfd8cb5547db148ab4226b866b4164.png</div>';
            
            try {
                // 获取解析结果
                addDebugInfo(caseDiv, '正在获取解析结果...', 'warning');
                const parseResults = await fetchWithErrorHandling(`${API_BASE}/api/datasets/complex/parse_results`);
                
                if (!parseResults || parseResults.length < 3) {
                    addDebugInfo(caseDiv, '错误: 解析结果不足3个案例', 'error');
                    resultsDiv.appendChild(caseDiv);
                    return;
                }
                
                const case3 = parseResults[2]; // 第3个案例 (索引2)
                const filename = case3.fileName;
                addDebugInfo(caseDiv, `案例文件名: ${filename}`, 'success');
                
                // 获取标注数据
                addDebugInfo(caseDiv, '正在获取标注数据...', 'warning');
                const annotations = await fetchWithErrorHandling(`${API_BASE}/api/annotations?dataset_name=complex&limit=200`);
                
                const matchingAnnotation = annotations.find(ann => ann.image_filename === filename);
                if (matchingAnnotation) {
                    addDebugInfo(caseDiv, `找到标注数据: ID=${matchingAnnotation.id}, 类型=${matchingAnnotation.annotation_type}`, 'success');
                    
                    // 显示标注内容预览
                    const tableContent = JSON.parse(matchingAnnotation.table_content);
                    addDebugInfo(caseDiv, `标注内容类型: ${typeof tableContent}`, 'success');
                    addDebugInfo(caseDiv, `标注内容预览: ${JSON.stringify(tableContent).substring(0, 200)}...`, 'success');
                    
                    // 测试数据处理逻辑
                    await testDataProcessing(caseDiv, case3, matchingAnnotation);
                } else {
                    addDebugInfo(caseDiv, '错误: 未找到匹配的标注数据', 'error');
                    
                    // 显示所有标注文件名以便调试
                    const annotationFilenames = annotations.slice(0, 10).map(ann => ann.image_filename);
                    addDebugInfo(caseDiv, `前10个标注文件名: ${annotationFilenames.join(', ')}`, 'warning');
                }
                
            } catch (error) {
                addDebugInfo(caseDiv, `错误: ${error.message}`, 'error');
            }
            
            resultsDiv.appendChild(caseDiv);
        }
        
        async function testDataProcessing(caseDiv, caseData, annotationData) {
            addDebugInfo(caseDiv, '正在测试数据处理逻辑...', 'warning');
            
            try {
                // 模拟前端的数据处理逻辑
                const tableContent = JSON.parse(annotationData.table_content);
                
                // 检查是否为JSON Schema格式
                if (tableContent && typeof tableContent === 'object' && tableContent.elements) {
                    addDebugInfo(caseDiv, '标注数据为JSON Schema格式', 'success');
                    
                    // 查找表格元素
                    const tableElements = tableContent.elements.filter(el => el.type === 'table');
                    addDebugInfo(caseDiv, `找到 ${tableElements.length} 个表格元素`, 'success');
                    
                    if (tableElements.length > 0) {
                        const firstTable = tableElements[0];
                        addDebugInfo(caseDiv, `第一个表格: ${firstTable.rows.length} 行`, 'success');
                        
                        // 显示表格预览
                        const tablePreview = document.createElement('div');
                        tablePreview.className = 'table-preview';
                        tablePreview.innerHTML = '<strong>表格预览:</strong><br>' + 
                            JSON.stringify(firstTable.rows.slice(0, 3), null, 2);
                        caseDiv.appendChild(tablePreview);
                    }
                } else {
                    addDebugInfo(caseDiv, '标注数据不是JSON Schema格式', 'warning');
                }
                
                // 检查解析结果
                addDebugInfo(caseDiv, '检查解析结果...', 'warning');
                const parsers = ['kdcMarkdown', 'kdcPlain', 'kdcKdc', 'monkeyOCR', 'monkeyOCRV2', 'vlLLM'];
                
                for (const parser of parsers) {
                    if (caseData[parser]) {
                        const hasResult = !!(caseData[parser].result || caseData[parser].data);
                        addDebugInfo(caseDiv, `${parser}: ${hasResult ? '有结果' : '无结果'}`, hasResult ? 'success' : 'warning');
                    }
                }
                
            } catch (error) {
                addDebugInfo(caseDiv, `数据处理错误: ${error.message}`, 'error');
            }
        }
        
        function addDebugInfo(container, message, type = 'success') {
            const debugDiv = document.createElement('div');
            debugDiv.className = `debug-info ${type}`;
            debugDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(debugDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
