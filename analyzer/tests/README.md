# Analyzer Tests

前端分析界面测试套件，包含前后端连接测试、UI功能测试和准确率显示测试。

## 测试文件说明

### 前后端连接测试
- `debug_frontend_backend.py` - 前后端连接调试
- `debug_frontend_backend_mismatch.py` - 前后端数据不匹配调试

### UI功能测试
- `test_frontend_api.html` - 前端API调用测试页面
- `test_frontend_accuracy.html` - 前端准确率显示测试
- `test_accuracy_fixes.html` - 准确率修复测试页面

### 调试工具
- `debug_parse_report.html` - 解析报告调试页面
- `debug_accuracy.html` - 准确率调试页面
- `debug_accuracy.py` - 准确率调试脚本

## 运行测试

### 前端单元测试
```bash
cd analyzer
npm test
```

### 前端集成测试
```bash
# 启动开发服务器
npm start

# 在浏览器中打开测试页面
# http://localhost:3000/tests/test_frontend_api.html
# http://localhost:3000/tests/test_frontend_accuracy.html
```

### 前后端连接测试
```bash
# 确保后端服务运行在8000端口
python tests/debug_frontend_backend.py

# 调试数据不匹配问题
python tests/debug_frontend_backend_mismatch.py
```

### 浏览器测试
```bash
# 在浏览器中打开以下页面进行手动测试：
# - tests/test_frontend_api.html
# - tests/test_frontend_accuracy.html
# - tests/debug_parse_report.html
```

## 测试环境要求

### 服务依赖
- 后端服务运行在端口8000
- 前端开发服务器运行在端口3000
- SSH隧道正确配置 (远程开发环境)

### 浏览器要求
- Chrome >= 88
- Firefox >= 85
- Safari >= 14
- Edge >= 88

### 测试数据
- 至少一个数据集包含解析结果
- 标注数据存在 (用于准确率测试)

## 测试内容

### API连接测试
- 数据集列表获取
- 解析结果获取
- 图片文件访问
- 标注数据获取

### UI功能测试
- 数据集切换
- 案例列表显示
- 图片预览功能
- 表格渲染
- 准确率计算显示

### 性能测试
- 大数据集加载
- 虚拟滚动
- 图片懒加载
- 内存使用

## 调试指南

### 前后端连接问题
1. 检查后端服务状态: `curl http://localhost:8000/health`
2. 检查CORS配置
3. 检查API路径是否正确
4. 查看浏览器控制台错误

### 数据显示问题
1. 检查数据格式是否正确
2. 验证解析结果文件存在
3. 确认图片路径正确
4. 检查标注数据格式

### 性能问题
1. 使用React DevTools分析
2. 检查网络请求
3. 监控内存使用
4. 优化渲染逻辑

## 注意事项

1. 远程开发环境需要SSH隧道
2. 某些测试需要真实数据
3. 浏览器缓存可能影响测试结果
4. 网络延迟可能影响API测试
5. 大数据集测试需要耐心等待
