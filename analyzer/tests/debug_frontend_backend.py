#!/usr/bin/env python3
"""
调试前端和后端连接问题
"""

import requests
import json
import time

def test_backend_api():
    """测试后端API"""
    print("🔍 测试后端API连接")
    print("=" * 40)
    
    try:
        # 1. 测试基础连接
        print("1. 测试基础连接...")
        response = requests.get("http://localhost:8000/", timeout=5)
        print(f"   后端服务状态: {response.status_code}")
        
        # 2. 测试数据集列表
        print("2. 测试数据集列表...")
        response = requests.get("http://localhost:8000/api/datasets", timeout=10)
        if response.status_code == 200:
            datasets = response.json()
            dataset_names = [d['name'] for d in datasets]
            print(f"   ✅ 数据集数量: {len(datasets)}")
            print(f"   Complex数据集存在: {'complex' in dataset_names}")
        else:
            print(f"   ❌ 数据集API失败: {response.status_code}")
        
        # 3. 测试标注数据API
        print("3. 测试标注数据API...")
        response = requests.get("http://localhost:8000/api/annotations", 
                              params={"dataset_name": "complex", "limit": 5}, 
                              timeout=10)
        if response.status_code == 200:
            annotations = response.json()
            print(f"   ✅ 标注数据数量: {len(annotations)}")
            if len(annotations) > 0:
                print(f"   第一个标注文件: {annotations[0].get('image_filename')}")
        else:
            print(f"   ❌ 标注API失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
        
        # 4. 测试解析结果API
        print("4. 测试解析结果API...")
        response = requests.get("http://localhost:8000/api/datasets/complex/parse_results", timeout=10)
        if response.status_code == 200:
            parse_data = response.json()
            parse_results = parse_data.get('parse_results', {})
            if parse_results:
                first_parser = list(parse_results.keys())[0]
                first_parser_data = parse_results[first_parser]
                print(f"   ✅ 解析结果数量: {len(first_parser_data)}")
                if len(first_parser_data) > 0:
                    print(f"   第一个解析文件: {first_parser_data[0].get('filename')}")
            else:
                print(f"   ❌ 解析结果为空")
        else:
            print(f"   ❌ 解析结果API失败: {response.status_code}")
        
        # 5. 测试CORS
        print("5. 测试CORS配置...")
        response = requests.options("http://localhost:8000/api/annotations", 
                                  headers={
                                      "Origin": "http://localhost:3000",
                                      "Access-Control-Request-Method": "GET"
                                  }, timeout=5)
        print(f"   CORS预检请求状态: {response.status_code}")
        cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
        print(f"   CORS响应头: {cors_headers}")
        
        return True
        
    except Exception as e:
        print(f"❌ 后端API测试失败: {e}")
        return False

def check_frontend_status():
    """检查前端状态"""
    print("\n🔍 检查前端状态")
    print("=" * 40)
    
    try:
        # 检查前端是否运行
        response = requests.get("http://localhost:3000", timeout=5)
        print(f"前端服务状态: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 前端服务正常运行")
            print("💡 请在浏览器中打开 http://localhost:3000")
            print("💡 打开开发者工具的Console标签查看调试信息")
            print("💡 查看Network标签检查API请求是否成功")
        else:
            print("❌ 前端服务异常")
            
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")
        print("💡 请确保前端服务正在运行:")
        print("   cd analyzer && npm start")

def main():
    """主函数"""
    print("🚀 TableRAG 前后端连接调试")
    print("=" * 50)
    
    # 测试后端
    backend_ok = test_backend_api()
    
    # 检查前端
    check_frontend_status()
    
    print("\n📋 调试建议")
    print("=" * 20)
    
    if backend_ok:
        print("✅ 后端API工作正常")
        print("🔍 如果前端仍显示'无标注数据'，请检查:")
        print("   1. 浏览器开发者工具的Console是否有错误")
        print("   2. Network标签中API请求是否成功")
        print("   3. 前端代码中的调试信息输出")
        print("   4. 确保选择的是complex数据集")
        print("   5. 确保选择的案例文件名能匹配到标注数据")
    else:
        print("❌ 后端API有问题，请检查:")
        print("   1. 后端服务是否正在运行")
        print("   2. MySQL数据库是否正常连接")
        print("   3. 数据库中是否有标注数据")
    
    print(f"\n💡 下一步:")
    print("1. 打开 http://localhost:3000")
    print("2. 选择complex数据集")
    print("3. 选择任意案例")
    print("4. 查看浏览器控制台的调试信息")
    print("5. 如果仍有问题，请提供控制台的错误信息")

if __name__ == "__main__":
    main()
