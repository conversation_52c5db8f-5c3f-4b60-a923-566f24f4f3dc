#!/usr/bin/env python3
"""
调试前后端数据不一致问题
"""

import json
import requests

def get_frontend_data():
    """获取前端使用的数据"""
    try:
        # 获取图片列表（前端用这个来生成案例）
        response = requests.get('http://localhost:8000/api/datasets/complex/images')
        if response.status_code == 200:
            images = response.json()
            image_list = [img['filename'] for img in images]
            return image_list
        else:
            print(f"❌ 获取图片列表失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取图片列表异常: {e}")
        return []

def get_backend_data():
    """获取后端解析结果数据"""
    try:
        response = requests.get('http://localhost:8000/api/datasets/complex/parse_results')
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取解析结果失败: {response.status_code}")
            return {}
    except Exception as e:
        print(f"❌ 获取解析结果异常: {e}")
        return {}

def get_annotations():
    """获取标注数据"""
    try:
        response = requests.get('http://localhost:8000/api/annotations?dataset_name=complex&limit=200')
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取标注数据异常: {e}")
        return []

def compare_data():
    """比较前后端数据"""
    print("=== 比较前后端数据 ===")
    
    # 获取数据
    image_list = get_frontend_data()
    parse_data = get_backend_data()
    annotations = get_annotations()
    
    print(f"前端图片列表: {len(image_list)} 个")
    print(f"后端解析数据: {'有' if parse_data else '无'}")
    print(f"标注数据: {len(annotations)} 个")
    
    if not image_list or not parse_data or not annotations:
        print("❌ 数据不完整，无法比较")
        return
    
    # 显示前10个图片的顺序
    print(f"\n=== 前端图片列表顺序（前10个）===")
    for i, img in enumerate(image_list[:10]):
        print(f"  {i+1}. {img}")
    
    # 显示后端处理的文件顺序
    processed_files = parse_data.get('processed_files', [])
    print(f"\n=== 后端处理文件顺序（前10个）===")
    for i, file_info in enumerate(processed_files[:10]):
        original_image = file_info.get('original_image', 'N/A')
        pdf_filename = file_info.get('pdf_filename', 'N/A')
        print(f"  {i+1}. 原始: {original_image}, PDF: {pdf_filename}")
    
    # 检查第2个和第3个案例的具体情况
    print(f"\n=== 第2个和第3个案例对比 ===")
    
    if len(image_list) >= 2:
        frontend_case2 = image_list[1]
        print(f"前端第2个案例: {frontend_case2}")
        
        # 查找标注
        matching_ann = None
        for ann in annotations:
            ann_filename = ann.get('image_filename', '')
            ann_basename = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
            case_basename = frontend_case2.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
            
            if ann_filename == frontend_case2 or ann_basename == case_basename:
                matching_ann = ann
                break
        
        if matching_ann:
            print(f"  ✅ 前端第2个案例有标注: {matching_ann['image_filename']} (ID: {matching_ann['id']})")
        else:
            print(f"  ❌ 前端第2个案例无标注")
    
    if len(image_list) >= 3:
        frontend_case3 = image_list[2]
        print(f"前端第3个案例: {frontend_case3}")
        
        # 查找标注
        matching_ann = None
        for ann in annotations:
            ann_filename = ann.get('image_filename', '')
            ann_basename = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
            case_basename = frontend_case3.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
            
            if ann_filename == frontend_case3 or ann_basename == case_basename:
                matching_ann = ann
                break
        
        if matching_ann:
            print(f"  ✅ 前端第3个案例有标注: {matching_ann['image_filename']} (ID: {matching_ann['id']})")
        else:
            print(f"  ❌ 前端第3个案例无标注")
    
    # 显示标注文件名列表
    print(f"\n=== 标注文件名列表（前10个）===")
    for i, ann in enumerate(annotations[:10]):
        print(f"  {i+1}. {ann.get('image_filename', 'N/A')} (ID: {ann.get('id', 'N/A')})")

def check_specific_files():
    """检查特定文件的标注情况"""
    print(f"\n=== 检查特定文件的标注情况 ===")
    
    # 这些是浏览器中显示的文件名
    browser_files = [
        "0088aad035efeb23181f497b987d26c2.png",  # 浏览器第2个案例
        "00cfd8cb5547db148ab4226b866b4164.png"   # 浏览器第3个案例
    ]
    
    annotations = get_annotations()
    
    for filename in browser_files:
        print(f"\n检查文件: {filename}")
        
        # 查找标注
        matching_ann = None
        for ann in annotations:
            ann_filename = ann.get('image_filename', '')
            ann_basename = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
            case_basename = filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '').replace('.jpeg', '')
            
            if ann_filename == filename or ann_basename == case_basename:
                matching_ann = ann
                break
        
        if matching_ann:
            print(f"  ✅ 找到标注: {matching_ann['image_filename']} (ID: {matching_ann['id']})")
            
            # 检查标注内容
            try:
                table_content = json.loads(matching_ann['table_content'])
                if isinstance(table_content, dict) and 'elements' in table_content:
                    tables = [el for el in table_content['elements'] if el.get('type') == 'table']
                    print(f"  📊 标注包含 {len(tables)} 个表格")
                    if tables:
                        first_table = tables[0]
                        print(f"  📊 第一个表格: {len(first_table.get('rows', []))} 行")
                else:
                    print(f"  ⚠️ 标注格式不是JSON Schema")
            except Exception as e:
                print(f"  ❌ 解析标注内容失败: {e}")
        else:
            print(f"  ❌ 未找到标注")

def main():
    print("=== 调试前后端数据不一致问题 ===")
    
    compare_data()
    check_specific_files()
    
    print("\n=== 调试完成 ===")

if __name__ == '__main__':
    main()
