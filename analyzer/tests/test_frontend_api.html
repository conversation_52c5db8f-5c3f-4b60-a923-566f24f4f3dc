<!DOCTYPE html>
<html>
<head>
    <title>测试前端API调用</title>
</head>
<body>
    <h1>测试前端API调用</h1>
    <div id="results"></div>

    <script>
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                console.log('🔍 测试前端API调用');
                resultsDiv.innerHTML += '<h2>测试前端API调用</h2>';
                
                // 1. 测试获取complex数据集标注数据
                console.log('1. 测试获取complex数据集标注数据...');
                resultsDiv.innerHTML += '<h3>1. 测试获取complex数据集标注数据</h3>';
                
                const response = await fetch('http://localhost:8000/api/annotations?dataset_name=complex&limit=5');
                console.log('API响应状态:', response.status);
                resultsDiv.innerHTML += `<p>API响应状态: ${response.status}</p>`;
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('返回数据:', data);
                    resultsDiv.innerHTML += `<p>返回标注数量: ${data.length}</p>`;
                    
                    if (data.length > 0) {
                        resultsDiv.innerHTML += '<h4>前3个标注数据:</h4>';
                        data.slice(0, 3).forEach((ann, i) => {
                            console.log(`标注 ${i+1}:`, ann);
                            resultsDiv.innerHTML += `
                                <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
                                    <p><strong>文件名:</strong> ${ann.image_filename}</p>
                                    <p><strong>数据集:</strong> ${ann.dataset_name}</p>
                                    <p><strong>状态:</strong> ${ann.status}</p>
                                    <p><strong>内容长度:</strong> ${ann.table_content ? ann.table_content.length : 0}</p>
                                </div>
                            `;
                        });
                    }
                    
                    // 2. 测试解析结果数据
                    console.log('2. 测试解析结果数据...');
                    resultsDiv.innerHTML += '<h3>2. 测试解析结果数据</h3>';
                    
                    const parseResponse = await fetch('http://localhost:8000/api/datasets/complex/parse_results');
                    console.log('解析结果API状态:', parseResponse.status);
                    resultsDiv.innerHTML += `<p>解析结果API状态: ${parseResponse.status}</p>`;
                    
                    if (parseResponse.ok) {
                        const parseData = await parseResponse.json();
                        const parseResults = parseData.parse_results;
                        const firstParser = Object.keys(parseResults)[0];
                        const firstParserData = parseResults[firstParser];
                        
                        console.log('解析结果数量:', firstParserData.length);
                        resultsDiv.innerHTML += `<p>解析结果数量: ${firstParserData.length}</p>`;
                        
                        // 3. 测试文件名匹配
                        console.log('3. 测试文件名匹配...');
                        resultsDiv.innerHTML += '<h3>3. 测试文件名匹配</h3>';
                        
                        // 创建标注文件名映射
                        const annotationMap = {};
                        data.forEach(ann => {
                            const basename = ann.image_filename.replace(/\.(pdf|png|jpg|jpeg)$/i, '');
                            annotationMap[basename] = ann;
                        });
                        
                        let matchCount = 0;
                        const testCases = firstParserData.slice(0, 10);
                        
                        resultsDiv.innerHTML += '<h4>前10个案例匹配情况:</h4>';
                        
                        testCases.forEach((caseData, i) => {
                            const filename = caseData.filename;
                            const basename = filename.replace(/\.(pdf|png|jpg|jpeg)$/i, '');
                            
                            if (annotationMap[basename]) {
                                matchCount++;
                                const ann = annotationMap[basename];
                                console.log(`✅ 案例 ${i+1} 匹配:`, filename, '->', ann.image_filename);
                                resultsDiv.innerHTML += `<p style="color: green;">✅ 案例 ${i+1}: ${filename} -> ${ann.image_filename}</p>`;
                            } else {
                                console.log(`❌ 案例 ${i+1} 无匹配:`, filename);
                                resultsDiv.innerHTML += `<p style="color: red;">❌ 案例 ${i+1}: ${filename} -> 无匹配</p>`;
                            }
                        });
                        
                        const matchRate = (matchCount / testCases.length) * 100;
                        console.log(`匹配率: ${matchCount}/${testCases.length} (${matchRate.toFixed(1)}%)`);
                        resultsDiv.innerHTML += `<p><strong>匹配率: ${matchCount}/${testCases.length} (${matchRate.toFixed(1)}%)</strong></p>`;
                        
                        if (matchCount > 0) {
                            resultsDiv.innerHTML += '<p style="color: green; font-weight: bold;">✅ 前端API调用正常，文件匹配逻辑工作正常！</p>';
                        } else {
                            resultsDiv.innerHTML += '<p style="color: red; font-weight: bold;">❌ 文件匹配失败，需要进一步调试</p>';
                        }
                    }
                } else {
                    const errorText = await response.text();
                    console.error('API错误:', errorText);
                    resultsDiv.innerHTML += `<p style="color: red;">API错误: ${errorText}</p>`;
                }
                
            } catch (error) {
                console.error('测试失败:', error);
                resultsDiv.innerHTML += `<p style="color: red;">测试失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载后自动运行测试
        window.onload = testAPI;
    </script>
</body>
</html>
