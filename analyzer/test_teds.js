// 简单的TEDS计算测试脚本
// 在浏览器控制台中运行此脚本来测试TEDS计算

// 测试数据
const testAnnotation = {
  "elements": [
    {
      "type": "table",
      "style": "wireless",
      "rows": [
        [
          {
            "content": "类别",
            "rowspan": 1,
            "colspan": 1
          },
          {
            "content": "调整前",
            "rowspan": 1,
            "colspan": 1
          },
          {
            "content": "调整金额",
            "rowspan": 1,
            "colspan": 1
          },
          {
            "content": "调整后",
            "rowspan": 1,
            "colspan": 1
          }
        ],
        [
          {
            "content": "资产",
            "rowspan": 1,
            "colspan": 4
          }
        ],
        [
          {
            "content": "以公允价值计量且其变动计入当期损益的金融投资",
            "rowspan": 1,
            "colspan": 1
          },
          {
            "content": "750,000",
            "rowspan": 1,
            "colspan": 1
          },
          {
            "content": "450",
            "rowspan": 1,
            "colspan": 1
          },
          {
            "content": "750,450",
            "rowspan": 1,
            "colspan": 1
          }
        ]
      ]
    }
  ]
};

const testParseResult = `
| 类别 | 调整前 | 调整金额 | 调整后 |
| --- | --- | --- | --- |
| 资产 |  |  |  |
| 以公允价值计量且其变动计入当期损益的金融投资 | 750,000 | 450 | 750,450 |
`;

// 测试函数
function testTEDSCalculation() {
  console.log('🔥 开始测试TEDS计算...');
  
  // 模拟案例数据
  const mockCaseData = {
    fileName: 'test_case.png',
    vlLLMResult: {
      result: testParseResult
    }
  };
  
  try {
    // 调用calculateMetricsForCase函数
    const result = window.calculateMetricsForCase(mockCaseData, testAnnotation);
    
    console.log('🔥 TEDS计算结果:', result);
    console.log('🔥 TEDS分数:', result.tedsScores);
    console.log('🔥 TEDS详情:', result.tedsDetails);
    
    if (result.tedsScores && Object.keys(result.tedsScores).length > 0) {
      console.log('✅ TEDS计算成功！');
    } else {
      console.log('❌ TEDS计算失败 - 没有TEDS分数');
    }
    
  } catch (error) {
    console.error('❌ TEDS计算出错:', error);
  }
}

// 导出测试函数到全局作用域
window.testTEDSCalculation = testTEDSCalculation;

console.log('🔧 TEDS测试脚本已加载，运行 testTEDSCalculation() 来测试');
