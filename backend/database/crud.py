"""
数据库CRUD操作

提供数据库的增删改查操作
"""

from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from .models import Dataset, Image, Annotation, AccuracyEvaluation

class DatasetCRUD:
    """数据集CRUD操作"""
    
    @staticmethod
    def get_all(db: Session) -> List[Dataset]:
        """获取所有数据集"""
        return db.query(Dataset).all()
    
    @staticmethod
    def get_by_name(db: Session, name: str) -> Optional[Dataset]:
        """根据名称获取数据集"""
        return db.query(Dataset).filter(Dataset.name == name).first()
    
    @staticmethod
    def create(db: Session, name: str, description: str = None) -> Dataset:
        """创建数据集"""
        dataset = Dataset(name=name, description=description)
        db.add(dataset)
        db.commit()
        db.refresh(dataset)
        return dataset
    
    @staticmethod
    def delete(db: Session, dataset_id: int) -> bool:
        """删除数据集"""
        dataset = db.query(Dataset).filter(Dataset.id == dataset_id).first()
        if dataset:
            db.delete(dataset)
            db.commit()
            return True
        return False

class ImageCRUD:
    """图片CRUD操作"""

    @staticmethod
    def get_by_dataset(db: Session, dataset_id: int) -> List[Image]:
        """获取数据集的所有图片"""
        return db.query(Image).filter(Image.dataset_id == dataset_id).all()

    @staticmethod
    def get_by_id(db: Session, image_id: int) -> Optional[Image]:
        """根据ID获取图片"""
        return db.query(Image).filter(Image.id == image_id).first()

    @staticmethod
    def get_by_filename(db: Session, dataset_id: int, filename: str) -> Optional[Image]:
        """根据文件名获取图片"""
        return db.query(Image).filter(
            and_(Image.dataset_id == dataset_id, Image.filename == filename)
        ).first()
    
    @staticmethod
    def create(db: Session, dataset_id: int, filename: str, file_path: str) -> Image:
        """创建图片记录"""
        image = Image(dataset_id=dataset_id, filename=filename, file_path=file_path)
        db.add(image)
        db.commit()
        db.refresh(image)
        return image
    
    @staticmethod
    def bulk_create(db: Session, dataset_id: int, image_data: List[dict]) -> List[Image]:
        """批量创建图片记录"""
        images = []
        for data in image_data:
            image = Image(
                dataset_id=dataset_id,
                filename=data["filename"],
                file_path=data["file_path"]
            )
            images.append(image)
        
        db.add_all(images)
        db.commit()
        for image in images:
            db.refresh(image)
        return images

class AnnotationCRUD:
    """标注CRUD操作"""
    
    @staticmethod
    def get_all(db: Session, skip: int = 0, limit: int = 100) -> List[Annotation]:
        """获取所有标注"""
        return db.query(Annotation).offset(skip).limit(limit).all()
    
    @staticmethod
    def get_by_image(db: Session, image_id: int) -> List[Annotation]:
        """获取图片的所有标注"""
        return db.query(Annotation).filter(Annotation.image_id == image_id).all()
    
    @staticmethod
    def get_by_id(db: Session, annotation_id: int) -> Optional[Annotation]:
        """根据ID获取标注"""
        return db.query(Annotation).filter(Annotation.id == annotation_id).first()
    
    @staticmethod
    def create(db: Session, annotation_data: dict) -> Annotation:
        """创建标注"""
        annotation = Annotation(**annotation_data)
        db.add(annotation)
        db.commit()
        db.refresh(annotation)
        return annotation
    
    @staticmethod
    def update(db: Session, annotation_id: int, update_data: dict) -> Optional[Annotation]:
        """更新标注"""
        annotation = db.query(Annotation).filter(Annotation.id == annotation_id).first()
        if annotation:
            for key, value in update_data.items():
                setattr(annotation, key, value)
            db.commit()
            db.refresh(annotation)
            return annotation
        return None
    
    @staticmethod
    def delete(db: Session, annotation_id: int) -> bool:
        """删除标注"""
        annotation = db.query(Annotation).filter(Annotation.id == annotation_id).first()
        if annotation:
            db.delete(annotation)
            db.commit()
            return True
        return False

class EvaluationCRUD:
    """评估结果CRUD操作"""
    
    @staticmethod
    def get_by_image(db: Session, image_id: int) -> List[AccuracyEvaluation]:
        """获取图片的所有评估结果"""
        return db.query(AccuracyEvaluation).filter(AccuracyEvaluation.image_id == image_id).all()
    
    @staticmethod
    def get_by_dataset(db: Session, dataset_name: str) -> List[AccuracyEvaluation]:
        """获取数据集的所有评估结果"""
        return db.query(AccuracyEvaluation).join(Image).join(Dataset).filter(
            Dataset.name == dataset_name
        ).all()
    
    @staticmethod
    def create(db: Session, evaluation_data: dict) -> AccuracyEvaluation:
        """创建评估结果"""
        evaluation = AccuracyEvaluation(**evaluation_data)
        db.add(evaluation)
        db.commit()
        db.refresh(evaluation)
        return evaluation
    
    @staticmethod
    def bulk_create(db: Session, evaluations_data: List[dict]) -> List[AccuracyEvaluation]:
        """批量创建评估结果"""
        evaluations = []
        for data in evaluations_data:
            evaluation = AccuracyEvaluation(**data)
            evaluations.append(evaluation)
        
        db.add_all(evaluations)
        db.commit()
        for evaluation in evaluations:
            db.refresh(evaluation)
        return evaluations


