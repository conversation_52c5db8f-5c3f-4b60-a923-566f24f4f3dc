#!/usr/bin/env python3
"""
调试标注数据匹配问题
检查 complex 数据集的第2个和第3个案例的标注数据匹配情况
"""

import json
import requests
import sys
import os

def check_annotation_api():
    """检查标注API是否正常工作"""
    try:
        response = requests.get('http://localhost:8000/api/annotations?dataset_name=complex&limit=10')
        if response.status_code == 200:
            annotations = response.json()
            print(f"✅ API正常，获取到 {len(annotations)} 条标注数据")
            
            # 显示前几条标注数据的文件名
            for i, ann in enumerate(annotations[:5]):
                print(f"  {i+1}. {ann.get('image_filename', 'N/A')}")
            
            return annotations
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return None

def check_parse_results():
    """检查解析结果中的文件名"""
    try:
        with open('/data/projects/kingsoft/personal/workspace/tablerag/enhance/parse_results/complex/latest_results.json', 'r') as f:
            data = json.load(f)
        
        processed_files = data.get('processed_files', [])
        print(f"✅ 解析结果文件正常，包含 {len(processed_files)} 个文件")
        
        # 显示前几个文件的信息
        for i, file_info in enumerate(processed_files[:5]):
            original_image = file_info.get('original_image', 'N/A')
            pdf_filename = file_info.get('pdf_filename', 'N/A')
            print(f"  {i+1}. 原始图片: {original_image}, PDF: {pdf_filename}")
        
        return processed_files
    except Exception as e:
        print(f"❌ 读取解析结果失败: {e}")
        return None

def check_specific_cases():
    """检查第2个和第3个案例的具体情况"""
    print("\n=== 检查第2个和第3个案例 ===")
    
    # 获取解析结果
    parse_results = check_parse_results()
    if not parse_results:
        return
    
    # 获取标注数据
    annotations = check_annotation_api()
    if not annotations:
        return
    
    # 检查第2个案例 (索引1)
    if len(parse_results) > 1:
        case2 = parse_results[1]
        case2_filename = case2.get('original_image', '')
        print(f"\n第2个案例文件名: {case2_filename}")
        
        # 查找匹配的标注
        matching_annotations = []
        for ann in annotations:
            ann_filename = ann.get('image_filename', '')
            if ann_filename == case2_filename:
                matching_annotations.append(ann)
            elif ann_filename.replace('.png', '') == case2_filename.replace('.png', ''):
                matching_annotations.append(ann)
        
        print(f"找到 {len(matching_annotations)} 条匹配的标注数据")
        for ann in matching_annotations:
            print(f"  - ID: {ann.get('id')}, 文件名: {ann.get('image_filename')}, 类型: {ann.get('annotation_type')}")
    
    # 检查第3个案例 (索引2)
    if len(parse_results) > 2:
        case3 = parse_results[2]
        case3_filename = case3.get('original_image', '')
        print(f"\n第3个案例文件名: {case3_filename}")
        
        # 查找匹配的标注
        matching_annotations = []
        for ann in annotations:
            ann_filename = ann.get('image_filename', '')
            if ann_filename == case3_filename:
                matching_annotations.append(ann)
            elif ann_filename.replace('.png', '') == case3_filename.replace('.png', ''):
                matching_annotations.append(ann)
        
        print(f"找到 {len(matching_annotations)} 条匹配的标注数据")
        for ann in matching_annotations:
            print(f"  - ID: {ann.get('id')}, 文件名: {ann.get('image_filename')}, 类型: {ann.get('annotation_type')}")

def check_annotation_files():
    """检查标注文件是否存在"""
    print("\n=== 检查标注文件 ===")
    
    annotation_dir = '/data/projects/kingsoft/personal/workspace/tablerag/enhance/dataset/complex/annotations'
    if not os.path.exists(annotation_dir):
        print(f"❌ 标注目录不存在: {annotation_dir}")
        return
    
    annotation_files = [f for f in os.listdir(annotation_dir) if f.endswith('.json')]
    print(f"✅ 标注目录存在，包含 {len(annotation_files)} 个JSON文件")
    
    # 显示前几个文件
    for i, filename in enumerate(annotation_files[:5]):
        print(f"  {i+1}. {filename}")
    
    return annotation_files

def main():
    print("=== 调试标注数据匹配问题 ===")
    
    # 检查标注文件
    annotation_files = check_annotation_files()
    
    # 检查API
    annotations = check_annotation_api()
    
    # 检查解析结果
    parse_results = check_parse_results()
    
    # 检查具体案例
    check_specific_cases()
    
    print("\n=== 调试完成 ===")

if __name__ == '__main__':
    main()
