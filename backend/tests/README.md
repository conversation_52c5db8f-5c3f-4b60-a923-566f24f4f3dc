# Backend Tests

后端服务测试套件，包含API测试、数据库测试、准确率计算测试等。

## 测试文件说明

### 准确率计算测试
- `debug_accuracy_calculation.py` - 调试准确率计算问题
- `test_accuracy_comparison.py` - 测试准确率计算和比对功能
- `test_accuracy_fix.py` - 准确率修复测试
- `final_accuracy_test.py` - 最终准确率计算测试

### 标注功能测试
- `debug_annotation_matching.py` - 调试标注匹配问题
- `test_auto_annotation.py` - 自动标注功能测试
- `test_annotation_api.sh` - 标注API接口测试

### 数据处理测试
- `debug_detailed_comparison.py` - 调试详细比较功能
- `test_latest_results.py` - 最新结果测试
- `test_real_accuracy.py` - 真实准确率测试

### 集成测试
- `test_json_schema_integration.py` - JSON Schema集成测试
- `test_realtime_update.py` - 实时更新测试

## 运行测试

### 运行所有测试
```bash
cd backend
python -m pytest tests/ -v
```

### 运行特定测试
```bash
# 准确率计算测试
python tests/test_accuracy_comparison.py

# 标注功能测试
bash tests/test_annotation_api.sh

# 调试特定问题
python tests/debug_accuracy_calculation.py
```

### 测试环境要求
- 后端服务运行在端口8000
- MySQL数据库连接正常
- 测试数据集存在 (如kingsoft)

## 测试数据

测试使用的数据集：
- `kingsoft` - 主要测试数据集
- `test10` - 特定问题测试数据集

## 注意事项

1. 运行测试前确保后端服务已启动
2. 某些测试需要真实的解析结果数据
3. 调试脚本会输出详细的诊断信息
4. 集成测试可能需要较长时间完成
