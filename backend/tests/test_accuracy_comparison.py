#!/usr/bin/env python3
"""
测试准确率计算和比对功能
验证解析报告中的准确率计算是否正确地与annotation标注数据进行比对
"""

import requests
import json
import sys

def test_accuracy_calculation():
    """测试准确率计算功能"""
    print("🔍 测试准确率计算和比对功能")
    print("=" * 50)
    
    try:
        # 1. 获取数据集列表
        print("1. 获取数据集列表...")
        response = requests.get("http://localhost:8000/api/datasets", timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取数据集列表失败: {response.status_code}")
            return False
        
        datasets = response.json()
        print(f"✅ 找到数据集: {[d['name'] for d in datasets]}")
        
        # 使用complex数据集进行测试
        dataset_name = "complex"
        if not any(d['name'] == dataset_name for d in datasets):
            print(f"❌ 未找到测试数据集: {dataset_name}")
            return False
        
        # 2. 获取解析结果
        print(f"\n2. 获取{dataset_name}数据集的解析结果...")
        response = requests.get(f"http://localhost:8000/api/datasets/{dataset_name}/parse_results", timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取解析结果失败: {response.status_code}")
            return False
        
        parse_data = response.json()

        # 调试：打印完整的API响应结构
        print(f"   API响应类型: {type(parse_data)}")
        if isinstance(parse_data, dict):
            print(f"   API响应键: {list(parse_data.keys())}")

            # 尝试找到实际的案例数据
            if 'parse_results' in parse_data and isinstance(parse_data['parse_results'], dict):
                # 解析结果可能在parse_results字典中
                parse_results_dict = parse_data['parse_results']
                print(f"   parse_results键: {list(parse_results_dict.keys())}")

                # 对于complex数据集，数据结构是每个解析器有一个包含100个结果的列表
                # 需要重新组织数据结构

                # 获取第一个解析器的数据来确定文件数量
                first_parser = list(parse_results_dict.keys())[0]
                first_parser_data = parse_results_dict[first_parser]

                if isinstance(first_parser_data, list) and len(first_parser_data) > 0:
                    print(f"   第一个解析器 {first_parser} 有 {len(first_parser_data)} 个结果")

                    # 检查第一个结果的结构
                    first_result = first_parser_data[0]
                    print(f"   第一个结果的键: {list(first_result.keys()) if isinstance(first_result, dict) else 'N/A'}")

                    # 重新组织数据：为每个索引位置创建一个案例
                    case_data = []
                    for i in range(len(first_parser_data)):
                        # 从第一个解析器获取文件名
                        base_result = first_parser_data[i]
                        if isinstance(base_result, dict):
                            filename = base_result.get('fileName') or base_result.get('filename') or base_result.get('image_filename') or f"case_{i}.png"

                            case_item = {'fileName': filename}

                            # 从所有解析器收集该索引位置的结果
                            for parser_key, parser_data in parse_results_dict.items():
                                if isinstance(parser_data, list) and i < len(parser_data):
                                    case_item[parser_key] = parser_data[i]

                            case_data.append(case_item)

                    parse_results = case_data
                    print(f"   重新组织后的案例数量: {len(parse_results)}")
                else:
                    parse_results = []
            else:
                parse_results = []
        else:
            parse_results = []

        print(f"✅ 获取到{len(parse_results)}个解析结果")

        # 调试：打印解析结果的文件名
        print("   解析结果文件名:")
        for i, case in enumerate(parse_results):
            if isinstance(case, dict):
                print(f"     {i}: {case.get('fileName', 'N/A')}")
            else:
                print(f"     {i}: {type(case)} - {str(case)[:50]}")
        
        # 3. 获取标注数据
        print(f"\n3. 获取{dataset_name}数据集的标注数据...")
        response = requests.get("http://localhost:8000/api/annotations", 
                              params={"dataset_name": dataset_name}, timeout=10)
        if response.status_code != 200:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return False
        
        annotations = response.json()
        print(f"✅ 获取到{len(annotations)}个标注数据")

        # 调试：打印标注数据的文件名
        print("   标注数据文件名:")
        for i, ann in enumerate(annotations):
            print(f"     {i}: {ann.get('image_filename', 'N/A')}")
        
        if len(annotations) == 0:
            print("⚠️  没有标注数据，无法测试准确率计算")
            return True
        
        # 4. 测试准确率计算
        print(f"\n4. 测试准确率计算...")
        
        # 找到第一个有标注数据的案例
        test_case = None
        test_annotation = None
        
        for case in parse_results:
            # 处理case可能是字符串的情况
            if isinstance(case, str):
                continue

            case_filename = case.get('fileName', '')
            matching_annotation = None

            # 提取文件名的基础部分（去掉扩展名）
            case_basename = case_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '')

            for ann in annotations:
                ann_filename = ann.get('image_filename', '')
                ann_basename = ann_filename.replace('.pdf', '').replace('.png', '').replace('.jpg', '')

                # 比较基础文件名
                if ann_basename == case_basename:
                    matching_annotation = ann
                    break

            if matching_annotation:
                test_case = case
                test_annotation = matching_annotation
                break
        
        if not test_case or not test_annotation:
            print("❌ 未找到有标注数据的测试案例")
            return False
        
        print(f"✅ 找到测试案例: {test_case['fileName']}")
        print(f"   标注ID: {test_annotation['id']}")
        print(f"   标注类型: {test_annotation['annotation_type']}")
        print(f"   标注员: {test_annotation['annotator']}")
        
        # 5. 检查标注内容
        print(f"\n5. 检查标注内容...")
        table_content = test_annotation.get('table_content', '')
        if not table_content:
            print("❌ 标注数据中没有table_content")
            return False
        
        print(f"✅ 标注内容长度: {len(table_content)} 字符")
        
        # 尝试解析标注内容
        try:
            if table_content.startswith('{') or table_content.startswith('['):
                # JSON格式
                annotation_data = json.loads(table_content)
                print(f"✅ 标注内容为JSON格式")
                if isinstance(annotation_data, dict) and 'elements' in annotation_data:
                    print(f"   包含{len(annotation_data['elements'])}个元素")
                elif isinstance(annotation_data, list):
                    print(f"   包含{len(annotation_data)}个数组元素")
            else:
                # HTML或其他格式
                print(f"✅ 标注内容为文本格式")
                if '<table>' in table_content:
                    print("   包含HTML表格")
                elif '|' in table_content:
                    print("   包含Markdown表格")
        except json.JSONDecodeError:
            print("✅ 标注内容为非JSON格式")
        
        # 6. 检查解析结果
        print(f"\n6. 检查解析结果...")
        parsers_with_results = []
        
        # 检查各个解析器的结果
        if test_case.get('kdcMarkdown', {}).get('result', {}).get('data'):
            parsers_with_results.append('KDC Markdown')
        if test_case.get('kdcPlain', {}).get('result', {}).get('data'):
            parsers_with_results.append('KDC Plain')
        if test_case.get('kdcKdc', {}).get('result', {}).get('data'):
            parsers_with_results.append('KDC KDC')
        if test_case.get('monkeyOCR', {}).get('result', {}).get('html'):
            parsers_with_results.append('MonkeyOCR(table)')
        if test_case.get('monkeyOCRV2', {}).get('result', {}).get('html'):
            parsers_with_results.append('MonkeyOCR(parse)')
        if test_case.get('vlLLMResult', {}).get('result'):
            parsers_with_results.append('VL-LLM')
        if test_case.get('monkeyOCRLocal', {}).get('result'):
            parsers_with_results.append('MonkeyOCR(local)')
        if test_case.get('monkeyOcrKas', {}).get('result', {}).get('content'):
            parsers_with_results.append('MonkeyOCR（kas）')
        
        print(f"✅ 找到{len(parsers_with_results)}个有结果的解析器:")
        for parser in parsers_with_results:
            print(f"   - {parser}")
        
        # 7. 总结
        print(f"\n7. 测试总结")
        print("=" * 30)
        print(f"✅ 数据集: {dataset_name}")
        print(f"✅ 解析结果数量: {len(parse_results)}")
        print(f"✅ 标注数据数量: {len(annotations)}")
        print(f"✅ 测试案例: {test_case['fileName']}")
        print(f"✅ 有结果的解析器: {len(parsers_with_results)}个")
        print(f"✅ 标注内容可用: 是")
        
        print(f"\n💡 现在可以在analyzer界面中查看详细的准确率比对结果:")
        print(f"   1. 打开 http://localhost:3000")
        print(f"   2. 选择数据集: {dataset_name}")
        print(f"   3. 选择案例: {test_case['fileName']}")
        print(f"   4. 查看解析报告中的'与标注数据的详细比对'部分")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = test_accuracy_calculation()
    sys.exit(0 if success else 1)
