#!/usr/bin/env python3
"""
调试准确率计算

详细分析准确率计算过程
"""

import sys
import os
import json
import pandas as pd
from io import StringIO

# 添加backend路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def debug_table_parsing():
    """调试表格解析过程"""
    print("🔍 调试表格解析过程...")
    
    # HTML格式（标注数据）
    html_content = '''<table>
  <thead>
    <tr>
      <th>项目</th>
      <th>营业收入（万元）</th>
      <th>营业成本（万元）</th>
      <th>毛利润（万元）</th>
      <th>毛利率（%）</th>
      <th>净利润（万元）</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>主营业务收入</td>
      <td>12000</td>
      <td>8400</td>
      <td>3600</td>
      <td>30</td>
      <td>2100</td>
    </tr>
    <tr>
      <td>投资收益</td>
      <td>500</td>
      <td>200</td>
      <td>300</td>
      <td>60</td>
      <td>180</td>
    </tr>
    <tr>
      <td>其他业务收入</td>
      <td>800</td>
      <td>400</td>
      <td>400</td>
      <td>50</td>
      <td>280</td>
    </tr>
    <tr>
      <td>营业费用</td>
      <td>1000</td>
      <td>800</td>
      <td>200</td>
      <td>20</td>
      <td>150</td>
    </tr>
  </tbody>
</table>'''
    
    # Markdown格式（VL LLM结果）
    markdown_content = "| 项目         | 营业收入（万元） | 营业成本（万元） | 毛利润（万元） | 毛利率（%） | 净利润（万元） |\n|--------------|------------------|------------------|----------------|--------------|-----------------|\n| 主营业务收入 | 12000            | 8400             | 3600           | 30           | 2100            |\n| 投资收益     | 500              | 200              | 300            | 60           | 180             |\n| 其他业务收入 | 800              | 400              | 400            | 50           | 280             |\n| 营业费用     | 1000             | 800              | 200            | 20           | 150             |\n"
    
    print("HTML内容:")
    print(html_content[:200] + "...")
    print("\nMarkdown内容:")
    print(markdown_content[:200] + "...")
    
    # 解析HTML表格
    try:
        html_df = pd.read_html(StringIO(html_content))[0]
        print(f"\n✅ HTML解析成功: {html_df.shape}")
        print("HTML DataFrame:")
        print(html_df)
    except Exception as e:
        print(f"❌ HTML解析失败: {e}")
        html_df = None
    
    # 解析Markdown表格
    try:
        lines = [line.strip() for line in markdown_content.split('\n') if line.strip()]
        table_lines = [line for line in lines if line.startswith('|') and line.endswith('|')]
        
        print(f"\n找到 {len(table_lines)} 行表格数据")
        
        # 移除分隔符行
        import re
        table_lines = [line for line in table_lines if not re.match(r'^\|[\s\-\|]+\|$', line)]
        print(f"移除分隔符后: {len(table_lines)} 行")
        
        # 解析表格数据
        data = []
        for i, line in enumerate(table_lines):
            cells = [cell.strip() for cell in line.split('|')[1:-1]]
            print(f"第{i+1}行: {cells}")
            data.append(cells)
        
        if data:
            markdown_df = pd.DataFrame(data[1:], columns=data[0])
            print(f"\n✅ Markdown解析成功: {markdown_df.shape}")
            print("Markdown DataFrame:")
            print(markdown_df)
        else:
            markdown_df = None
            
    except Exception as e:
        print(f"❌ Markdown解析失败: {e}")
        markdown_df = None
    
    # 比较两个DataFrame
    if html_df is not None and markdown_df is not None:
        print(f"\n📊 比较结果:")
        print(f"HTML形状: {html_df.shape}")
        print(f"Markdown形状: {markdown_df.shape}")
        
        # 检查列名
        print(f"HTML列名: {list(html_df.columns)}")
        print(f"Markdown列名: {list(markdown_df.columns)}")
        
        # 检查数据内容
        if html_df.shape == markdown_df.shape:
            print("✅ 形状匹配")
            
            # 比较每个单元格
            matches = 0
            total = html_df.shape[0] * html_df.shape[1]
            
            for i in range(html_df.shape[0]):
                for j in range(html_df.shape[1]):
                    html_val = str(html_df.iloc[i, j]).strip()
                    md_val = str(markdown_df.iloc[i, j]).strip()
                    if html_val == md_val:
                        matches += 1
                    else:
                        print(f"不匹配 [{i},{j}]: '{html_val}' vs '{md_val}'")
            
            accuracy = matches / total if total > 0 else 0
            print(f"单元格匹配率: {matches}/{total} = {accuracy:.4f}")
        else:
            print("❌ 形状不匹配")

def main():
    """主函数"""
    print("🔍 开始调试准确率计算\n")
    debug_table_parsing()

if __name__ == "__main__":
    main()
