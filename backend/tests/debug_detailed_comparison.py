#!/usr/bin/env python3
"""
详细调试解析报告中的单元格比对问题
"""

import json
import requests
import sys
import os

def load_parse_results():
    """加载解析结果"""
    try:
        with open('/data/projects/kingsoft/personal/workspace/tablerag/enhance/parse_results/complex/latest_results.json', 'r') as f:
            data = json.load(f)

        # 检查数据结构
        if 'parse_results' in data:
            # 新格式：按解析器分组
            parse_results = data['parse_results']
            processed_files = data.get('processed_files', [])

            # 重组数据：每个文件一个条目
            results = []
            for i, file_info in enumerate(processed_files):
                filename = file_info.get('pdf_filename', '').replace('.pdf', '.png')
                case_data = {'fileName': filename}

                # 添加各个解析器的结果
                for parser_name, parser_results in parse_results.items():
                    if i < len(parser_results):
                        case_data[parser_name.replace('_', '')] = parser_results[i]

                results.append(case_data)

            return results
        else:
            # 旧格式
            return data.get('results', [])
    except Exception as e:
        print(f"❌ 加载解析结果失败: {e}")
        return []

def get_annotations():
    """获取标注数据"""
    try:
        response = requests.get('http://localhost:8000/api/annotations?dataset_name=complex&limit=200')
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取标注数据失败: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ 获取标注数据异常: {e}")
        return []

def parse_json_schema_table(json_data):
    """解析JSON Schema格式的表格数据为二维数组"""
    if not json_data or not isinstance(json_data, dict):
        return None
    
    elements = json_data.get('elements', [])
    tables = [el for el in elements if el.get('type') == 'table']
    
    if not tables:
        return None
    
    # 使用第一个表格
    table = tables[0]
    rows = table.get('rows', [])
    
    # 转换为二维数组
    result = []
    for row in rows:
        result_row = []
        for cell in row:
            content = cell.get('content', '')
            result_row.append(content)
        result.append(result_row)
    
    return result

def parse_table_content(text):
    """解析表格内容"""
    if not text:
        return None
    
    # 尝试解析HTML表格
    if '<table' in text.lower():
        # 简单的HTML表格解析
        import re
        rows = re.findall(r'<tr[^>]*>(.*?)</tr>', text, re.DOTALL | re.IGNORECASE)
        result = []
        for row in rows:
            cells = re.findall(r'<t[hd][^>]*>(.*?)</t[hd]>', row, re.DOTALL | re.IGNORECASE)
            # 清理HTML标签
            clean_cells = []
            for cell in cells:
                clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                clean_cells.append(clean_cell)
            if clean_cells:
                result.append(clean_cells)
        return result if result else None
    
    # 尝试解析Markdown表格
    if '|' in text:
        lines = text.strip().split('\n')
        result = []
        for line in lines:
            if '|' in line and not line.strip().startswith('|---'):
                cells = [cell.strip() for cell in line.split('|')]
                # 移除首尾空元素
                if cells and cells[0] == '':
                    cells = cells[1:]
                if cells and cells[-1] == '':
                    cells = cells[:-1]
                if cells:
                    result.append(cells)
        return result if result else None
    
    return None

def compare_tables(baseline_table, actual_table):
    """比对两个表格"""
    if not baseline_table or not actual_table:
        return None
    
    max_rows = max(len(baseline_table), len(actual_table))
    max_cols = max(
        max(len(row) for row in baseline_table) if baseline_table else 0,
        max(len(row) for row in actual_table) if actual_table else 0
    )
    
    cells = []
    correct_count = 0
    total_count = 0
    
    for row in range(max_rows):
        for col in range(max_cols):
            expected_content = ''
            actual_content = ''
            
            if row < len(baseline_table) and col < len(baseline_table[row]):
                expected_content = str(baseline_table[row][col]).strip()
            
            if row < len(actual_table) and col < len(actual_table[row]):
                actual_content = str(actual_table[row][col]).strip()
            
            if expected_content or actual_content:
                is_correct = expected_content == actual_content
                if is_correct:
                    correct_count += 1
                total_count += 1
                
                cells.append({
                    'row': row + 1,
                    'col': col + 1,
                    'expected_content': expected_content,
                    'actual_content': actual_content,
                    'is_correct': is_correct,
                    'is_empty': not expected_content and not actual_content
                })
    
    accuracy = (correct_count / total_count * 100) if total_count > 0 else 0
    
    return {
        'cells': cells,
        'total_cells': total_count,
        'correct_cells': correct_count,
        'incorrect_cells': total_count - correct_count,
        'accuracy': round(accuracy, 1)
    }

def debug_case(case_index, case_name):
    """调试指定案例"""
    print(f"\n=== 调试{case_name} ===")
    
    # 获取解析结果
    parse_results = load_parse_results()
    if len(parse_results) <= case_index:
        print(f"❌ 解析结果不足 {case_index + 1} 个案例")
        return
    
    case_data = parse_results[case_index]
    filename = case_data.get('fileName', '')
    print(f"📁 文件名: {filename}")
    
    # 获取标注数据
    annotations = get_annotations()
    matching_annotation = None
    for ann in annotations:
        if ann.get('image_filename') == filename:
            matching_annotation = ann
            break
    
    if not matching_annotation:
        print(f"❌ 未找到匹配的标注数据")
        return
    
    print(f"✅ 找到标注数据: ID={matching_annotation['id']}, 类型={matching_annotation['annotation_type']}")
    
    # 解析标注数据
    try:
        table_content = json.loads(matching_annotation['table_content'])
        baseline_table = parse_json_schema_table(table_content)
        print(f"📊 标注表格: {len(baseline_table)} 行" if baseline_table else "❌ 标注表格解析失败")
        
        if baseline_table:
            print(f"   第一行: {baseline_table[0]}")
    except Exception as e:
        print(f"❌ 解析标注数据失败: {e}")
        return
    
    # 检查各个解析器的结果
    parsers = {
        'kdcmarkdown': 'KDC Markdown',
        'kdcplain': 'KDC Plain',
        'kdckdc': 'KDC KDC',
        'monkeyocr': 'MonkeyOCR (table)',
        'monkeyocrlatex': 'MonkeyOCR (parse)',
        'vlllm': 'VL LLM'
    }
    
    for parser_key, parser_name in parsers.items():
        if parser_key in case_data:
            parser_data = case_data[parser_key]
            
            # 提取文本内容
            actual_text = ''
            if parser_key == 'kdcMarkdown':
                actual_text = parser_data.get('result', {}).get('data', [{}])[0].get('markdown', '')
            elif parser_key == 'kdcPlain':
                actual_text = parser_data.get('result', {}).get('data', [{}])[0].get('plain', '')
            elif parser_key == 'kdcKdc':
                actual_text = parser_data.get('result', {}).get('data', [{}])[0].get('kdc', '')
            elif parser_key == 'monkeyOCR':
                actual_text = parser_data.get('result', {}).get('html', '') or parser_data.get('html', '')
            elif parser_key == 'monkeyOCRV2':
                actual_text = parser_data.get('result', {}).get('html', '') or parser_data.get('html', '')
            elif parser_key == 'vlLLM':
                actual_text = parser_data.get('result', '')
            
            print(f"\n🔍 {parser_name}:")
            print(f"   有文本: {'是' if actual_text else '否'}")
            
            if actual_text:
                print(f"   文本长度: {len(actual_text)}")
                print(f"   文本预览: {actual_text[:100]}...")
                
                # 解析表格
                actual_table = parse_table_content(actual_text)
                print(f"   解析表格: {'成功' if actual_table else '失败'}")
                
                if actual_table and baseline_table:
                    print(f"   实际表格: {len(actual_table)} 行")
                    
                    # 进行比对
                    comparison = compare_tables(baseline_table, actual_table)
                    if comparison:
                        print(f"   比对结果: {comparison['correct_cells']}/{comparison['total_cells']} 正确 ({comparison['accuracy']}%)")
                        
                        # 显示前几个不匹配的单元格
                        incorrect_cells = [cell for cell in comparison['cells'] if not cell['is_correct']][:3]
                        if incorrect_cells:
                            print(f"   前3个错误:")
                            for cell in incorrect_cells:
                                print(f"     行{cell['row']}列{cell['col']}: 期望'{cell['expected_content']}' 实际'{cell['actual_content']}'")
                    else:
                        print(f"   比对失败")

def main():
    print("=== 详细调试解析报告问题 ===")
    
    # 调试第2个案例
    debug_case(1, "第2个案例")
    
    # 调试第3个案例  
    debug_case(2, "第3个案例")
    
    print("\n=== 调试完成 ===")

if __name__ == '__main__':
    main()
