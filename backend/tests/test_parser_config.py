#!/usr/bin/env python3
"""
测试解析器配置的脚本
验证PARSER_ENABLED_LIST环境变量是否正确工作
"""

import os
import sys
from pathlib import Path

# 添加parser/src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "parser" / "src"))

from core.parse_manager import Parse<PERSON>anager

def test_parser_config():
    """测试解析器配置"""
    print("🔍 测试解析器配置...")
    
    # 显示当前环境变量
    parser_enabled_list = os.getenv("PARSER_ENABLED_LIST", "未设置")
    print(f"📋 环境变量 PARSER_ENABLED_LIST: {parser_enabled_list}")
    
    # 创建解析管理器
    parse_manager = ParseManager()
    
    # 获取启用的解析器列表
    enabled_parsers = parse_manager.get_enabled_parsers()
    print(f"✅ 从环境变量读取的启用解析器: {enabled_parsers}")
    
    # 创建默认解析器配置
    parse_manager.create_default_parsers()
    
    # 获取已注册的解析器
    registered_parsers = parse_manager.list_parsers()
    print(f"📝 已注册的解析器: {registered_parsers}")
    
    # 获取可用的启用解析器
    available_enabled_parsers = parse_manager.get_available_enabled_parsers()
    print(f"🎯 可用的启用解析器: {available_enabled_parsers}")
    
    # 获取执行模式
    execution_mode = parse_manager.get_execution_mode()
    print(f"⚙️ 执行模式: {execution_mode}")
    
    # 如果是串行模式，显示执行顺序
    if execution_mode == "sequential":
        sequential_order = parse_manager.get_sequential_order()
        print(f"📊 串行执行顺序: {sequential_order}")
    
    print("\n" + "="*50)
    print("✅ 解析器配置测试完成")
    
    return available_enabled_parsers

def test_different_configs():
    """测试不同的配置"""
    print("\n🧪 测试不同的解析器配置...")
    
    # 测试配置1：只启用KDC解析器
    print("\n--- 测试配置1：只启用KDC解析器 ---")
    os.environ["PARSER_ENABLED_LIST"] = "kdc_markdown,kdc_plain"
    test_parser_config()
    
    # 测试配置2：只启用MonkeyOCR解析器
    print("\n--- 测试配置2：只启用MonkeyOCR解析器 ---")
    os.environ["PARSER_ENABLED_LIST"] = "monkey_ocr,monkey_ocr_latex"
    test_parser_config()
    
    # 测试配置3：启用本地MonkeyOCR
    print("\n--- 测试配置3：启用本地MonkeyOCR ---")
    os.environ["PARSER_ENABLED_LIST"] = "monkey_ocr_local"
    test_parser_config()
    
    # 恢复原始配置
    print("\n--- 恢复原始配置 ---")
    os.environ["PARSER_ENABLED_LIST"] = "kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,vl_llm"
    test_parser_config()

if __name__ == "__main__":
    # 首先加载配置文件
    config_file = Path(__file__).parent / "config.env"
    if config_file.exists():
        print(f"📁 加载配置文件: {config_file}")
        # 简单的配置文件解析
        with open(config_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('export ') and '=' in line:
                    # 解析 export VAR=value 格式
                    var_assignment = line[7:]  # 移除 'export '
                    if '=' in var_assignment:
                        key, value = var_assignment.split('=', 1)
                        # 移除引号
                        value = value.strip('"\'')
                        os.environ[key] = value
    else:
        print(f"⚠️ 配置文件不存在: {config_file}")
    
    # 运行基本测试
    test_parser_config()
    
    # 运行不同配置测试
    test_different_configs()
