# Backend API Reference

TableRAG Enhanced 后端API完整参考文档。

## 基础信息

- **Base URL**: `http://localhost:8000`
- **API版本**: v1
- **认证方式**: 无需认证 (开发环境)
- **数据格式**: JSON

## 通用响应格式

### 成功响应
```json
{
  "status": "success",
  "data": {...},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "status": "error",
  "error": "错误类型",
  "message": "错误描述",
  "details": {...}
}
```

## 数据集管理 API

### 获取数据集列表
```http
GET /api/datasets
```

**响应示例:**
```json
{
  "status": "success",
  "data": [
    {
      "name": "kingsoft",
      "image_count": 150,
      "has_annotations": true,
      "has_parse_results": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 获取数据集详情
```http
GET /api/datasets/{dataset_name}
```

### 创建数据集
```http
POST /api/datasets
Content-Type: application/json

{
  "name": "new_dataset",
  "description": "数据集描述"
}
```

### 删除数据集
```http
DELETE /api/datasets/{dataset_name}
```

## 图片管理 API

### 获取图片列表
```http
GET /api/datasets/{dataset_name}/images
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 50)
- `format`: 图片格式过滤

### 获取图片信息
```http
GET /api/datasets/{dataset_name}/images/{image_name}
```

### 上传图片
```http
POST /api/datasets/{dataset_name}/images
Content-Type: multipart/form-data

file: <image_file>
```

## 解析结果 API

### 获取解析结果
```http
GET /api/parse_results/{dataset_name}
```

**查询参数:**
- `latest`: 是否只返回最新结果 (默认: true)
- `parser`: 指定解析器类型

### 获取特定图片的解析结果
```http
GET /api/parse_results/{dataset_name}/{image_name}
```

### 触发解析任务
```http
POST /api/parse_results/{dataset_name}/parse
Content-Type: application/json

{
  "images": ["image1.jpg", "image2.jpg"],
  "parsers": ["kdc_markdown", "monkey_ocr"],
  "force_reparse": false
}
```

## 标注管理 API

### 获取标注列表
```http
GET /api/annotations?dataset_name={dataset_name}&limit={limit}
```

**查询参数**:
- `dataset_name`: 数据集名称
- `limit`: (可选) 返回结果数量限制，默认为100

**响应**:
```json
[
  {
    "id": 1,
    "dataset_name": "test20",
    "image_filename": "image1.png",
    "annotation_data": {...},
    "created_at": "2023-06-01T10:00:00",
    "updated_at": "2023-06-01T10:00:00"
  }
]
```

### 获取特定标注
```http
GET /api/annotations/{annotation_id}
```

**路径参数**:
- `annotation_id`: 标注ID

### 获取图片的所有标注
```http
GET /api/images/{image_id}/annotations
```

**路径参数**:
- `image_id`: 图片ID

### 创建标注
```http
POST /api/annotations
Content-Type: application/json

{
  "image_name": "test.jpg",
  "annotator": "user1",
  "table_structure": {
    "rows": 3,
    "cols": 4,
    "merged_cells": []
  },
  "table_content": "<table>...</table>",
  "content_format": "html",
  "status": "draft"
}
```

### 更新标注
```http
PUT /api/annotations/{annotation_id}
Content-Type: application/json

{
  "table_content": "<table>...</table>",
  "status": "completed"
}
```

### 删除标注
```http
DELETE /api/annotations/{annotation_id}
```

**路径参数**:
- `annotation_id`: 标注ID

### 批量创建标注
```http
POST /api/annotations/batch
Content-Type: application/json
```

### 自动生成标注
```http
POST /api/annotations/auto-generate/{dataset_name}
```

**路径参数**:
- `dataset_name`: 数据集名称

**查询参数**:
- `annotator`: (可选) 标注者名称，默认为"auto_generator"
- `overwrite`: (可选) 是否覆盖已有标注，默认为false

## 评估分析 API

### 获取准确率评估
```http
GET /api/evaluations/{dataset_name}
```

**查询参数:**
- `parser`: 指定解析器
- `metric`: 指定评估指标 (accuracy, precision, recall)

### 执行准确率计算
```http
POST /api/evaluations/{dataset_name}/calculate
Content-Type: application/json

{
  "images": ["image1.jpg"],
  "parsers": ["kdc_markdown", "monkey_ocr"],
  "metrics": ["accuracy", "teds"]
}
```

### 获取详细比较结果
```http
GET /api/evaluations/{dataset_name}/{image_name}/details
```

## 系统状态 API

### 健康检查
```http
GET /health
```

### 系统信息
```http
GET /api/system/info
```

### 服务状态
```http
GET /api/system/status
```

## 错误代码

| 代码 | 说明 |
|------|------|
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

## 使用示例

### Python客户端示例
```python
import requests

# 获取数据集列表
response = requests.get("http://localhost:8000/api/datasets")
datasets = response.json()["data"]

# 创建标注
annotation_data = {
    "image_name": "test.jpg",
    "annotator": "user1",
    "table_content": "<table><tr><td>Cell 1</td></tr></table>",
    "content_format": "html"
}
response = requests.post(
    "http://localhost:8000/api/annotations/kingsoft",
    json=annotation_data
)
```

### JavaScript客户端示例
```javascript
// 获取解析结果
fetch('http://localhost:8000/api/parse_results/kingsoft')
  .then(response => response.json())
  .then(data => console.log(data));

// 上传图片
const formData = new FormData();
formData.append('file', imageFile);
fetch('http://localhost:8000/api/datasets/kingsoft/images', {
  method: 'POST',
  body: formData
});
```

## 注意事项

1. 所有时间戳使用ISO 8601格式
2. 文件上传限制为10MB
3. API调用频率限制: 100次/分钟
4. 长时间运行的任务会返回任务ID，需要轮询状态
5. 某些操作需要足够的磁盘空间
