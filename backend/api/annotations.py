"""
标注功能API

提供人工标注数据的增删改查功能
"""

import json
from typing import List, Optional
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel

from backend.database.database import get_database
from backend.database.crud import AnnotationCRUD, ImageCRUD, DatasetCRUD
from backend.database.models import Annotation, Image, Dataset

router = APIRouter()

def find_json_schema_for_image(gen_data_dir: str, image_filename: str) -> Optional[dict]:
    """
    查找图片对应的JSON Schema数据

    Args:
        gen_data_dir: 生成数据目录
        image_filename: 图片文件名

    Returns:
        dict: JSON Schema数据，如果找不到返回None
    """
    import os

    # 尝试从多个可能的位置查找JSON Schema数据
    possible_paths = [
        # 在gen_data目录的上级目录查找
        os.path.join(os.path.dirname(gen_data_dir), 'llm_gen_data.json'),
        # 在generator目录查找
        os.path.join(os.path.dirname(gen_data_dir), '../../generator/data/llm_train_data/latest/llm_gen_data.json'),
        # 在当前目录查找
        os.path.join(gen_data_dir, 'llm_gen_data.json')
    ]

    for json_path in possible_paths:
        if os.path.exists(json_path):
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)

                if isinstance(json_data, list):
                    # 根据文件名查找对应的数据
                    base_name = os.path.splitext(image_filename)[0]

                    # 尝试通过索引匹配
                    file_number = None
                    import re
                    match = re.search(r'(\d+)$', base_name)
                    if match:
                        file_number = int(match.group(1))
                        if 1 <= file_number <= len(json_data):
                            return json_data[file_number - 1]

                    # 如果索引匹配失败，尝试其他方式
                    for item in json_data:
                        if isinstance(item, dict) and item.get('filename') == image_filename:
                            return item

                break

            except Exception as e:
                print(f"读取JSON Schema文件失败 {json_path}: {e}")
                continue

    return None

def find_annotation_data_for_image(dataset_name: str, image_filename: str) -> Optional[dict]:
    """
    查找图片对应的标注数据

    Args:
        dataset_name: 数据集名称
        image_filename: 图片文件名

    Returns:
        dict: 标注数据，如果找不到返回None
    """
    import os
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
    
    # 尝试从多个可能的位置查找标注数据
    possible_dirs = [
        # 数据集的annotations目录
        os.path.join(project_root, 'dataset', dataset_name, 'annotations'),
        # 数据集的gen_data目录
        os.path.join(project_root, 'dataset', dataset_name, 'gen_data'),
    ]
    
    # 提取文件的基本名称（不包含扩展名）
    base_name = os.path.splitext(image_filename)[0]
    
    for data_dir in possible_dirs:
        if not os.path.exists(data_dir):
            continue
            
        # 尝试不同的文件名格式
        possible_files = [
            os.path.join(data_dir, f"{base_name}.json"),
            os.path.join(data_dir, f"{image_filename}.json"),
            os.path.join(data_dir, 'llm_gen_data.json'),
        ]
        
        for json_path in possible_files:
            if os.path.exists(json_path):
                try:
                    with open(json_path, 'r', encoding='utf-8') as f:
                        json_data = json.load(f)
                    
                    # 如果是单个文件对应的JSON数据，直接返回
                    if json_path.endswith(f"{base_name}.json") or json_path.endswith(f"{image_filename}.json"):
                        return json_data
                    
                    # 如果是llm_gen_data.json，需要查找对应的条目
                    if isinstance(json_data, list):
                        # 尝试通过索引匹配
                        import re
                        match = re.search(r'(\d+)$', base_name)
                        if match:
                            file_number = int(match.group(1))
                            if 1 <= file_number <= len(json_data):
                                return json_data[file_number - 1]
                        
                        # 如果索引匹配失败，尝试其他方式
                        for item in json_data:
                            if isinstance(item, dict) and item.get('filename') == image_filename:
                                return item
                    
                    # 如果是单个对象且不是列表，直接返回
                    elif isinstance(json_data, dict):
                        return json_data
                        
                except Exception as e:
                    print(f"读取标注文件失败 {json_path}: {e}")
                    continue
        
        # 特殊处理：对于complex数据集，annotation文件是按照MD5哈希值命名的
        # 需要遍历所有annotation文件来查找匹配
        if dataset_name == 'complex' and data_dir.endswith('annotations'):
            try:
                # 遍历所有annotation文件
                for annotation_file in os.listdir(data_dir):
                    if annotation_file.endswith('.json'):
                        json_path = os.path.join(data_dir, annotation_file)
                        try:
                            with open(json_path, 'r', encoding='utf-8') as f:
                                json_data = json.load(f)
                            
                            # 对于complex数据集，annotation文件直接包含table数据
                            # 检查是否为有效的JSON Schema格式
                            if isinstance(json_data, dict) and 'elements' in json_data:
                                # 这里可能需要更复杂的匹配逻辑
                                # 暂时先返回第一个找到的有效annotation
                                # 实际应用中可能需要更精确的匹配方式
                                
                                # 尝试通过文件名模式匹配
                                # 如果图片文件名包含数字，可能与annotation文件有某种关联
                                import re
                                img_match = re.search(r'(\d+)', image_filename)
                                if img_match:
                                    img_number = img_match.group(1)
                                    # 这里可以添加更复杂的匹配逻辑
                                    # 暂时先返回找到的第一个有效annotation
                                    return json_data
                                
                        except Exception as e:
                            print(f"读取complex annotation文件失败 {json_path}: {e}")
                            continue
                            
            except Exception as e:
                print(f"遍历complex annotations目录失败: {e}")

    return None

class AnnotationCreate(BaseModel):
    """创建标注请求模型"""
    image_id: Optional[int] = None  # 图片数据库ID（可选）
    dataset_name: Optional[str] = None  # 数据集名称（与image_filename配合使用）
    image_filename: Optional[str] = None  # 图片文件名（与dataset_name配合使用）
    annotator: str
    table_structure: str  # JSON字符串
    table_content: str    # JSON Schema格式的表格内容
    annotation_type: str = "manual"  # manual/imported/json_schema
    status: str = "draft"

class AnnotationUpdate(BaseModel):
    """更新标注请求模型"""
    annotator: Optional[str] = None
    table_structure: Optional[str] = None
    table_content: Optional[str] = None
    annotation_type: Optional[str] = None
    status: Optional[str] = None

class AnnotationResponse(BaseModel):
    """标注响应模型"""
    id: int
    image_id: int
    annotator: str
    table_structure: str
    table_content: str
    annotation_type: str
    status: str
    created_at: str
    updated_at: str
    
    # 关联信息
    image_filename: Optional[str] = None
    dataset_name: Optional[str] = None

@router.get("/annotations", response_model=List[AnnotationResponse])
async def get_annotations(
    skip: int = 0,
    limit: int = 100,
    dataset_name: Optional[str] = None,
    status: Optional[str] = None,
    annotator: Optional[str] = None,
    db: Session = Depends(get_database)
):
    """
    获取标注列表
    
    支持按数据集、状态、标注员等条件筛选
    """
    try:
        # 基础查询
        query = db.query(Annotation)
        
        # 添加筛选条件
        if dataset_name:
            query = query.join(Annotation.image).join(Image.dataset).filter(
                Dataset.name == dataset_name
            )
        
        if status:
            query = query.filter(Annotation.status == status)
        
        if annotator:
            query = query.filter(Annotation.annotator == annotator)
        
        # 分页
        annotations = query.offset(skip).limit(limit).all()
        
        # 构建响应数据
        result = []
        for annotation in annotations:
            result.append(AnnotationResponse(
                id=annotation.id,
                image_id=annotation.image_id,
                annotator=annotation.annotator,
                table_structure=annotation.table_structure,
                table_content=annotation.table_content,
                annotation_type=annotation.annotation_type,
                status=annotation.status,
                created_at=annotation.created_at.isoformat(),
                updated_at=annotation.updated_at.isoformat(),
                image_filename=annotation.image.filename if annotation.image else None,
                dataset_name=annotation.image.dataset.name if annotation.image and annotation.image.dataset else None
            ))
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标注列表失败: {str(e)}")

@router.post("/annotations", response_model=AnnotationResponse)
async def create_annotation(annotation: AnnotationCreate, db: Session = Depends(get_database)):
    """创建新标注"""
    try:
        # 获取图片ID
        image_id = None
        if annotation.image_id:
            # 直接使用提供的图片ID
            image_id = annotation.image_id
            image = ImageCRUD.get_by_id(db, image_id)
            if not image:
                raise HTTPException(status_code=404, detail=f"图片不存在: {image_id}")
        elif annotation.dataset_name and annotation.image_filename:
            # 通过数据集名称和文件名查找图片
            dataset = DatasetCRUD.get_by_name(db, annotation.dataset_name)
            if not dataset:
                raise HTTPException(status_code=404, detail=f"数据集不存在: {annotation.dataset_name}")

            image = ImageCRUD.get_by_filename(db, dataset.id, annotation.image_filename)
            if not image:
                raise HTTPException(status_code=404, detail=f"图片不存在: {annotation.image_filename}")
            image_id = image.id
        else:
            raise HTTPException(status_code=400, detail="必须提供image_id或者dataset_name+image_filename")

        # 验证table_structure是否为有效JSON
        try:
            json.loads(annotation.table_structure)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="table_structure必须是有效的JSON字符串")

        # 创建标注数据
        annotation_data = {
            "image_id": image_id,
            "annotator": annotation.annotator,
            "table_structure": annotation.table_structure,
            "table_content": annotation.table_content,
            "annotation_type": annotation.annotation_type,
            "status": annotation.status
        }

        new_annotation = AnnotationCRUD.create(db, annotation_data)
        
        return AnnotationResponse(
            id=new_annotation.id,
            image_id=new_annotation.image_id,
            annotator=new_annotation.annotator,
            table_structure=new_annotation.table_structure,
            table_content=new_annotation.table_content,
            annotation_type=new_annotation.annotation_type,
            status=new_annotation.status,
            created_at=new_annotation.created_at.isoformat(),
            updated_at=new_annotation.updated_at.isoformat(),
            image_filename=new_annotation.image.filename,
            dataset_name=new_annotation.image.dataset.name
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建标注失败: {str(e)}")

@router.get("/annotations/{annotation_id}", response_model=AnnotationResponse)
async def get_annotation(annotation_id: int, db: Session = Depends(get_database)):
    """获取单个标注"""
    try:
        annotation = AnnotationCRUD.get_by_id(db, annotation_id)
        if not annotation:
            raise HTTPException(status_code=404, detail=f"标注不存在: {annotation_id}")
        
        return AnnotationResponse(
            id=annotation.id,
            image_id=annotation.image_id,
            annotator=annotation.annotator,
            table_structure=annotation.table_structure,
            table_content=annotation.table_content,
            annotation_type=annotation.annotation_type,
            status=annotation.status,
            created_at=annotation.created_at.isoformat(),
            updated_at=annotation.updated_at.isoformat(),
            image_filename=annotation.image.filename if annotation.image else None,
            dataset_name=annotation.image.dataset.name if annotation.image and annotation.image.dataset else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标注失败: {str(e)}")

@router.put("/annotations/{annotation_id}", response_model=AnnotationResponse)
async def update_annotation(
    annotation_id: int,
    annotation_update: AnnotationUpdate,
    db: Session = Depends(get_database)
):
    """更新标注"""
    try:
        # 构建更新数据
        update_data = {}
        if annotation_update.annotator is not None:
            update_data["annotator"] = annotation_update.annotator
        if annotation_update.table_structure is not None:
            # 验证JSON格式
            try:
                json.loads(annotation_update.table_structure)
                update_data["table_structure"] = annotation_update.table_structure
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="table_structure必须是有效的JSON字符串")
        if annotation_update.table_content is not None:
            update_data["table_content"] = annotation_update.table_content
        if annotation_update.annotation_type is not None:
            update_data["annotation_type"] = annotation_update.annotation_type
        if annotation_update.status is not None:
            update_data["status"] = annotation_update.status
        
        # 添加更新时间
        update_data["updated_at"] = datetime.now(timezone.utc)
        
        # 更新标注
        updated_annotation = AnnotationCRUD.update(db, annotation_id, update_data)
        if not updated_annotation:
            raise HTTPException(status_code=404, detail=f"标注不存在: {annotation_id}")
        
        return AnnotationResponse(
            id=updated_annotation.id,
            image_id=updated_annotation.image_id,
            annotator=updated_annotation.annotator,
            table_structure=updated_annotation.table_structure,
            table_content=updated_annotation.table_content,
            annotation_type=updated_annotation.annotation_type,
            status=updated_annotation.status,
            created_at=updated_annotation.created_at.isoformat(),
            updated_at=updated_annotation.updated_at.isoformat(),
            image_filename=updated_annotation.image.filename if updated_annotation.image else None,
            dataset_name=updated_annotation.image.dataset.name if updated_annotation.image and updated_annotation.image.dataset else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新标注失败: {str(e)}")

@router.delete("/annotations/{annotation_id}")
async def delete_annotation(annotation_id: int, db: Session = Depends(get_database)):
    """删除标注"""
    try:
        success = AnnotationCRUD.delete(db, annotation_id)
        if not success:
            raise HTTPException(status_code=404, detail=f"标注不存在: {annotation_id}")
        
        return {"message": f"标注 {annotation_id} 删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除标注失败: {str(e)}")

@router.get("/images/{image_id}/annotations", response_model=List[AnnotationResponse])
async def get_image_annotations(image_id: int, db: Session = Depends(get_database)):
    """获取图片的所有标注"""
    try:
        annotations = AnnotationCRUD.get_by_image(db, image_id)
        
        result = []
        for annotation in annotations:
            result.append(AnnotationResponse(
                id=annotation.id,
                image_id=annotation.image_id,
                annotator=annotation.annotator,
                table_structure=annotation.table_structure,
                table_content=annotation.table_content,
                annotation_type=annotation.annotation_type,
                status=annotation.status,
                created_at=annotation.created_at.isoformat(),
                updated_at=annotation.updated_at.isoformat(),
                image_filename=annotation.image.filename if annotation.image else None,
                dataset_name=annotation.image.dataset.name if annotation.image and annotation.image.dataset else None
            ))
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图片标注失败: {str(e)}")


class BatchAnnotationCreate(BaseModel):
    """批量创建标注请求模型"""
    dataset_name: str
    annotations: List[AnnotationCreate]
    overwrite: bool = False  # 是否覆盖已存在的标注


class BatchAnnotationResponse(BaseModel):
    """批量创建标注响应模型"""
    success_count: int
    failed_count: int
    skipped_count: int
    errors: List[str]
    created_annotations: List[AnnotationResponse]


@router.post("/annotations/batch", response_model=BatchAnnotationResponse)
async def batch_create_annotations(
    batch_request: BatchAnnotationCreate,
    db: Session = Depends(get_database)
):
    """批量创建标注"""
    try:
        stats = {
            'success_count': 0,
            'failed_count': 0,
            'skipped_count': 0,
            'errors': [],
            'created_annotations': []
        }

        # 检查数据集是否存在
        dataset = DatasetCRUD.get_by_name(db, batch_request.dataset_name)
        if not dataset:
            raise HTTPException(status_code=404, detail=f"数据集不存在: {batch_request.dataset_name}")

        for annotation_data in batch_request.annotations:
            try:
                # 设置数据集名称
                annotation_data.dataset_name = batch_request.dataset_name

                # 获取图片ID
                image_id = None
                if annotation_data.image_id:
                    image_id = annotation_data.image_id
                    image = ImageCRUD.get_by_id(db, image_id)
                    if not image:
                        stats['failed_count'] += 1
                        stats['errors'].append(f"图片不存在: {image_id}")
                        continue
                elif annotation_data.image_filename:
                    image = ImageCRUD.get_by_filename(db, dataset.id, annotation_data.image_filename)
                    if not image:
                        stats['failed_count'] += 1
                        stats['errors'].append(f"图片不存在: {annotation_data.image_filename}")
                        continue
                    image_id = image.id
                else:
                    stats['failed_count'] += 1
                    stats['errors'].append("必须提供image_id或image_filename")
                    continue

                # 检查是否已存在标注
                existing_annotations = AnnotationCRUD.get_by_image(db, image_id)
                if existing_annotations and not batch_request.overwrite:
                    stats['skipped_count'] += 1
                    continue

                # 如果需要覆盖，先删除已存在的标注
                if existing_annotations and batch_request.overwrite:
                    for existing in existing_annotations:
                        AnnotationCRUD.delete(db, existing.id)

                # 验证table_structure是否为有效JSON
                try:
                    json.loads(annotation_data.table_structure)
                except json.JSONDecodeError:
                    stats['failed_count'] += 1
                    stats['errors'].append(f"table_structure必须是有效的JSON字符串: {annotation_data.image_filename}")
                    continue

                # 创建标注数据
                new_annotation_data = {
                    "image_id": image_id,
                    "annotator": annotation_data.annotator,
                    "table_structure": annotation_data.table_structure,
                    "table_content": annotation_data.table_content,
                    "annotation_type": annotation_data.annotation_type,
                    "status": annotation_data.status
                }

                new_annotation = AnnotationCRUD.create(db, new_annotation_data)
                stats['success_count'] += 1

                # 添加到成功列表
                stats['created_annotations'].append(AnnotationResponse(
                    id=new_annotation.id,
                    image_id=new_annotation.image_id,
                    annotator=new_annotation.annotator,
                    table_structure=new_annotation.table_structure,
                    table_content=new_annotation.table_content,
                    annotation_type=new_annotation.annotation_type,
                    status=new_annotation.status,
                    created_at=new_annotation.created_at.isoformat(),
                    updated_at=new_annotation.updated_at.isoformat(),
                    image_filename=new_annotation.image.filename,
                    dataset_name=new_annotation.image.dataset.name
                ))

            except Exception as e:
                stats['failed_count'] += 1
                stats['errors'].append(f"处理标注失败: {str(e)}")

        return BatchAnnotationResponse(**stats)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量创建标注失败: {str(e)}")


@router.post("/annotations/auto-generate/{dataset_name}")
async def auto_generate_annotations(
    dataset_name: str,
    annotator: str = "auto_generator",
    overwrite: bool = False,
    db: Session = Depends(get_database)
):
    """
    为数据集自动生成标注

    从生成的HTML表格数据或现有标注数据中提取标注信息
    """
    try:
        # 检查数据集是否存在
        dataset = DatasetCRUD.get_by_name(db, dataset_name)
        if not dataset:
            raise HTTPException(status_code=404, detail=f"数据集不存在: {dataset_name}")

        # 获取数据集的图片列表
        images = ImageCRUD.get_by_dataset(db, dataset.id)
        if not images:
            raise HTTPException(status_code=404, detail=f"数据集 {dataset_name} 中没有图片")

        # 导入表格解析器
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '../../parser/src'))

        try:
            from utils.table_parser import TableDataParser
        except ImportError:
            raise HTTPException(status_code=500, detail="无法导入表格解析器，请确保parser模块可用")

        parser = TableDataParser()
        stats = {
            'success_count': 0,
            'failed_count': 0,
            'skipped_count': 0,
            'errors': []
        }

        # 动态确定生成数据目录
        # 获取项目根目录
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        gen_data_dir = os.path.join(project_root, 'dataset', dataset_name, 'gen_data')
        
        # 检查生成数据目录是否存在
        gen_data_exists = os.path.exists(gen_data_dir)
        annotations_dir = os.path.join(project_root, 'dataset', dataset_name, 'annotations')
        annotations_exists = os.path.exists(annotations_dir)
        
        if not gen_data_exists and not annotations_exists:
            raise HTTPException(status_code=400, detail=f"数据集 {dataset_name} 既没有生成数据目录也没有标注数据目录")

        for image in images:
            try:
                # 检查是否已存在标注
                existing_annotations = AnnotationCRUD.get_by_image(db, image.id)
                if existing_annotations and not overwrite:
                    stats['skipped_count'] += 1
                    continue

                # 优先尝试从标注数据中查找
                parsed_data = None
                
                # 尝试从现有标注数据中查找
                annotation_data = find_annotation_data_for_image(dataset_name, image.filename)
                if annotation_data:
                    parsed_data = parser.parse_json_schema_data(annotation_data)
                    if parsed_data:
                        # 标记为从标注数据导入
                        parsed_data['annotation_type'] = 'imported'

                # 如果没有找到标注数据，尝试从生成数据中查找
                if not parsed_data and gen_data_exists:
                    # 尝试从JSON Schema训练数据中查找
                    json_schema_data = find_json_schema_for_image(gen_data_dir, image.filename)
                    if json_schema_data:
                        parsed_data = parser.parse_json_schema_data(json_schema_data)
                        if parsed_data:
                            # 标记为JSON Schema类型
                            parsed_data['annotation_type'] = 'json_schema'

                    # 如果没有JSON Schema数据，回退到HTML解析
                    if not parsed_data:
                        parsed_data = parser.parse_generated_data(gen_data_dir, image.filename)
                        if parsed_data:
                            parsed_data['annotation_type'] = 'html_generated'

                if not parsed_data:
                    stats['failed_count'] += 1
                    stats['errors'].append(f"无法解析图片 {image.filename} 对应的表格数据")
                    continue

                # 如果需要覆盖，先删除已存在的标注
                if existing_annotations and overwrite:
                    for existing in existing_annotations:
                        AnnotationCRUD.delete(db, existing.id)

                # 创建标注
                annotation_data = {
                    "image_id": image.id,
                    "annotator": annotator,
                    "table_structure": parsed_data['table_structure'],
                    "table_content": parsed_data['table_content'],
                    "annotation_type": parsed_data.get('annotation_type', 'auto_generated'),
                    "status": "completed"
                }

                AnnotationCRUD.create(db, annotation_data)
                stats['success_count'] += 1

            except Exception as e:
                stats['failed_count'] += 1
                stats['errors'].append(f"处理图片 {image.filename} 失败: {str(e)}")

        return {
            "message": f"自动生成标注完成",
            "dataset_name": dataset_name,
            "stats": stats
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"自动生成标注失败: {str(e)}")
