# ============================================================================
# TableRAG Enhanced 配置文件
# ============================================================================
# 注意: 本项目运行在远程开发环境，所有端口都是固定的，不可随意更改
# SSH隧道设置: ssh -L 3000:localhost:3000 -L 8000:localhost:8000 -L 10000:localhost:10000 user@remote
# ============================================================================

# 服务端口配置 (远程开发环境 - 端口固定，不可更改)
# ============================================================================
export BACKEND_PORT=8000          # 后端FastAPI服务端口 (包含静态文件服务)
export FRONTEND_PORT=3000         # 前端React开发服务器端口
export MYSQL_PORT=13306           # MySQL数据库端口

# 数据库配置
# ============================================================================
export DATABASE_URL=mysql+pymysql://aidocsuser:aidocspass@localhost:13306/aidocsdb

# KS3 配置
# ============================================================================
export KS3_AK=AKLTV3HRQgmmSnm94aEKSo5A
export KS3_SK=OJx3DQrRgDULwK7VbnCg83hlPk47Jksxc9OgdE9V
export KS3_HOST=ks3-cn-beijing.ksyun.com
export KS3_BUCKET=test-platform-qa
export KS3_PROXY_HOST=127.0.0.1
export KS3_PROXY_PORT=18899

# 项目路径配置
# ============================================================================
export PROJECT_ROOT_DIR=/data/projects/kingsoft/personal/workspace/tablerag/enhance
export PYTHON_VENV_PATH=/data/deploy/pyvenvs/pyldu/bin/activate
export SSL_CERT_PATH=/etc/ssl/certs/ca-certificates.crt

# 生成数据目录配置
export GEN_DATA_DIR=/data/projects/kingsoft/personal/workspace/tablerag/enhance/dataset/test25/gen_data

# WPS配置
export WPS_AK=AK20230407UWDQSR
export WPS_SK=a9560a5967c5aaf44c9d8885abbd000c

# 代理配置
export http_proxy=http://localhost:18899
export https_proxy=http://localhost:18899

# LLM配置
export LLM_SERVICE_TYPE=custom
export CUSTOM_LLM_ENDPOINT=http://kmd-api.kas.wps.cn/api/11329-v1/HgDolg/v1/chat/completions
export CUSTOM_LLM_USER_AGENT="Apifox/1.0.0 (https://apifox.com)"
export CUSTOM_LLM_HOST=kmd-api.kas.wps.cn
export LLM_USE_PROXY=true
export LLM_MAX_TOKENS=12000
export LLM_TEMPERATURE=0.8
export LLM_TIMEOUT=60
export LLM_RETRY_COUNT=3
export LLM_RETRY_DELAY=1

# 默认测试集配置
# ============================================================================
export DATASET_NAME=kingsoft       # 默认数据集名称（命令行参数有最高优先级）

# MonkeyOCR超时配置（秒）
export MONKEY_OCR_TIMEOUT=300

# MonkeyOCR（kas） API配置
export MONKEY_OCR_KAS_API_URL=http://kmd-api.kas.wps.cn/api/11456-v1/Qa0N8c
export MONKEY_OCR_KAS_TIMEOUT=300

# Mineru 配置
export MINERU_OUTPUT_DIR=${PROJECT_ROOT_DIR}/dataset/${DATASET_NAME}/mineru

# Mineru VLM 配置
export MINERU_VLM_OUTPUT_DIR=${PROJECT_ROOT_DIR}/dataset/${DATASET_NAME}/mineru_vlm

# PDF 文件目录配置
export PDF_DIR=${PROJECT_ROOT_DIR}/dataset/${DATASET_NAME}/converted_pdfs

# 表格生成配置
export TABLE_DEFAULT_ROWS=5
export TABLE_DEFAULT_COLS=4

# DST客户端超时配置（秒）
export DST_REQUEST_TIMEOUT=180
export DST_CONNECT_TIMEOUT=60

# 日志配置
export LOG_LEVEL=DEBUG
export LOG_FILE=parse.log
export LOG_USE_COLOR=true

# 多路解析器配置
# ============================================================================
# PARSER_ENABLED_LIST: 启用的解析器列表（逗号分隔）
# 可选解析器:
#   - kdc_markdown: KDC Markdown解析器
#   - kdc_plain: KDC Plain解析器
#   - kdc_kdc: KDC KDC解析器
#   - monkey_ocr: MonkeyOCR解析器
#   - monkey_ocr_latex: MonkeyOCR LaTeX解析器
#   - vl_llm: VL-LLM解析器
#   - monkey_ocr_kas: MonkeyOCR（kas）解析器
#   - ocrflux: OCRFlux解析器
# export PARSER_ENABLED_LIST="kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,vl_llm,monkey_ocr_kas,ocrflux,mineru"
export PARSER_ENABLED_LIST="kdc_kdc,mineru,mineru_vlm"  # 单独测试某个解析器时使用


# PARSER_EXECUTION_MODE: 解析器执行模式
# parallel: 并行执行（默认，速度快）
# sequential: 串行执行（资源占用少，便于调试）
export PARSER_EXECUTION_MODE=parallel

# PARSER_SEQUENTIAL_ORDER: 串行执行时的顺序（逗号分隔）
# 注意：顺序很重要，会影响解析结果的展示顺序
export PARSER_SEQUENTIAL_ORDER="monkey_ocr_local,kdc_markdown,kdc_plain,kdc_kdc,monkey_ocr,monkey_ocr_latex,vl_llm,monkey_ocr_kas,ocrflux,mineru"

# 解析器重试配置
# PARSER_RETRY_COUNT: 解析失败时的最大重试次数（默认3次）
export PARSER_RETRY_COUNT=3

# PARSER_RETRY_DELAY: 重试间隔时间（秒，默认2秒）
export PARSER_RETRY_DELAY=2

# 表格生成配置
export TABLE_DEFAULT_ROWS=5
export TABLE_DEFAULT_COLS=4

# DST客户端超时配置（秒）
export DST_REQUEST_TIMEOUT=180
export DST_CONNECT_TIMEOUT=60

# 日志配置
export LOG_LEVEL=DEBUG
export LOG_FILE=parse.log
export LOG_USE_COLOR=true
