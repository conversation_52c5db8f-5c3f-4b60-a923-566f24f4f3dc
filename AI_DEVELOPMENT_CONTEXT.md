
### 文件访问统一化
- **🎯 统一后端**: 所有文件访问都通过backend服务 (端口8000)
- **❌ 废弃端口**: 不再使用10000端口的静态文件服务
- **📁 路径规范**: dataset/和parse_results/通过backend API访问

### Console日志优化
- **🚨 问题**: 前端页面产生几万条日志，导致console崩溃
- **✅ 解决**: 实现智能日志管理系统，默认WARN级别
- **🎛️ 控制**: 浏览器控制台运行 `setLogLevel('WARN')` 减少日志
- **🔧 调试**: 需要时运行 `setLogLevel('DEBUG')` 启用详细日志



## 🔌 固定端口配置

### 为什么端口必须固定？
1. **远程开发限制**: SSH隧道需要预先配置端口映射
2. **服务间通信**: 前后端服务需要知道确切的端口号
3. **配置一致性**: 避免每次启动都要重新配置隧道

### 端口分配表
| 服务 | 端口 | 用途 | 配置位置 |
|------|------|------|----------|
| 前端React | 3000 | 开发服务器 | analyzer/package.json |
| 后端FastAPI | 8000 | API服务 + 静态文件 | backend/main.py |
| MySQL | 13306 | 数据库 | config.env |

### 端口冲突处理
```bash
# 检查端口占用
lsof -i :3000
lsof -i :8000

# 强制杀死占用进程
kill -9 $(lsof -t -i:3000)
kill -9 $(lsof -t -i:8000)
```

## 🏗️ 项目架构概览

### 核心组件
```
TableRAG Enhanced
├── parser/              # 表格解析引擎 (Python)
├── analyzer/            # 前端分析界面 (React)
├── backend/             # 统一后端服务 (FastAPI)
├── generator/           # 数据生成器 (Python)
├── dataset/             # 测试数据集
└── parse_results/       # 解析结果存储
```

### 数据流向
```
图片数据 → Parser解析 → 结果存储 → Backend API → Frontend展示
                ↓
            人工标注 → 准确率计算 → 报告生成
```

## 🚀 启动流程

### 一键启动（推荐）
```bash
./start_tablerag.sh
```

### 手动启动顺序
```bash
# 1. 启动后端服务
cd backend
python main.py &

# 2. 启动前端服务
cd analyzer
npm start &

# 3. 验证服务状态
curl http://localhost:8000/health
curl http://localhost:3000
```

### 启动检查清单
- [ ] 后端服务响应 (http://localhost:8000/health)
- [ ] 前端服务加载 (http://localhost:3000)
- [ ] SSH隧道正常工作
- [ ] 数据库连接正常
- [ ] 静态文件访问正常

## 🔧 常见问题与解决方案

### 1. 端口被占用
```bash
# 问题: Error: listen EADDRINUSE :::3000
# 解决: 杀死占用进程
pkill -f "react-scripts"
pkill -f "npm start"
```

### 2. SSH隧道断开
```bash
# 问题: 无法访问本地端口
# 解决: 重新建立隧道
ssh -L 3000:localhost:3000 -L 8000:localhost:8000 user@remote
```

### 3. 数据库连接失败
```bash
# 问题: Can't connect to MySQL server
# 检查: 数据库服务状态
systemctl status mysql
# 检查: 端口是否正确
netstat -tlnp | grep 13306
```

### 4. 静态文件404
```bash
# 问题: 图片无法加载
# 解决: 检查后端服务状态，所有静态文件现在通过backend提供
curl http://localhost:8000/static/dataset/kingsoft/images/test.jpg
```

## 📁 重要路径配置

### 环境变量 (config.env)
```bash
# 项目根路径
PROJECT_ROOT_DIR=/data/projects/kingsoft/personal/workspace/tablerag/enhance

# 数据库连接
DATABASE_URL=mysql+pymysql://aidocsuser:aidocspass@localhost:13306/aidocsdb

# 服务端口
BACKEND_PORT=8000
FRONTEND_PORT=3000
```

### 关键目录
```bash
# 数据集目录
dataset/kingsoft/images/          # 测试图片
dataset/kingsoft/annotations/     # 人工标注

# 解析结果
parse_results/kingsoft/latest_results.json

# 前端构建
analyzer/build/                   # 生产构建输出
analyzer/src/                     # 源码目录
```

## 🔍 调试技巧

### 1. 日志查看
```bash
# 后端日志
tail -f backend/backend.log

# 前端日志
tail -f analyzer/frontend.log

# 解析器日志
tail -f parser/parse.log
```

### 2. API测试
```bash
# 测试后端API
curl http://localhost:8000/api/datasets
curl http://localhost:8000/api/parse_results/kingsoft

# 测试前端API调用
# 打开浏览器开发者工具 → Network 标签
```

### 3. 数据库调试
```bash
# 连接数据库
mysql -h localhost -P 13306 -u aidocsuser -p aidocsdb

# 查看表结构
SHOW TABLES;
DESCRIBE tablerag_annotations;
```


### 性能考虑
1. **大数据集**: kingsoft数据集较大，加载需要时间
2. **并行解析**: 默认开启并行模式，注意资源占用
3. **内存使用**: 前端虚拟滚动避免内存溢出

## 🔄 常用命令速查

### 服务管理
```bash
# 启动所有服务
./start_tablerag.sh

# 停止所有服务
pkill -f "python main.py"
pkill -f "react-scripts"

# 重启后端
cd backend && python main.py &

# 重启前端
cd analyzer && npm start &
```

### 数据处理
```bash
# 运行解析
cd parser && ./table_parser.sh kingsoft

# 查看解析进度
tail -f parse_results/kingsoft/latest_results.json

# 清理缓存
rm -rf analyzer/node_modules/.cache
```

### 测试运行
```bash
# 后端测试
cd backend && python -m pytest tests/

# 前端测试
cd analyzer && npm test

# 解析器测试
cd parser && ./unit_test.sh
```